import React from 'react';
import { connect } from 'react-redux';
import { ConnectedResourcesPageFiltersPane } from '../connectedComponents/connectedFilters';
import BasePage from './basePage';
import { PAGE_ACTIONS } from '../actions/navigateActions';
import { ConnectedModalEntityWindow, ConnectedBatchModalEntityWindow } from '../connectedComponents/connectedEntityWindow';
import { ConnectedHotKeysHelpWindow } from '../connectedComponents/connectedHotKeysHelpWindow';
import { ConnectedPromptModal } from '../connectedComponents/connectedPrompt/connectedPromptModal';
import { ConnectedDetailsPane, ConnectedPaneTabsContent } from '../connectedComponents/connectedJobDetailsPane';
import { ConnectedPlannerToasterMessage } from '../connectedComponents/connectedPlannerToasterMessage';
import { getCurrentPageTitleSelector } from '../selectors/navigationSelectors';
import { ConnectedJobFilterDialogInstance } from '../connectedComponents/connectedJobFilterDialog';
import { ResourcesPageConnectedCommandBar } from '../connectedComponents/connectedCommandBar/resourcesPageConnectedCommandBar';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';
import { ConnectedResourcesPageLayout } from '../connectedComponents/ConnectedResourcesPageLayout';
import { ConnectedResourcesGrid } from '../connectedComponents/connectedDataGrid/connectedResourcesDataGrid';

const commandBar = {
    component: ResourcesPageConnectedCommandBar,
    containerStyle: { background: 'white', display: 'flex', alignItems: 'center', padding: '0px' }
};

const dataGrid = {
    component: ConnectedResourcesGrid,
    props: {},
    containerStyle: {
        overflow: 'hidden',
        margin: '15px 15px 0 15px',
        borderRadius: '8px 8px 0 0',
        boxShadow: '0 -1px 6px rgba(0,0,0,0.08)',
        backgroundColor: '#fff'
    }
};

const filterPane = {
    component: ConnectedResourcesPageFiltersPane,
    containerClassName: 'filter-pane-container',
    props: {}
};


const detailsPane = {
    component: ConnectedDetailsPane,
    props: {
        layoutProps: {
            style: {
                background: 'white',
                overflow: 'hidden',
                position: 'fixed',
                right: '0px',
                height: '100%'
            },
            width: '550px'
        },
        classNameVisible: 'JOBS_CLEAR_SELECTION_IGNORE_CLASS display-above-ant-notification',
        classNameHidden: 'details-pane-container-hidden',
        contentComponent: ConnectedPaneTabsContent,
        contentComponentProps: {},
        prefix: 'dp'
    }
};

const connectedComponents = (
    <React.Fragment>
        <ConnectedModalEntityWindow />
        <ConnectedBatchModalEntityWindow />
        <ConnectedHotKeysHelpWindow />
        <ConnectedPromptModal pageAlias={RESOURCES_PAGE_ALIAS}/>
        <ConnectedPlannerToasterMessage />
        {ConnectedJobFilterDialogInstance}
    </React.Fragment>
);

const pageObj = { commandBar, filterPane, dataGrid, detailsPane, connectedComponents };

class ResourcesPage extends BasePage {
    constructor(props) {
        super(props);
    }

    internalRender() {
        const allProps = { ...this.props, ...pageObj };

        return <ConnectedResourcesPageLayout {...allProps} />;
    }
}

const mapStateToProps = (state) => {
    const { resourcesPage } = state.resourcesPage;
    const pageTitle = getCurrentPageTitleSelector(state)(RESOURCES_PAGE_ALIAS);

    return {
        ...resourcesPage,
        pageTitle
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onPageMount: (routerProps) => dispatch(PAGE_ACTIONS.OPEN[RESOURCES_PAGE_ALIAS](routerProps.location))
    };
};

const ConnectedResourcesPage = connect(
    mapStateToProps,
    mapDispatchToProps
)(ResourcesPage);



export default ConnectedResourcesPage;
