import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { APPLICATION_NAME, TABLE_NAMES } from '../constants/globalConsts';
import { replaceUrl } from '../actions/navigateActions';
import ConnectedJobsPage from './jobsPage';
import { getActiveListView } from '../selectors/listPageSelectors';
import ConnectedResourcesPage from './resourcesPage';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';

export default function ListPage(props) {
  const dispatch = useDispatch();
  const activeListView = useSelector(getActiveListView);
  const { pageTitle } = useSelector((state) =>
    getTranslationsSelector(state, { sectionName: LIST_PAGE_ALIAS })
  );

  useEffect(() => {
    document.title = `${APPLICATION_NAME} - ${pageTitle}`;
    dispatch(replaceUrl({}));
  }, []);

  const renderView = () => {
    switch (activeListView) {
      case TABLE_NAMES.RESOURCE: {
        return <ConnectedResourcesPage {...props} />;
      }
      default: {
        return <ConnectedJobsPage {...props} />;
      }
    }
  };

  return renderView();
}
