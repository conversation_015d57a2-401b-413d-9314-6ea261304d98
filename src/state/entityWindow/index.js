import { ENTITY_WINDOW_MODULES, <PERSON><PERSON><PERSON>_PAGE_ALIAS, PLANNER_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE, ROLE_INBOX_PAGE_ALIAS } from '../../constants';
import * as fieldsConfig from './fieldsConfig';
import * as actionTypes from '../../actions/actionTypes';
import {
    ENTITY_WINDOW_OPERATIONS,
    ENTITY_WINDOW_SECTION_KEYS,
    ENTITY_WINDOW_CUSTOM_CONTROL_TYPES,
    ENTITY_WINDOW_SECTION_TITLES,
    ENTITY_WINDOW_SECTION_TYPES,
    ENTITY_WINDOW_TAB_KEYS,
    ASSIGNEE_BUDGET_MODAL_BY_PAGE
} from '../../constants/entityWindowConsts';
import { COUNT_DISTINCTIONS, PREVIEW_ENTITY_KEY, TABLE_NAMES } from '../../constants/globalConsts';
import {
    bookingBudgetDetails,
    bookingMultipleResourcesBudgetDetails,
    criteriaRoleAssignedResourceChangeMessage,
    criteriaRoleAssignResourceMessage,
    requirementSectionInsufficientPermissionMessage,
    rolerequestCriteriaUnassignedResourceMessage,
    roleResourceWarning,
    rolerequestCriteriaDPSuggestionPaneMessageText,
    roleApplicationMessage,
    criteriaRolePublishMessage,
    boookingJobOverBudgetMessage,
    bookingResourceOverBudgetMessage
} from '../../utils/messages';
import { CREATE_FNAS_PER_TABLENAME, ROLEREQUEST_WORKFLOW_ACCESS_TYPES, MANAGE_ROLE_TEMPLATES_FNA, PUBLISH_ROLE_FNA } from '../../constants/tablesConsts';
import {
    UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY,
    BOOKING_RESOURCES_FIELD_VALUE_EXPLANATION_KEY,
    ROLEREQUEST_ROLEREQUESTGROUP_FIELD_EXPLANATION_KEY
} from '../../constants/fieldValueExplanationsConsts';
import createChangesCollection from '../../utils/changesCollectionUtils';
import { noFilesUploaded, countLimitReached, sizeLimitReached, fileTypeForbidden, serverErrorMessage } from '../../utils/messages/attachmentMessages';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { DELETE_FNAS_PER_TABLENAME } from '../../constants/tablesConsts';
import { ROLES_MODAL, ROLES_TRANSITION_ACTION, ROLE_ITEM_STATUS_KEYS } from '../../constants/rolesConsts';
import {
    roleRequestHasInvalidResourceCondition,
    cannotAssignResourceToRolerequestCondition,
    cannotUnassignResourceFromRolerequestCondition,
    cannotEditResourceViaSuggestedTabCondition,
    cannotEditRoleByNameCondition,
    cannotEditRoleByCriteriaCondition,
    cannotEditEntityCondition,
    shouldShowOverlappingBookingSectionCondition,
    shouldShowAttachmentSectionCondition,
    shouldShowCommentsSectionCondition,
    shouldShowSkillsSectionCondition,
    shouldShowRoleListSectionCondition,
    shouldShowRequirementsSectionCondition,
    shouldShowRoleGroupsSectionCondition,
    cannotRejectRequestCondition,
    cannotSaveEntityWhenFormHasError,
    shouldShowBudgetSectionCondition,
    cannotManageBudgetRequestCondition,
    noRequirementsAddedCondition,
    shouldShowWorkHistorySectionCondition,
    cannotApplyRoleCondition,
    cannotPublishCustomCondition,
    cannotWithdrawRoleCondition,
    cannotDuplicateJobCondition,
    cannotUseMoreInfoCondition,
    cannotEditRoleGroupCondition,
    shouldRenderEditBookingSeriesCondition,
    shouldShowJobReviewSectionCondition
} from '../../utils/entityWindowConditionsUtils';
import { ENTITY_ACTION_KEYS } from '../../constants/entityAccessConsts';
import { LICENSE_KEYS_ADMIN_SETTINGS } from '../../constants/globalConsts';
import { TEMPLATE_INTERPOLATION_SOURCE_NAMES } from '../../utils/translationUtils';
import { NOTIFICATIONS_PAGE_ALIAS } from '../../constants/notificationsPageConsts';
import { ROLEREQUEST_FIELDS } from '../../constants/fieldConsts';
import { ACTIONS_MENU_COMPONENTS, EXPLICIT_ASSIGNMENT_CONTROL } from '../../constants/actionsMenuConsts';
import { workHistory } from '../workHistory';
import { assigneesBudgetModalSections, criteriaBudgetSection } from './sections/criteriaRoleSections';
import { ROLEGROUP_DUPLICATE_DIALOG_ACTIONS } from '../../reducers/roleGroupDuplicateReducer/actions';
import React from 'react';
import { SettingOutlined } from '@ant-design/icons';
const { ROLES_ACTION_MAKE_LIVE, ROLES_ACTION_SUBMIT_REQUEST } = ROLES_TRANSITION_ACTION;
const { MAKE_LIVE, SUBMIT_REQUEST, PROGRESS_ROLES_DATA_ALIAS, REJECT_ROLES_DATA_ALIAS } = ROLES_MODAL;
const { licenseAttachmentsEnabled } = LICENSE_KEYS_ADMIN_SETTINGS;

const bookingTableName = TABLE_NAMES.BOOKING;
const resourceTableName = TABLE_NAMES.RESOURCE;
const jobTableName = TABLE_NAMES.JOB;
const clientTableName = TABLE_NAMES.CLIENT;
const rolerequestTableName = TABLE_NAMES.ROLEREQUEST;
const roleRequestGroupTableName = TABLE_NAMES.ROLEREQUESTGROUP;
const roleMarketplaceTableName = TABLE_NAMES.ROLEMARKETPLACE;

const titles = {
    [bookingTableName]: {
        title: '##key##bookingSectionTitle###Booking',
        titleIcon: 'booking'
    },
    [jobTableName]: {
        title: '##key##jobSectionTitle###Job',
        titleIcon: 'job'
    },
    [resourceTableName]: {
        title: fieldsConfig.resourceDescriptionField.name,
        titleIcon: 'user'
    },
    [clientTableName]: {
        title: '##key##clientSectionTitle###Client',
        titleIcon: 'client'
    },
    [rolerequestTableName]: {
        title: '##key##rolerequestSectionTitle###Requirements',
        titleIcon: 'role'
    },
    [roleRequestGroupTableName]: {
        title: 'rolerequestgroup_description',
        titleIcon: 'role-group-avatar'
    },
    [`${bookingTableName}_plural`]: {
        title: '##key##bookingSectionTitlePlural###Bookings',
        titleIcon: 'booking'
    },
    [`${jobTableName}_plural`]: {
        title: '##key##jobSectionTitlePlural###Jobs',
        titleIcon: 'job'
    },
    [`${resourceTableName}_plural`]: {
        title: '##key##resourceSectionTitlePlural###Resources',
        titleIcon: 'user'
    },
    [`${clientTableName}_plural`]: {
        title: '##key##clientSectionTitlePlural###Clients',
        titleIcon: 'client'
    },
    [`${rolerequestTableName}_plural`]: {
        title: '##key##rolerequestSectionTitlePlural###Requirements',
        titleIcon: ''
    },
    [`${roleRequestGroupTableName}_plural`]: {
        title: '##key##rolerequestGroupSectionTitlePlural###Role Groups',
        titleIcon: 'role-group-avatar'
    }
};

const moveToTitles = {
    [rolerequestTableName]: {
        title: '##key##moveToModalTitle###Move to...',
        titleIcon: null
    }
};

const moduleTitles = {
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: {
        [jobTableName]: {
            ...titles[jobTableName],
            titleIcon: '',
            subTitle: '##key##roleGroupListSectionTitle###Role groups',
            subTitleIcon: 'role-group-avatar'
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: {
        [`${jobTableName}_plural`]: {
            ...titles[`${jobTableName}_plural`],
            titleIcon: '',
            subTitle: '##key##roleGroupListSectionTitle###Role groups',
            subTitleIcon: 'role-group-avatar'
        },
        [jobTableName]: {
            ...titles[jobTableName],
            titleIcon: '',
            subTitle: '##key##roleGroupListSectionTitle###Role groups',
            subTitleIcon: 'role-group-avatar'
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {
        [`${resourceTableName}_plural`]: {
            ...titles[`${resourceTableName}_plural`],
            navigationLabel: '##key##backToSuggestionLabel###Back to suggestions'
        },
        [resourceTableName]: {
            ...titles[resourceTableName],
            navigationLabel: '##key##backToSuggestionLabel###Back to suggestions'
        }
    },
    [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {
        ...titles,
        [rolerequestTableName]: {
            ...titles[rolerequestTableName],
            titleButtonKey: PREVIEW_ENTITY_KEY,
            titleButtonIconType: 'expand'
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {
        [roleMarketplaceTableName]: {
            titleIcon: 'megaphone',
            title: '##key##publishToMarketplaceLabel###Publish to Roles board'
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {
        [roleRequestGroupTableName]: {
            titleIcon: null,
            title: '##key##createScenarioLabel###Create scenario'
        }
    },
    [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {
        [rolerequestTableName]: {
            ...titles[rolerequestTableName]
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {
        [roleMarketplaceTableName]: {
            titleIcon: 'megaphone',
            title: '##key##rolePublicationWindowTitle##Role publication'
        }
    },
    [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {
        [rolerequestTableName]: {
            title: '##key##manageMyTemplatesLabel##Manage My Templates',
            titleIcon: null
        },
        [`${rolerequestTableName}_plural`]: {
            title: '##key##manageMyTemplatesLabel##Manage My Templates',
            titleIcon: null
        }
    },
    [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {
        [rolerequestTableName]: {
            title: '##key##roleTemplateLabel##Role template',
            titleIcon: 'role'
        }
    }
};

const publishRoleModalSections = {
    [roleMarketplaceTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.PUBLISH_ROLE,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: false,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleMarketplaceCategoryField
                },
                {
                    ...fieldsConfig.roleMarketplacePublishDateField
                },
                {
                    ...fieldsConfig.roleMarketplaceCriteriaMatchField
                }
            ],
            messages: [
                {
                    id: criteriaRolePublishMessage.id
                }
            ],
            excludeSectionFields: false,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['edit', 'create', 'read']
            }
        }
    ]
};

const roleGroupModalSections = {
    [roleRequestGroupTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.CREATE_ROLE_GROUP,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestGroupField,
                    editable: true
                },
                {
                    ...fieldsConfig.roleRequestGroupJobField
                },
                {
                    ...fieldsConfig.roleRequestGroupInfoField
                }

            ],
            messages: [],
            excludeSectionFields: false,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: ['edit']
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.EDIT_ROLE_GROUP,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestGroupField,
                    editable: true
                },
                {
                    ...fieldsConfig.roleRequestGroupJobField,
                    editable: false
                },
                {
                    ...fieldsConfig.roleRequestGroupInfoField
                }
            ],
            messages: [],
            excludeSectionFields: false,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: ['create']
            }
        }
    ]
};

const simplifiedSections = {
    [bookingTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.bookingResourceSectionField,
                    showValueExplanation: true,
                    fieldMessages: [
                        {
                            id: bookingResourceOverBudgetMessage.id
                        }
                    ]
                },
                {
                    ...fieldsConfig.bookingJobSectionField
                },
                {
                    ...fieldsConfig.dateRangeSectionField,
                    fields:[]
                }
            ]
        }
    ],
    [rolerequestTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestDescription
                },
                {
                    ...fieldsConfig.roleRequestJobGroupSectionField
                },
                {
                    ...fieldsConfig.roleRequestDateRangeSectionField,
                    fields:[]
                },
                {
                    ...fieldsConfig.roleRequestResourceField
                },
                {
                    ...fieldsConfig.roleRequestStatusField
                }
            ],
            messages: [
                {
                    id: roleResourceWarning.id
                }
            ]
        }
    ]
};

const moveToSections = {
    [rolerequestTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: false,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestJobGroupSectionField
                }
            ],
            messages: []
        }
    ]
};

const roleGroupListSections = {
    [jobTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ROLEGROUP_LIST,
            title: ENTITY_WINDOW_SECTION_TITLES.ROLEGROUP_LIST,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ROLE_GROUP_LIST_SECTION_TYPE,
            shouldDisplayTitle: false,
            roleGroups: [],
            fields: [],
            requestedfields: [
                {
                    ...fieldsConfig.jobDescriptionField
                },
                {
                    ...fieldsConfig.jobIsConfidentialField
                },
                {
                    ...fieldsConfig.jobClientField
                },
                {
                    ...fieldsConfig.jobStatusField
                },
                {
                    ...fieldsConfig.jobEngagementleadField
                },
                {
                    ...fieldsConfig.jobCodeField
                },
                {
                    ...fieldsConfig.jobChargeTypeField
                },
                {
                    ...fieldsConfig.jobTotalCostField
                },
                {
                    ...fieldsConfig.jobTotalRevenueField
                },
                {
                    ...fieldsConfig.jobTotalProfitField
                },
                {
                    ...fieldsConfig.jobBudgetField
                },
                {
                    ...fieldsConfig.jobBudgetConsumedField
                },
                {
                    ...fieldsConfig.jobStartDateField
                },
                {
                    ...fieldsConfig.jobEndDateField
                },
                {
                    ...fieldsConfig.jobLocationField
                },
                {
                    ...fieldsConfig.jobOpportynity
                },
                {
                    ...fieldsConfig.jobBillabilityType
                },
                {
                    ...fieldsConfig.jobUtilisationType
                }
            ],
            excludeSectionCondition: {
                type: [shouldShowRoleGroupsSectionCondition.type]
            }
        }
    ]
};


const attachmentMessages = [
    {
        id: noFilesUploaded.id
    },
    {
        id: countLimitReached.id
    },
    {
        id: sizeLimitReached.id
    },
    {
        id: fileTypeForbidden.id
    },
    {
        id: serverErrorMessage.id
    }
];

const sections = {
    [bookingTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.bookingResourceSectionField
                },
                {
                    ...fieldsConfig.bookingResourcesSectionField
                },
                {
                    ...fieldsConfig.bookingJobSectionField
                },
                {
                    ...fieldsConfig.bookingStatusSectionField
                },
                {
                    ...fieldsConfig.dateRangeSectionField
                },
                {
                    ...fieldsConfig.timeAllocationSectionField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.OTHER_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.WORK_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.bookingWorkActivitySectionField
                },
                {
                    ...fieldsConfig.bookingNotesSectionField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BUDGET,
            title: ENTITY_WINDOW_SECTION_TITLES.BUDGET,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            showReadOnlyFields: false,
            fields: [
                // EW create and edit modes
                {
                    ...fieldsConfig.bookingChargeModeSectionField
                },
                {
                    ...fieldsConfig.bookingRevenueRatesRowField
                },
                {
                    ...fieldsConfig.bookingCostRatesRowField
                },
                {
                    ...fieldsConfig.bookingProfitRatesRowField
                },
                // EW read mode
                {
                    ...fieldsConfig.bookingReadonlyChargeRatesRowField
                },
                {
                    ...fieldsConfig.bookingTotalRevenueField
                },
                {
                    ...fieldsConfig.bookingTotalCostField
                },
                {
                    ...fieldsConfig.bookingTotalProfitField
                }
            ],
            messages: [
                {
                    id: bookingBudgetDetails.id
                },
                {
                    id : bookingMultipleResourcesBudgetDetails.id
                },
                {
                    id: boookingJobOverBudgetMessage.id
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ATTACHMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.ATTACHMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE,
            shouldDisplayTitle: true,
            fields: [],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit']
            },
            excludeSectionCondition: {
                type: [shouldShowAttachmentSectionCondition.type]
            },
            collapsed: false,
            fields: [],
            messages: attachmentMessages,
            licenseKey: licenseAttachmentsEnabled
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.COMMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.COMMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE,
            excludeSectionCondition: {
                type: [shouldShowCommentsSectionCondition.type]
            },
            shouldDisplayTitle: true,
            collapsed: false
            // commentsPageSize:
        }
    ],
    [jobTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.jobDescriptionField
                },
                {
                    ...fieldsConfig.jobIsConfidentialField
                },
                {
                    ...fieldsConfig.jobClientField
                },
                {
                    ...fieldsConfig.jobStatusField
                },
                {
                    ...fieldsConfig.jobEngagementleadField
                },
                {
                    ...fieldsConfig.jobCurrentDepartmentField
                },
                {
                    ...fieldsConfig.jobDivisionField
                },
                {
                    ...fieldsConfig.jobCodeField
                }
            ]
        },
        { // Section to be removed when BudgetHoursAndRevenue feature flag is removed.
            key: ENTITY_WINDOW_SECTION_KEYS.BUDGET_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BUDGET_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.jobChargeTypeField
                },
                {
                    ...fieldsConfig.jobBillingTypeField
                },
                {
                    ...fieldsConfig.jobTotalRevenueField
                },
                {
                    ...fieldsConfig.jobFixedPriceField
                },
                {
                    ...fieldsConfig.jobTotalCostField
                },
                {
                    ...fieldsConfig.jobTotalProfitField
                },
                {
                    ...fieldsConfig.jobBudgetField
                },
                {
                    ...fieldsConfig.jobBudgetConsumedField
                },
                {
                    ...fieldsConfig.jobProfitMarginField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.TIME_AND_FINANCIALS,
            title: ENTITY_WINDOW_SECTION_TITLES.TIME_AND_FINANCIALS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.jobChargeTypeField
                },
                {
                    ...fieldsConfig.jobBillingTypeField
                }
            ],
            subSectionItems: [
                {
                    key: ENTITY_WINDOW_SECTION_KEYS.REVENUE,
                    title: ENTITY_WINDOW_SECTION_TITLES.REVENUE,
                    sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
                    shouldDisplayTitle: true,
                    collapsed: false,
                    fields: [
                        {
                            ...fieldsConfig.jobFixedPriceField
                        },
                        {
                            ...fieldsConfig.jobTotalRevenueField
                        },
                        {
                            ...fieldsConfig.jobRevenuePercentageTargetField
                        }
                    ]
                },
                {
                    key: ENTITY_WINDOW_SECTION_KEYS.COSTS,
                    title: ENTITY_WINDOW_SECTION_TITLES.COSTS,
                    sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
                    shouldDisplayTitle: true,
                    collapsed: false,
                    fields: [
                        {
                            ...fieldsConfig.jobBudgetField
                        },
                        {
                            ...fieldsConfig.jobTotalCostField
                        },
                        {
                            ...fieldsConfig.jobBudgetConsumedField
                        }
                    ]
                },
                {
                    key: ENTITY_WINDOW_SECTION_KEYS.PROFIT,
                    title: ENTITY_WINDOW_SECTION_TITLES.PROFIT,
                    sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
                    shouldDisplayTitle: true,
                    collapsed: false,
                    fields: [
                        {
                            ...fieldsConfig.jobProfitMarginTargetField
                        },
                        {
                            ...fieldsConfig.jobProfitMarginField
                        },
                        {
                            ...fieldsConfig.jobMarginPercentageTargetField
                        }
                    ]
                },
                {
                    key: ENTITY_WINDOW_SECTION_KEYS.HOURS,
                    title: ENTITY_WINDOW_SECTION_TITLES.HOURS,
                    sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
                    shouldDisplayTitle: true,
                    collapsed: false,
                    fields: [
                        {
                            ...fieldsConfig.jobHoursBudgetField
                        },
                        {
                            ...fieldsConfig.jobTotalHoursBookedField
                        },
                        {
                            ...fieldsConfig.jobHoursPercentageBudgetField
                        }
                    ]
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.PLANNING,
            title: ENTITY_WINDOW_SECTION_TITLES.PLANNING,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.jobStartDateField
                },
                {
                    ...fieldsConfig.jobEndDateField
                },
                {
                    ...fieldsConfig.jobDiaryGroupField
                },
                {
                    ...fieldsConfig.jobLocationField
                },
                {
                    ...fieldsConfig.jobPreviousJobField
                },
                {
                    ...fieldsConfig.jobNextJobField
                },
                {
                    ...fieldsConfig.jobOpportynity
                },
                {
                    ...fieldsConfig.jobBillabilityType
                },
                {
                    ...fieldsConfig.jobUtilisationType
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.PROJECT_HEALTH,
            title: ENTITY_WINDOW_SECTION_TITLES.PROJECT_HEALTH,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: ['create'],
                [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create']
            },
            fields: [
                {
                    ...fieldsConfig.jobTimeHealthField
                },
                {
                    ...fieldsConfig.jobCostHealthField
                },
                {
                    ...fieldsConfig.jobQualityHealthField
                },
                {
                    ...fieldsConfig.jobTotalHealthField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.MILESTONES,
            title: ENTITY_WINDOW_SECTION_TITLES.MILESTONES,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.MILESTONES_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.jobMilestonesField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ATTACHMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.ATTACHMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE,
            shouldDisplayTitle: true,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: ['contextual edit']
            },
            excludeSectionCondition: {
                type: [shouldShowAttachmentSectionCondition.type]
            },
            collapsed: false,
            fields: [],
            messages: attachmentMessages,
            licenseKey: licenseAttachmentsEnabled
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.JOB_REVIEW,
            title: ENTITY_WINDOW_SECTION_TITLES.JOB_REVIEW,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.JOB_REVIEW_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            excludeSectionCondition: {
                type: [shouldShowJobReviewSectionCondition.type]
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.COMMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.COMMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE,
            excludeSectionCondition: {
                type: [shouldShowCommentsSectionCondition.type]
            },
            shouldDisplayTitle: true,
            collapsed: false
        }
    ],
    [resourceTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: ['contextual edit']

            },
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.resourceDescriptionField
                },
                {
                    ...fieldsConfig.resourceFirstNameField
                },
                {
                    ...fieldsConfig.resourceLastNameField
                },
                {
                    ...fieldsConfig.resourceJobTitleField
                },
                {
                    ...fieldsConfig.resourceLocalGradeField
                },
                {
                    ...fieldsConfig.resourceDivisionField
                },
                {
                    ...fieldsConfig.resourceCurrDeparmentField
                },
                {
                    ...fieldsConfig.resourceManagerField
                },
                {
                    ...fieldsConfig.resourceSummaryField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.RESOURCE_SUMMARY,
            title: ENTITY_WINDOW_SECTION_TITLES.RESOURCE_SUMMARY,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE,
            shouldDisplayTitle: false,
            collapsed: false,
            excludeSectionModules: Object.values(ENTITY_WINDOW_MODULES).reduce((acc, val)=> {
                if (val != ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE) {
                    acc[val] = ['read', 'edit', 'create'];
                }

                return acc;
            }, {}),
            fields: [
                {
                    ...fieldsConfig.resourceDescriptionField
                },
                {
                    ...fieldsConfig.resourceFirstNameField
                },
                {
                    ...fieldsConfig.resourceLastLogin
                },
                {
                    ...fieldsConfig.resourceLastNameField
                },
                {
                    ...fieldsConfig.resourceJobTitleField
                },
                {
                    ...fieldsConfig.resourceLocalGradeField
                },
                {
                    ...fieldsConfig.resourceDivisionField
                },
                {
                    ...fieldsConfig.resourceCurrDeparmentField
                },
                {
                    ...fieldsConfig.resourceManagerField
                },
                {
                    ...fieldsConfig.resourceAvailabilityField
                },
                {
                    ...fieldsConfig.resourceAvailableTimeField
                },
                {
                    ...fieldsConfig.resourceCMeRed
                },
                {
                    ...fieldsConfig.resourceCMeBlue
                },
                {
                    ...fieldsConfig.resourceCMeGreen
                },
                {
                    ...fieldsConfig.resourceCMeYellow
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.WORK_HISTORY,
            title: ENTITY_WINDOW_SECTION_TITLES.WORK_HISTORY,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.WORK_HISTORY_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            excludeSectionCondition: {
                type: [shouldShowWorkHistorySectionCondition.type]
            },
            excludeSectionModules: Object.values(ENTITY_WINDOW_MODULES).reduce((acc, val)=> {
                if (val != ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE
                    || val != ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE) {
                    acc[val] = ['read', 'edit', 'create'];
                }

                return acc;
            }, {})
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.OVERLAPPING_BOOKINGS,
            title: ENTITY_WINDOW_SECTION_TITLES.OVERLAPPING_BOOKINGS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.OVERLAPPING_BOOKING_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['edit', 'create'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: ['edit', 'create', 'read']
            },
            fields: [
                {
                    ...fieldsConfig.resourceOverlappingBookingField,
                    ...fieldsConfig.resourceOverlappingRolerequestField

                }
            ],
            excludeSectionFields: true,
            excludeSectionCondition: {
                type: shouldShowOverlappingBookingSectionCondition.type
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.CONTACT,
            title: ENTITY_WINDOW_SECTION_TITLES.CONTACT,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.resourceEmailField
                },
                {
                    ...fieldsConfig.resourceCellphoneField
                },
                {
                    ...fieldsConfig.resourceLocationField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.EMPLOYMENT_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.EMPLOYMENT_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.resourceChargeRateField
                },
                {
                    ...fieldsConfig.resourceDiaryGroupField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.CME,
            title: ENTITY_WINDOW_SECTION_TITLES.CME,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.CME_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.resourceCMeRed
                },
                {
                    ...fieldsConfig.resourceCMeBlue
                },
                {
                    ...fieldsConfig.resourceCMeGreen
                },
                {
                    ...fieldsConfig.resourceCMeYellow
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.SKILLS,
            title: ENTITY_WINDOW_SECTION_TITLES.SKILLS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.SKILLS_LIST_SECTION_TYPE,
            excludeSectionCondition: {
                type: [shouldShowSkillsSectionCondition.type]
            },
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [],
            skills: []
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ATTACHMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.ATTACHMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE,
            shouldDisplayTitle: true,
            fields: [],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit']
            },
            excludeSectionCondition: {
                type: [shouldShowAttachmentSectionCondition.type]
            },
            collapsed: false,
            messages: attachmentMessages,
            licenseKey: licenseAttachmentsEnabled
        }
    ],
    [clientTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.REQUIRED_INFORMATION,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.clientDescriptionField
                },
                {
                    ...fieldsConfig.clientCodeField
                },
                {
                    ...fieldsConfig.clientCurrentDepartmentField
                },
                {
                    ...fieldsConfig.clientDivisionField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ATTACHMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.ATTACHMENTS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ATTACHMENTS_SECTION_TYPE,
            excludeSectionCondition: {
                type: [shouldShowAttachmentSectionCondition.type]
            },
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [],
            messages: attachmentMessages,
            licenseKey: licenseAttachmentsEnabled
        }
    ],
    [rolerequestTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.REQUIRED_INFORMATION,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.rolerequestResourcesSectionField
                },
                {
                    ...fieldsConfig.roleRequestResourceField
                },
                {
                    ...fieldsConfig.roleRequestDescription
                },
                {
                    ...fieldsConfig.roleRequestInfoSectionField
                },
                {
                    ...fieldsConfig.roleRequestJobGroupSectionField
                }
            ],
            messages: [
                { id: roleResourceWarning.id },
                { id: criteriaRoleAssignResourceMessage.id },
                { id: criteriaRoleAssignedResourceChangeMessage.id },
                {
                    ...rolerequestCriteriaDPSuggestionPaneMessageText
                },
                { id: roleApplicationMessage.id }
            ],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read']
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.SCHEDULING,
            title: ENTITY_WINDOW_SECTION_TITLES.SCHEDULING,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestDateRangeSectionField
                },
                {
                    ...fieldsConfig.roleRequestTimeAllocationSectionField
                },
                {
                    ...fieldsConfig.criteriaRoleRequestTimeAllocationSectionField
                },
                {
                    ...fieldsConfig.roleRequestStatusField
                }
            ],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: ['read'],
                [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: ['create']
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.REQUIREMENTS,
            title: ENTITY_WINDOW_SECTION_TITLES.REQUIREMENTS_SECTION,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.REQUIREMENTS_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['edit', 'create'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['edit', 'create'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['contextual edit'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: ['edit', 'create'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: ['create'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['edit', 'create'],
                [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: ['edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: ['edit', 'create', 'read']
            },
            messages: [
                {
                    id: requirementSectionInsufficientPermissionMessage.id
                }
            ],
            excludeSectionCondition: {
                type: shouldShowRequirementsSectionCondition.type
            }
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BUDGET,
            title: ENTITY_WINDOW_SECTION_TITLES.BUDGET,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            showReadOnlyFields: false,
            fields: [
                // EW create and edit modes
                {
                    ...fieldsConfig.rolerequestChargeModeSectionField
                },
                {
                    ...fieldsConfig.rolerequestRevenueRatesRowField
                },
                {
                    ...fieldsConfig.rolerequestCostRatesRowField
                },
                {
                    ...fieldsConfig.rolerequestProfitRatesRowField
                },
                // EW read mode
                {
                    ...fieldsConfig.criteriaRolerequestReadonlyChargeRateRowFields
                },
                {
                    ...fieldsConfig.rolerequestTotalRevenueField
                },
                {
                    ...fieldsConfig.rolerequestTotalCostField
                },
                {
                    ...fieldsConfig.rolerequestTotalProfitField
                }
            ],
            messages: [
                {
                    id: rolerequestCriteriaUnassignedResourceMessage.id
                }
            ],
            excludeSectionFields: true,
            excludeSectionCondition: {
                type: shouldShowBudgetSectionCondition.type
            }
        },
        criteriaBudgetSection,
        {
            key: ENTITY_WINDOW_SECTION_KEYS.WORK_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.WORK_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestWorkActivitySectionField
                },
                {
                    ...fieldsConfig.roleRequestNotesSectionField
                }
            ],
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read'],
                [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['contextual edit', 'edit', 'create', 'read']
            }
        }
    ],
    [roleRequestGroupTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BASIC_DETAILS,
            title: ENTITY_WINDOW_SECTION_TITLES.BASIC_DETAILS,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            requestedfields: [
                {
                    ...fieldsConfig.roleRequestGroupField
                }
            ],
            fields: [
                {
                    ...fieldsConfig.roleRequestGroupField,
                    editable: true
                },
                {
                    ...fieldsConfig.roleRequestGroupInfoField
                },
                {
                    ...fieldsConfig.roleRequestGroupStartDateField
                },
                {
                    ...fieldsConfig.roleRequestGroupEndDateField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.BUDGET,
            title: ENTITY_WINDOW_SECTION_TITLES.BUDGET,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [
                {
                    ...fieldsConfig.roleRequestGroupTotalCostField
                },
                {
                    ...fieldsConfig.roleRequestGroupTotalRevenueField
                },
                {
                    ...fieldsConfig.roleRequestGroupTotalProfitField
                }
            ]
        },
        {
            key: ENTITY_WINDOW_SECTION_KEYS.ROLES,
            title: ENTITY_WINDOW_SECTION_TITLES.ROLES,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.ROLE_LIST_SECTION_TYPE,
            excludeSectionModules: {
                [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'edit', 'create', 'read']
            },
            excludeSectionCondition: {
                type: [shouldShowRoleListSectionCondition.type]
            },
            shouldDisplayTitle: true,
            collapsed: false,
            fields: [],
            roles: []
        }
    ],
    [roleMarketplaceTableName]: [
        {
            key: ENTITY_WINDOW_SECTION_KEYS.PUBLISH_ROLE,
            sectionType: ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE,
            shouldDisplayTitle: false,
            collapsed: false,
            fields: [],
            messages: [
                { id: criteriaRolePublishMessage.id }
            ]
        }
    ]
};

const additionalEntityFields = {
    [TABLE_NAMES.BOOKING]: [
        fieldsConfig.bookingDiaryDaysField
    ],
    [TABLE_NAMES.RESOURCE]: [
        fieldsConfig.resourceOverlappingBookingField,
        fieldsConfig.resourceOverlappingRolerequestField,
        fieldsConfig.resourceMaxCapableFTE,
        fieldsConfig.resourceDefaultCapableFTE,
        fieldsConfig.resourceSurrogateID
    ],
    [TABLE_NAMES.CLIENT]: [],
    [TABLE_NAMES.JOB]: [
        fieldsConfig.jobTotalHealthField
    ],
    [TABLE_NAMES.ROLEREQUEST]: [
        fieldsConfig.roleRequestDiaryDaysField,
        fieldsConfig.roleRequestGroupGuidField,
        fieldsConfig.roleRequestHasCriteriaField,
        fieldsConfig.roleRequestJobGuidField,
        fieldsConfig.rolerequestResourceTotalCost,
        fieldsConfig.rolerequestResourceTotalProfit,
        fieldsConfig.rolerequestResourceTotalRevenue,
        fieldsConfig.roleRequestFilledPercentageField,
        fieldsConfig.roleRequestHasPotentialConflictsField,
        fieldsConfig.roleRequestIsTemplateSectionField,
        fieldsConfig.roleRequestPublicationGuid,
        fieldsConfig.roleRequestSurrogateIdField,
        fieldsConfig.roleRequestContainsRequirements,
        fieldsConfig.rolerequestIsCallerApplicant,
        fieldsConfig.rolerequestCallerApplyDate,
        fieldsConfig.rolerequestDemandFulfilledField,
        fieldsConfig.rolerequestDiaryGroupSectionField,
        fieldsConfig.rolerequestChargeRateChargeModeSectionField
    ],
    [TABLE_NAMES.ROLEREQUESTGROUP]: [
        fieldsConfig.roleRequestGroupJobField
    ],
    [TABLE_NAMES.ROLEMARKETPLACE]: [
        fieldsConfig.roleMarketplaceCategoryField,
        fieldsConfig.roleMarketplacePublishDateField,
        fieldsConfig.roleMarketplaceCriteriaMatchField,
        fieldsConfig.rolerequestIsCallerApplicant,
        fieldsConfig.rolerequestCallerApplyDate,
        fieldsConfig.roleRequestPublicationGuid
    ]
};

const commonEntityWindowDeletePayloadConfig = [
    { key: 'entityId', alias: 'tableDataEntryGuid' },
    { key: 'tableName', alias: 'tableName' },
    { key: 'moduleName', alias: 'moduleName' },
    { key: 'stopEditing', alias: 'stopEditing', value: true }
];

const entityWindowRoleInboxPageDeletePayloadConfig = [
    ...commonEntityWindowDeletePayloadConfig,
    { key: 'cleanEntity', alias: 'tableData' },
    { key: 'pageAlias', alias: 'pageAlias', value: ROLE_INBOX_PAGE_ALIAS },
    { key: 'batchIds', alias: 'tableDataEntryGuids' }
];

const entityWindowPlannerPageDeletePayloadConfig = [
    ...commonEntityWindowDeletePayloadConfig,
    { key: 'cleanEntity', alias: 'tableData' },
    { key: 'pageAlias', alias: 'pageAlias', value: PLANNER_PAGE_ALIAS },
    { key: 'batchIds', alias: 'tableDataEntryGuids' }
];

const entityWindowJobPageDeletePayloadConfig = [
    ...commonEntityWindowDeletePayloadConfig,
    { key: 'pageAlias', alias: 'pageAlias', value: JOBS_PAGE_ALIAS }
];

const entityWindowRoleGroupDetailsPageDeletePayloadConfig = [
    { key: 'entityId', alias: 'tableDataEntryGuid' },
    { key: 'tableName', alias: 'tableName' },
    { key: 'pageAlias', alias: 'pageAlias', value: ROLE_GROUP_DETAILS_PAGE }
];

const entityWindowNotificationsPageDeletePayloadConfig = [
    ...commonEntityWindowDeletePayloadConfig,
    { key: 'cleanEntity', alias: 'tableData' },
    { key: 'pageAlias', alias: 'pageAlias', value: NOTIFICATIONS_PAGE_ALIAS },
    { key: 'batchIds', alias: 'tableDataEntryGuids' }
];

const getDetailsPaneBatchActions = (moduleName, singleEntityModuleName, pageAlias) => {
    return [
        {
            type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${ROLES_MODAL.MAKE_LIVE}`,
            description: '##key##makeLivePluralButtonLabel###Make all live',
            key: ENTITY_ACTION_KEYS.MAKE_LIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE,
            isBatch: true,
            settings: {
                icon: 'booking',
                type: 'tertiary',
                tables: [rolerequestTableName],
                useTemplateInterpolation: true,
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
            },
            payloadConfig: [
                { key: 'createableIds', alias: 'roleGuids' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE },
                { key: 'alias', alias: 'alias', value: PROGRESS_ROLES_DATA_ALIAS }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS]
        },
        {
            type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${ROLES_MODAL.SUBMIT_REQUEST}`,
            description: '##key##submitRequestAllButtonLabel###Submit requests',
            key: ENTITY_ACTION_KEYS.SUBMIT_REQUEST,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_SUBMIT_REQUEST,
            isBatch: true,
            settings: {
                icon: 'archive',
                type: 'tertiary',
                tables: [rolerequestTableName],
                useTemplateInterpolation: true,
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
            },
            payloadConfig: [
                { key: 'requestableIds', alias: 'roleGuids' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_SUBMIT_REQUEST },
                { key: 'alias', alias: 'alias', value: PROGRESS_ROLES_DATA_ALIAS }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS]
        },
        {
            type: actionTypes.ROLL_FORWARD_DIALOG.OPEN,
            description: '##key##duplicateLabel###Duplicate',
            key: 'rollForward',
            settings: {
                icon: 'duplicate-bar',
                type: 'secondary',
                tables: [bookingTableName]
            },
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            payloadConfig: [
                { key: 'batchIds', alias: 'entityIds' },
                { key: 'entities', alias: 'entities' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'lazyLoadEntityData', alias: 'lazyLoading', value: true }
            ],
            excludeFromPage: [ROLE_INBOX_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${moduleName}`,
            description: '##key##moreInfoButtonLabel###More info',
            key: 'moreInfo',
            entityAccess: ENTITY_ACCESS_TYPES.READ,
            settings: {
                icon: 'select',
                type: 'secondary'
            },
            payloadConfig: [
                { key: 'activeEntity', alias: 'activeEntity' },
                { key: 'batchIds', alias: 'entityIds' },
                { key: 'entities', alias: 'entities' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.READ },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: singleEntityModuleName },
                { key: 'activeTab', alias: 'activeTab' }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${moduleName}`,
            description: '##key##editButtonLabel###Edit',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            key: ENTITY_ACTION_KEYS.EDIT,
            isBatch: true,
            settings: {
                icon: 'edit',
                type: 'tertiary',
                template: 'editEntities',
                capitalized: false,
                singularForm: false,
                isEntityDependant: false
            },
            payloadConfig: [
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'editableIds', alias: 'entityIds' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'entities', alias: 'entities', value: [] },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: singleEntityModuleName }
            ],
            customHideActionConditions: [
                {
                    type: cannotEditEntityCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${moduleName}`,
            description: '##key##editRoleByNameButtonLabel###Edit Role by name',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_NAME,
            isBatch: true,
            settings: {
                icon: 'edit',
                type: 'tertiary',
                template: 'editEntities',
                capitalized: false,
                singularForm: false,
                isEntityDependant: false
            },
            payloadConfig: [
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'editableRoleByNameIds', alias: 'entityIds' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'entities', alias: 'entities', value: [] },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: singleEntityModuleName }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            customHideActionConditions: [
                {
                    type: cannotEditRoleByNameCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${moduleName}`,
            description: '##key##editRoleByCriteriaButtonLabel###Edit Role by criteria',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_CRITERIA,
            isBatch: true,
            settings: {
                icon: 'edit',
                type: 'tertiary',
                template: 'editEntities',
                capitalized: false,
                singularForm: false,
                isEntityDependant: false
            },
            payloadConfig: [
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'editableRoleByCriteriaIds', alias: 'entityIds' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'entities', alias: 'entities', value: [] },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: singleEntityModuleName }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            customHideActionConditions: [
                {
                    type: cannotEditRoleByCriteriaCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.UPDATE_ROLEREQUEST_STATUS}_${pageAlias}`,
            description: '##key##restartButtonLabel###Restart',
            key: ENTITY_ACTION_KEYS.RESTART,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_RESTART,
            isBatch: true,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'tableName', alias: 'tableName' },
                { key: 'restartableIds', alias: 'rolesIds' },
                { key: 'status', alias: 'newValue', value: ROLE_ITEM_STATUS_KEYS.DRAFT },
                { key: 'actionType', alias: 'actionType', value: 'UPDATE' },
                { key: 'customType', alias: 'customType', value: { status: ROLE_ITEM_STATUS_KEYS.DRAFT } }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS]
        },
        {
            type: `${actionTypes.UPDATE_ROLEREQUEST_STATUS}_${pageAlias}`,
            description: '##key##archiveButtonLabel###Archive',
            key: ENTITY_ACTION_KEYS.ARCHIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_ARCHIVE,
            isBatch: true,
            settings: {
                icon: 'archive',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'tableName', alias: 'tableName' },
                { key: 'archivableIds', alias: 'rolesIds' },
                { key: 'status', alias: 'newValue', value: ROLE_ITEM_STATUS_KEYS.ARCHIVED },
                { key: 'actionType', alias: 'actionType', value: 'UPDATE' },
                { key: 'customType', alias: 'customType', value: { status: ROLE_ITEM_STATUS_KEYS.ARCHIVED } }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS]
        },
        {
            type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${actionTypes.ROLE_TRANSITION_DIALOG.REJECT_REQUEST}`,
            description: '##key##rejectButtonLabel###Reject',
            key: ENTITY_ACTION_KEYS.REJECT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REJECT,
            settings: {
                icon: 'reject',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'roleGuids' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_REJECT_ROLE },
                { key: 'alias', alias: 'alias', value: REJECT_ROLES_DATA_ALIAS }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            customHideActionConditions: [
                {
                    type: cannotRejectRequestCondition.type
                }
            ]
        }
    ];
};

const commonBatchArchiveRoleRequestStatusAction = {
    description: '##key##archiveButtonLabel###Archive',
    key: ENTITY_ACTION_KEYS.ARCHIVE,
    entityAccess: ENTITY_ACCESS_TYPES.EDIT,
    workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_ARCHIVE,
    settings: {
        icon: 'archive',
        type: 'tertiary',
        tables: [rolerequestTableName]
    },
    payloadConfig: [
        { key: 'tableName', alias: 'tableName' },
        { key: 'entityId', alias: 'rolesIds' },
        { key: 'status', alias: 'newValue', value: ROLE_ITEM_STATUS_KEYS.ARCHIVED },
        { key: 'actionType', alias: 'actionType', value: 'UPDATE' },
        { key: 'customType', alias: 'customType', value: { status: ROLE_ITEM_STATUS_KEYS.ARCHIVED } }
    ]
};

const commonBatchRestartRoleRequestStatusAction = {
    description: '##key##restartButtonLabel###Restart',
    key: ENTITY_ACTION_KEYS.RESTART,
    entityAccess: ENTITY_ACCESS_TYPES.EDIT,
    workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_RESTART,
    settings: {
        icon: 'history',
        type: 'tertiary',
        tables: [rolerequestTableName]
    },
    payloadConfig: [
        { key: 'tableName', alias: 'tableName' },
        { key: 'entityId', alias: 'rolesIds' },
        { key: 'status', alias: 'newValue', value: ROLE_ITEM_STATUS_KEYS.DRAFT },
        { key: 'actionType', alias: 'actionType', value: 'UPDATE' },
        { key: 'customType', alias: 'customType', value: { status: ROLE_ITEM_STATUS_KEYS.DRAFT } }
    ]
};

const progressRolesActions = [
    {
        type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${ROLES_MODAL.MAKE_LIVE}`,
        description: '##key##makeLiveSingularButtonLabel###Make live',
        key: ENTITY_ACTION_KEYS.MAKE_LIVE,
        entityAccess: ENTITY_ACCESS_TYPES.EDIT,
        workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE,
        settings: {
            leadingTableName: rolerequestTableName,
            icon: 'booking',
            type: 'tertiary',
            tables: [rolerequestTableName, roleRequestGroupTableName],
            useTemplateInterpolation: true,
            templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
        },
        payloadConfig: [
            { key: 'entityId', alias: 'roleGuids' },
            { key: 'moduleName', alias: 'moduleName' },
            { key: 'tableName', alias: 'tableName' },
            { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE },
            { key: 'alias', alias: 'alias', value: PROGRESS_ROLES_DATA_ALIAS }
        ],
        // Added because action may be used for role pane and scenario tab
        tableDependancy: {
            [rolerequestTableName]: {
                hasExternalEntitiesDependancy: false
            },
            [roleRequestGroupTableName]: {
                hasExternalEntitiesDependancy: true,
                entityFieldMapKey: 'roleRequestId'
            }
        }
    },
    {
        type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${ROLES_MODAL.SUBMIT_REQUEST}`,
        description: '##key##submitRequestButtonLabel###Submit request',
        key: ENTITY_ACTION_KEYS.SUBMIT_REQUEST,
        entityAccess: ENTITY_ACCESS_TYPES.EDIT,
        workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_SUBMIT_REQUEST,
        settings: {
            leadingTableName: rolerequestTableName,
            icon: 'archive',
            type: 'tertiary',
            tables: [rolerequestTableName, roleRequestGroupTableName],
            useTemplateInterpolation: true,
            templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
        },
        payloadConfig: [
            { key: 'entityId', alias: 'roleGuids' },
            { key: 'moduleName', alias: 'moduleName' },
            { key: 'tableName', alias: 'tableName' },
            { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_SUBMIT_REQUEST },
            { key: 'alias', alias: 'alias', value: PROGRESS_ROLES_DATA_ALIAS }
        ],
        tableDependancy: {
            [rolerequestTableName]: {
                hasExternalEntitiesDependancy: false
            },
            [roleRequestGroupTableName]: {
                hasExternalEntitiesDependancy: true,
                entityFieldMapKey: 'roleRequestId'
            }
        }
    }
];

const getDetailsPaneActions = (targetModuleName, actionModuleName, pageAlias) => {
    return [
        ...progressRolesActions,
        {
            type: ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.OPEN,
            description: '##key##duplicateLabel###Duplicate',
            key: ENTITY_ACTION_KEYS.DUPLICATE,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            settings: {
                icon: 'duplicate-bar',
                type: 'secondary',
                tables: [roleRequestGroupTableName],
                useTemplateInterpolation: true,
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'moduleName', alias: 'moduleName' },
                { key: 'tableName', alias: 'tableName' }
            ]
        },
        {
            type: actionTypes.ROLL_FORWARD_DIALOG.OPEN,
            description: '##key##duplicateLabel###Duplicate',
            key: 'rollForward',
            settings: {
                icon: 'duplicate-bar',
                type: 'secondary',
                tables: [bookingTableName]
            },
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'lazyLoadEntityData', alias: 'lazyLoading', value: true }
            ],
            excludeFromPage: [ROLE_INBOX_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS]
        },
        {
            collectionAliased: false,
            type: actionTypes.DELETE_ROLE_GROUP_REQUEST,
            description: '##key##deleteButtonLabel###Delete',
            functionalAccess: DELETE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            entityAccess: ENTITY_ACCESS_TYPES.DELETE,
            key: 'delete',
            settings: {
                tables: [roleRequestGroupTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'pageAlias', alias: 'pageAlias' }
            ]
        },
        {
            type: actionTypes.JOB_DUPLICATE_DIALOG_ACTIONS.OPEN,
            description: '##key##duplicateLabel###Duplicate',
            key: 'rollForward',
            settings: {
                icon: 'duplicate-bar',
                type: 'secondary',
                tables: [TABLE_NAMES.JOB]
            },
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'lazyLoadEntityData', alias: 'lazyLoading', value: true }
            ],
            excludeFromPage: [ROLE_INBOX_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE],
            customHideActionConditions: [
                {
                    type: cannotDuplicateJobCondition.type
                }
            ]
        },
        {
            type: actionTypes.REDIRECT_TO_ROLEGROUP_DETAILS_PAGE,
            description: '##key##openScenarioButtonLabel###Open scenario',
            key: 'openScenario',
            settings: {
                icon: 'info',
                type: 'primary',
                useTemplateInterpolation: true,
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                tables: [roleRequestGroupTableName]
            },
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN}_${targetModuleName}`,
            description: '##key##moreInfoButtonLabel###More info',
            key: 'moreInfo',
            settings: {
                icon: 'select',
                type: 'secondary'
            },
            entityAccess: ENTITY_ACCESS_TYPES.READ,
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.READ },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: targetModuleName },
                { key: 'activeTab', alias: 'activeTab' }
            ],
            customHideActionConditions: [
                {
                    type: cannotUseMoreInfoCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL}`,
            description: '##key##editButtonLabel###Edit',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            key: ENTITY_ACTION_KEYS.EDIT,
            settings: {
                icon: 'edit',
                type: 'tertiary'
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: targetModuleName }
            ],
            customHideActionConditions: [
                {
                    type: cannotEditRoleGroupCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN}_${targetModuleName}`,
            description: '##key##editButtonLabel###Edit',
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            key: ENTITY_ACTION_KEYS.EDIT,
            settings: {
                icon: 'edit',
                type: 'tertiary'
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: targetModuleName }
            ],
            customHideActionConditions: [
                {
                    type: cannotEditResourceViaSuggestedTabCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${actionTypes.ROLE_TRANSITION_DIALOG.REJECT_REQUEST}`,
            description: '##key##rejectButtonLabel###Reject',
            key: ENTITY_ACTION_KEYS.REJECT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REJECT,
            settings: {
                icon: 'reject',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'roleGuids' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'roleTransitionActionType', alias: 'roleTransitionActionType', value: ROLES_TRANSITION_ACTION.ROLES_ACTION_REJECT_ROLE },
                { key: 'alias', alias: 'alias', value: REJECT_ROLES_DATA_ALIAS }
            ],
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS]
        },
        {
            type: `${actionTypes.UPDATE_ROLEREQUEST_STATUS}_${pageAlias}`,
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            ...commonBatchRestartRoleRequestStatusAction
        },
        {
            type: `${actionTypes.UPDATE_ROLEREQUEST_STATUS}_${pageAlias}`,
            excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE, JOBS_PAGE_ALIAS],
            ...commonBatchArchiveRoleRequestStatusAction
        },
        {
            type: `${actionTypes.ASSIGN_RESOURCE_TO_CRITERIA_ROLE}_${actionModuleName}`,
            description: '##key##assignToRoleButtonLabel###Assign to role',
            key: ENTITY_ACTION_KEYS.ASSIGN_TO_ROLE,
            componentType: EXPLICIT_ASSIGNMENT_CONTROL,
            settings: {
                type: 'primary',
                template: 'editEntities',
                isEntityDependant: true,
                capitalized: false,
                tables: [rolerequestTableName, resourceTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'resourceId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName', value: TABLE_NAMES.ROLEREQUEST },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: targetModuleName },
                { key: 'actionModuleName', alias: 'actionModuleName', value: actionModuleName },
                { key: ROLEREQUEST_FIELDS.GUID, alias: 'roleId' },
                { key: ROLEREQUEST_FIELDS.FTE, alias: 'fte' }
            ],
            customHideActionConditions: [
                {
                    type: cannotAssignResourceToRolerequestCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.UNASSIGN_RESOURCE_FROM_CRITERIA_ROLE}_${actionModuleName}`,
            description: '##key##unassignFromRoleButtonLabel###Unassign from role',
            key: ENTITY_ACTION_KEYS.UNASSIGN_FROM_ROLE,
            componentType: EXPLICIT_ASSIGNMENT_CONTROL,
            settings: {
                type: 'primary',
                template: 'editEntities',
                isEntityDependant: true,
                capitalized: false,
                tables: [rolerequestTableName, resourceTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'resourceId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName', value: TABLE_NAMES.ROLEREQUEST },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: targetModuleName },
                { key: 'actionModuleName', alias: 'actionModuleName', value: actionModuleName },
                { key: ROLEREQUEST_FIELDS.GUID, alias: 'roleId' }
            ],
            customHideActionConditions: [
                {
                    type: cannotUnassignResourceFromRolerequestCondition.type
                }
            ]
        },
        {
            type: actionTypes.OPEN_MANAGE_BUDGET_EW,
            description: '##key##manageBudgetLabel###Manage Budget',
            key: ENTITY_ACTION_KEYS.MANAGE_BUDGET,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MANAGE_BUDGET,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'roleId' },
                { key: 'entity', alias: 'roleEntity' },
                { key: 'moduleName', alias: 'moduleName', value: ASSIGNEE_BUDGET_MODAL_BY_PAGE[pageAlias] },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' }
            ],
            customHideActionConditions:[
                {
                    type: cannotManageBudgetRequestCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${pageAlias}`,
            description: '##key##movePendingFTELabel###Move Pending FTEs',
            key: ENTITY_ACTION_KEYS.MOVE_PENDING_FTEs,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MOVE_PENDING,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' }
            ]
        },
        {
            type: `${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${pageAlias}`,
            description: '##key##removePendingFTELabel###Remove Pending FTEs',
            key: ENTITY_ACTION_KEYS.REMOVE_PENDING_FTEs,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REMOVE_PENDING,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' }
            ]
        },
        {
            type: `${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${pageAlias}`,
            description: '##key##movePendingResourcesLabel###Move Pending Resources',
            key: ENTITY_ACTION_KEYS.MOVE_PENDING_RESOURCES,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MOVE_PENDING,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' }
            ]
        },
        {
            type: `${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${pageAlias}`,
            description: '##key##removePendingResourcesLabel###Remove Pending Resources',
            key: ENTITY_ACTION_KEYS.REMOVE_PENDING_RESOURCES,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REMOVE_PENDING,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' }
            ]
        },
        {
            key: ACTIONS_MENU_COMPONENTS.DIVIDER,
            settings: {
                tables: [rolerequestTableName]
            },
            componentType: ACTIONS_MENU_COMPONENTS.DIVIDER
        },
        {
            type: actionTypes.OPEN_PUBLISH_ROLE_EW,
            description: '##key##publishToMarketplaceLabel###Publish to Roles board',
            key: ENTITY_ACTION_KEYS.PUBLISH,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            functionalAccess: PUBLISH_ROLE_FNA,
            settings: {
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName],
                populatePageAlias: true
            },
            payloadConfig: [
                { key: 'entityId', alias: 'roleId' },
                { key: 'entity', alias: 'roleEntity' },
                { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL },
                { key: 'tableName', alias: 'tableName' },
                { key: 'status', alias: 'status' },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: false }
            ]
        },
        {
            type: actionTypes.OPEN_EDIT_ROLE_PUBLICATION_EW,
            description: '##key##editRolePublicationButtonLabel###Edit role publication',
            key: ENTITY_ACTION_KEYS.EDIT_ROLE_PUBLICATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            functionalAccess: PUBLISH_ROLE_FNA,
            settings: {
                useTemplateInterpolation: true,
                icon: 'history',
                type: 'tertiary',
                tables: [rolerequestTableName],
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'roleId' },
                { key: 'entity', alias: 'roleEntity' },
                { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL },
                { key: 'tableName', alias: 'tableName' },
                { key: 'pageAlias', alias: 'pageAlias', value: ROLE_INBOX_PAGE_ALIAS },
                { key: 'status', alias: 'status' },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: false }
            ]
        },
        {
            type: `${actionTypes.SAVE_AS_TEMPLATE_CREATION_MODAL.OPEN}`,
            description: '##key##saveAsTemplateLabel###Save as template',
            key: ENTITY_ACTION_KEYS.SAVE_AS_TEMPLATE,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: MANAGE_ROLE_TEMPLATES_FNA,
            settings: {
                type: 'tertiary',
                tables: [rolerequestTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' }
            ]
        }
    ];
};

const getRoleGroupDetailsPaneActions = (moduleName) => {
    return [
        ...progressRolesActions,
        {
            type: ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.OPEN,
            description: '##key##duplicateLabel###Duplicate',
            key: ENTITY_ACTION_KEYS.DUPLICATE,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            settings: {
                icon: 'duplicate-bar',
                type: 'secondary',
                tables: [roleRequestGroupTableName],
                useTemplateInterpolation: true,
                templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'moduleName', alias: 'moduleName' },
                { key: 'tableName', alias: 'tableName' }
            ]
        },
        {
            type: `${actionTypes.ENTITY_WINDOW.EDIT}_${moduleName}`,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            description: '##key##editAllButtonLabel###Edit All',
            key: ENTITY_ACTION_KEYS.EDIT_ALL,
            settings: {
                icon: 'edit',
                type: 'tertiary'
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName }
            ]
        }
    ];
};

const getRoleGroupListDetailsPaneActions = (moduleName) => {
    return [
        {
            type: `${actionTypes.VIEW_ROLE_GROUP_LIST_PAGE_REQUEST}`,
            description: '##key##compareButtonLabel###Compare',
            settings: {
                icon: 'role-group',
                type: 'secondary',
                template: 'viewEntities',
                capitalized: false,
                countDistinction: COUNT_DISTINCTIONS.PLURAL,
                isEntityDependant: true,
                tables: [roleRequestGroupTableName, jobTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'tableName', alias: 'tableName' }
            ]
        },
        {
            type: `${actionTypes.OPEN_CREATE_ROLE_GROUP}`,
            description: '##key##createButtonLabel###Create',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            settings: {
                icon: 'plus',
                type: 'primary',
                template: 'newEntities',
                capitalized: false,
                isEntityDependant: true,
                tables: [roleRequestGroupTableName, jobTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'jobId' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'entity', alias: 'entity' }
            ]
        }
    ];
};

const getRoleGroupListBatchDetailsPaneActions = (moduleName) => {
    return [
        {
            type: `${actionTypes.VIEW_ROLE_GROUP_LIST_PAGE_REQUEST}`,
            description: '##key##compareButtonLabel###Compare',
            settings: {
                icon: 'role-group',
                type: 'secondary',
                template: 'viewEntities',
                capitalized: false,
                countDistinction: COUNT_DISTINCTIONS.PLURAL,
                isEntityDependant: true,
                tables: [roleRequestGroupTableName, jobTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'tableName', alias: 'tableName' }
            ]
        },
        {
            type: `${actionTypes.OPEN_CREATE_ROLE_GROUP}`,
            description: '##key##createButtonLabel###Create',
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccess: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
            settings: {
                icon: 'plus',
                type: 'primary',
                template: 'newEntities',
                capitalized: false,
                isEntityDependant: true,
                tables: [roleRequestGroupTableName, jobTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'jobId' },
                { key: 'tableName', alias: 'tableName' },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'entity', alias: 'entity' }
            ]
        }
    ];
};

const getJobRoleResourceDetailsPaneActions = (moduleName) => {
    return [
        {
            type: `${actionTypes.ENTITY_WINDOW.OPEN}_${moduleName}`,
            description: '##key##moreInfoButtonLabel###More info',
            key: 'moreInfo',
            entityAccess: ENTITY_ACCESS_TYPES.READ,
            settings: {
                icon: 'select',
                type: 'secondary'
            },
            payloadConfig: [
                { key: 'entityId', alias: 'entityId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName' },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.READ },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'activeTab', alias: 'activeTab' }
            ]
        },
        {
            type: `${actionTypes.ASSIGN_RESOURCE_TO_CRITERIA_ROLE}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE}`,
            description: '##key##assignToRoleButtonLabel###Assign to role',
            key: ENTITY_ACTION_KEYS.ASSIGN_TO_ROLE,
            componentType: EXPLICIT_ASSIGNMENT_CONTROL,
            settings: {
                type: 'primary',
                template: 'editEntities',
                isEntityDependant: true,
                capitalized: false,
                tables: [rolerequestTableName, resourceTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'resourceId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName', value: TABLE_NAMES.ROLEREQUEST },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'actionModuleName', alias: 'actionModuleName', value: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE },
                { key: ROLEREQUEST_FIELDS.GUID, alias: 'roleId' },
                { key: ROLEREQUEST_FIELDS.FTE, alias: 'fte' }
            ],
            customHideActionConditions: [
                {
                    type: cannotAssignResourceToRolerequestCondition.type
                }
            ]
        },
        {
            type: `${actionTypes.UNASSIGN_RESOURCE_FROM_CRITERIA_ROLE}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE}`,
            description: '##key##unassignFromRoleButtonLabel###Unassign from role',
            key: ENTITY_ACTION_KEYS.UNASSIGN_FROM_ROLE,
            componentType: EXPLICIT_ASSIGNMENT_CONTROL,
            settings: {
                type: 'primary',
                template: 'editEntities',
                isEntityDependant: true,
                capitalized: false,
                tables: [rolerequestTableName, resourceTableName]
            },
            payloadConfig: [
                { key: 'entityId', alias: 'resourceId' },
                { key: 'entity', alias: 'entity', value: {} },
                { key: 'tableName', alias: 'tableName', value: TABLE_NAMES.ROLEREQUEST },
                { key: 'collectionAlias', alias: 'collectionAlias' },
                { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                { key: 'moduleName', alias: 'moduleName', value: moduleName },
                { key: 'actionModuleName', alias: 'actionModuleName', value: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE },
                { key: ROLEREQUEST_FIELDS.GUID, alias: 'roleId' }
            ],
            customHideActionConditions: [
                {
                    type: cannotUnassignResourceFromRolerequestCondition.type
                }
            ]
        }
    ];
};

// Currently, we cannot alias for both module and collection. This is probably not needed anyway, but is up for discussion nonetheless
const actions = {
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {
        create: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##submitRequestButtonLabel###Submit request',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                    appendEntityCount: true,
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'progressToRequested', alias: 'progressToRequested', value: true }
                ],
                disableConditions: [
                    {
                        type: roleRequestHasInvalidResourceCondition.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [clientTableName, jobTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.BATCH_SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [bookingTableName],
                    isEntityDependant : true,
                    template: 'createMultipleBooking',
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'batchGuids', alias: 'batchGuids' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##saveAsADraft###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'secondary',
                    tables: [rolerequestTableName],
                    isEntityDependant : true,
                    template: 'createMultipleRoles',
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'batchGuids', alias: 'batchGuids' }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ],
        edit: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary',
                    tables: [bookingTableName, jobTableName, clientTableName, resourceTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: 'ENTITY_WINDOW_SUBMIT_DELETE',
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteBooking',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [bookingTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: 'ENTITY_WINDOW_SUBMIT_DELETE',
                description: '##key##deleteButtonLabel###Delete',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [rolerequestTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: {
        create: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##addBookingLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [bookingTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##submitRequestButtonLabel###Submit request',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: ['rolerequest'],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                    appendEntityCount: false
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'progressToRequested', alias: 'progressToRequested', value: true }
                ],
                disableConditions: [
                    {
                        type: roleRequestHasInvalidResourceCondition.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##saveAsADraft###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'secondary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_REQUEST}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`,
                description: '##key##moreOptionsButtonLabel###More options',
                settings: {
                    type: 'secondary'
                },
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                payloadConfig: [
                    { key: 'entity', alias: 'entity' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.CREATE },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: false },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: {
        edit: [
            //Fix those for batch when hooking
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                isBatch:true,
                multipleDescription:'##key##saveAllButtonLabel###Save All',
                payloadConfig: [
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'patchData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            },
            {
                collectionAliased: true,
                key: ENTITY_ACTION_KEYS.DELETE,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant : true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES,
                key: ENTITY_ACTION_KEYS.DELETE,
                isBatch: true,
                description: '##key##deleteMultipleBookinngsButtonLabel###Delete',
                functionalAccess: 'DeleteBooking',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    useTemplate: true,
                    type: 'danger',
                    style: { float: 'right' },
                    template: 'deleteEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false,
                    tables: [bookingTableName]
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES,
                key: ENTITY_ACTION_KEYS.DELETE,
                isBatch: true,
                description: '##key##deleteMultipleRolerequestsButtonLabel###Delete',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    useTemplate: true,
                    type: 'danger',
                    style: { float: 'right' },
                    template: 'deleteEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false,
                    tables: [rolerequestTableName]
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'activeEntity', alias: 'activeEntity' },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditEntityCondition.type
                    }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL}`,
                description: '##key##editRoleByNameButtonLabel###Edit Role by name',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_NAME,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableRoleByNameIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditRoleByNameCondition.type
                    }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL}`,
                description: '##key##editRoleByCriteriaButtonLabel###Edit Role by criteria',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_CRITERIA,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableRoleByCriteriaIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditRoleByCriteriaCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                type: actionTypes.ROLEREQUEST_MOVE_TO,
                description: '##key##moveButtonLabel###Move',
                disabledKey: 'errors',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                type: actionTypes.ROLEREQUEST_MOVE_TO_CREATE,
                description: '##key##moveButtonLabel###Move',
                disabledKey: 'errors',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneActions(ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL, ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE, PLANNER_PAGE_ALIAS)
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneBatchActions(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL, ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL, PLANNER_PAGE_ALIAS)
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [jobTableName, resourceTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL}`,
                key: ENTITY_ACTION_KEYS.EDIT,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {
        create: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ],
        edit: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'entityId', alias: 'tableDataEntryGuid' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowJobPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteJob',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [jobTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowJobPageDeletePayloadConfig,
                excludeFromPage: [ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE]
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                functionalAccess: 'EditJob',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL, ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE)
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getRoleGroupDetailsPaneActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE)
    },
    [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneBatchActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL, ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: {
        edit: [
            //Fix those for batch when hooking
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                isBatch:true,
                multipleDescription:'##key##saveAllButtonLabel###Save All',
                payloadConfig: [
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'patchData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant : true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'activeEntity', alias: 'activeEntity' },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getRoleGroupListDetailsPaneActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE)
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getRoleGroupListBatchDetailsPaneActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE)
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneBatchActions(ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL, ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL)
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]: {
        edit: [
            //Fix those for batch when hooking
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                isBatch:true,
                multipleDescription:'##key##saveAllButtonLabel###Save All',
                payloadConfig: [
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'patchData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant : true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowPlannerPageDeletePayloadConfig
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'activeEntity', alias: 'activeEntity' },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: {
        edit: [
            {
                collectionAliased: false,
                type: `${actionTypes.ROLE_GROUP_DETAILS_PAGE_ACTIONS.SUBMIT_UPDATE}`,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'patchData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.DISCARD_BATCH_ENTITY_CHANGES,
                description: '##key##discardChangesButtonLabel###Discard changes',
                settings: {
                    type: 'link'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'batchIds', alias: 'batchIds' }
                ]
            },
            {
                collectionAliased: false,
                type: actionTypes.DELETE_ROLE_GROUP_REQUEST,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: DELETE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    type: 'danger',
                    style: { float: 'right', right: '625px' },
                    template: 'deleteEntity',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false,
                    leadingTableName: roleRequestGroupTableName,
                    tables: [roleRequestGroupTableName, rolerequestTableName]
                },
                payloadConfig: entityWindowRoleGroupDetailsPageDeletePayloadConfig
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getJobRoleResourceDetailsPaneActions(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneActions(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL, ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE, ROLE_INBOX_PAGE_ALIAS)
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT]: getDetailsPaneBatchActions(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL, ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL, ROLE_INBOX_PAGE_ALIAS)
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##submitRequestButtonLabel###Submit request',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                    appendEntityCount: false
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'progressToRequested', alias: 'progressToRequested', value: true }
                ],
                disableConditions: [
                    {
                        type: roleRequestHasInvalidResourceCondition.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##saveAsADraft###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'secondary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_REQUEST}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL}`,
                description: '##key##moreOptionsButtonLabel###More options',
                settings: {
                    type: 'secondary'
                },
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                payloadConfig: [
                    { key: 'entity', alias: 'entity' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.CREATE },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: false },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##submitRequestButtonLabel###Submit request',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                    appendEntityCount: true,
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'progressToRequested', alias: 'progressToRequested', value: true }
                ],
                disableConditions: [
                    {
                        type: roleRequestHasInvalidResourceCondition.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_ROLEREQUEST_ATTEMPT,
                description: '##key##saveAsADraft###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'secondary',
                    tables: [rolerequestTableName],
                    isEntityDependant: true,
                    template: 'createMultipleRoles',
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'batchGuids', alias: 'batchGuids' }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary',
                    tables: [jobTableName, resourceTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.DELETE,
                collectionAliased: true,
                type: 'ENTITY_WINDOW_SUBMIT_DELETE',
                description: '##key##deleteButtonLabel###Delete',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [rolerequestTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowRoleInboxPageDeletePayloadConfig
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL}`,
                key: ENTITY_ACTION_KEYS.EDIT,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_ROLEREQUEST_ATTEMPT,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [jobTableName, resourceTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL}`,
                key: ENTITY_ACTION_KEYS.EDIT,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                tables: [roleRequestGroupTableName],
                description: '##key##createScenarioLabel###Create scenario',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [roleRequestGroupTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.CREATE }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                tables: [roleRequestGroupTableName],
                description: '##key##editScenarioLabel###Edit scenario',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [roleRequestGroupTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                moduleAliased: true,
                type: actionTypes.PUBLISH_ROLE,
                description: '##key##publishRoleLabel###Publish role',
                functionalAccess: PUBLISH_ROLE_FNA,
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [roleMarketplaceTableName],
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'pageAlias', alias: 'pageAlias', value: ROLE_INBOX_PAGE_ALIAS },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'editTableName', alias: 'editTableName', value: roleMarketplaceTableName },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' }
                ],
                disableConditions: [
                    {
                        type: cannotPublishCustomCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                moduleAliased: true,
                type: actionTypes.EDIT_ROLE_PUBLICATION,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [roleMarketplaceTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'pageAlias', alias: 'pageAlias', value: ROLE_INBOX_PAGE_ALIAS },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' }
                ],
                disableConditions: [
                    {
                        type: cannotSaveEntityWhenFormHasError.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.REMOVE_ROLE_PUBLICATION,
                type: actionTypes.REMOVE_ROLE_PUBLICATION,
                description: '##key##removeRolePublicationButtonLabel###Remove role publication',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                functionalAccess: PUBLISH_ROLE_FNA,
                settings: {
                    useTemplateInterpolation: true,
                    templateInterpolationSources: [TEMPLATE_INTERPOLATION_SOURCE_NAMES.COMMON_ENTITY_ALIASES],
                    type: 'danger',
                    capitalized: false,
                    singularForm: false,
                    tables: [roleMarketplaceTableName],
                    style: { float: 'right' }
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'roleId' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'entity', alias: 'roleEntity' },
                    { key: 'pageAlias', alias: 'pageAlias', value: ROLE_INBOX_PAGE_ALIAS }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_BATCH_REQUEST,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                isBatch: true,
                multipleDescription:'##key##saveAllButtonLabel###Save All',
                payloadConfig: [
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'patchData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.DELETE,
                collectionAliased: true,
                isBatch: true,
                type: actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES,
                description: '##key##deleteButtonLabel###Delete',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: false,
                    useTemplate: true,
                    template: 'deleteEntities',
                    type: 'danger',
                    capitalized: false,
                    singularForm: false,
                    tables: [rolerequestTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowRoleInboxPageDeletePayloadConfig
            }
        ],
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL}`,
                key: ENTITY_ACTION_KEYS.EDIT,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'activeEntity', alias: 'activeEntity' },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditEntityCondition.type
                    }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL}`,
                description: '##key##editRoleByNameButtonLabel###Edit Role by name',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_NAME,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableRoleByNameIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditRoleByNameCondition.type
                    }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_FOR_MULTIPLE_REQUEST}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL}`,
                description: '##key##editRoleByCriteriaButtonLabel###Edit Role by criteria',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                key: ENTITY_ACTION_KEYS.EDIT_ROLE_BY_CRITERIA,
                isBatch: true,
                settings: {
                    icon: 'edit',
                    type: 'secondary',
                    template: 'editEntities',
                    capitalized: false,
                    singularForm: false,
                    isEntityDependant: false
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'editableRoleByCriteriaIds', alias: 'entityIds' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'entities', alias: 'entities', value: [] },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL },
                    { key: 'singleEntityModuleName', alias: 'singleEntityModuleName', value: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotEditRoleByCriteriaCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: {
        [ENTITY_WINDOW_OPERATIONS.EDIT]: [
            {
                type: actionTypes.ROLEREQUEST_MOVE_TO,
                description: '##key##moveButtonLabel###Move',
                disabledKey: 'errors',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: {
        edit: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_DELETE,
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteClient',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [clientTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowNotificationsPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: 'ENTITY_WINDOW_SUBMIT_DELETE',
                description: '##key##deleteButtonLabel###Delete',
                functionalAccess: 'DeleteBooking',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [bookingTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowNotificationsPageDeletePayloadConfig
            },
            {
                collectionAliased: true,
                type: 'ENTITY_WINDOW_SUBMIT_DELETE',
                description: '##key##deleteButtonLabel###Delete',
                entityAccess: ENTITY_ACCESS_TYPES.DELETE,
                settings: {
                    isEntityDependant: true,
                    template: 'deleteEntity',
                    type: 'danger',
                    tables: [rolerequestTableName],
                    style: { float: 'right' }
                },
                payloadConfig: entityWindowNotificationsPageDeletePayloadConfig
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: shouldRenderEditBookingSeriesCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL}`,
                description: '##key##moreInfoButtonLabel###More info',
                key: 'moreInfo',
                entityAccess: ENTITY_ACCESS_TYPES.READ,
                settings: {
                    icon: 'select',
                    type: 'secondary',
                    tables: [jobTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.READ },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL },
                    { key: 'activeTab', alias: 'activeTab' }
                ]
            },
            {
                key: `${ENTITY_ACTION_KEYS.WITHDRAW_ROLE_APPLICATION}`,
                type: actionTypes.ENTITY_WINDOW.WITHDRAW_ROLE_APPLICATION,
                description: '##key##withdrawButtonLabel###Withdraw',
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE },
                    { key: 'tableName', alias: 'tableName' }
                ],
                customHideActionConditions: [
                    {
                        type: cannotWithdrawRoleCondition.type
                    }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.APPLY_TO_ROLE,
                type: `${actionTypes.MARKETPLACE_PAGE_ROLE_APPPLY}_${ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE}`,
                description: '##key##applyButtonLabel###Apply',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'primary',
                    tables: [rolerequestTableName]
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE }
                ],
                customHideActionConditions: [
                    {
                        type: cannotApplyRoleCondition.type
                    }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##closeButtonLabel###Close',
                settings: {
                    type: 'teritary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {
        edit: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE,
                description: '##key##saveChangesButtonLabel###Save changes',
                disabledKey: 'errors', //disabled when skill form has errors
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'tableDataEntryGuid' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ],
        read: [
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL}`,
                description: '##key##editButtonLabel###Edit',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'secondary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.READ]: [
            {
                key: `${ENTITY_ACTION_KEYS.WITHDRAW_ROLE_APPLICATION}`,
                type: actionTypes.ENTITY_WINDOW.WITHDRAW_ROLE_APPLICATION,
                description: '##key##withdrawButtonLabel###Withdraw',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL },
                    { key: 'tableName', alias: 'tableName' }
                ],
                customHideActionConditions: [
                    {
                        type: cannotWithdrawRoleCondition.type
                    }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.APPLY_TO_ROLE,
                type: `${actionTypes.MARKETPLACE_PAGE_ROLE_APPPLY}_${ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL}`,
                description: '##key##applyButtonLabel###Apply',
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity', value: {} },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotApplyRoleCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##closeButtonLabel###Close',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.READ]: []
    },
    [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                type: actionTypes.INSERT_ROLE_TEMPLATE,
                description: '##key##createTemplateLabel###Create template',
                functionalAccess: MANAGE_ROLE_TEMPLATES_FNA,
                settings: { type: 'primary' },
                disabledKey: 'errors',
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'entityId', alias: 'tableDataEntryGuid' }
                ],
                disableConditions: [
                    { type: cannotSaveEntityWhenFormHasError.type },
                    { type: noRequirementsAddedCondition.type }
                ]
            },
            {
                key: ENTITY_ACTION_KEYS.APPLY_TO_ROLE,
                type: `${actionTypes.MARKETPLACE_PAGE_ROLE_APPPLY}_${ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE}`,
                description: '##key##applyButtonLabel###Apply',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                settings: {
                    type: 'primary'
                },
                payloadConfig: [
                    { key: 'entityId', alias: 'entityId' },
                    { key: 'entity', alias: 'entity' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: true },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.EDIT },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL }
                ],
                customHideActionConditions: [
                    {
                        type: cannotApplyRoleCondition.type
                    }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: {
        create: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [clientTableName, jobTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.BATCH_SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [bookingTableName],
                    isEntityDependant : true,
                    template: 'createMultipleBooking',
                    countDistinction: COUNT_DISTINCTIONS.BOTH
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true },
                    { key: 'batchGuids', alias: 'batchGuids' }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                }
            }
        ],
        edit: [],
        read: []
    },
    [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##addBookingLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [bookingTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                type: `${actionTypes.ENTITY_WINDOW.OPEN_REQUEST}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL}`,
                description: '##key##moreOptionsButtonLabel###More options',
                settings: {
                    type: 'secondary'
                },
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                payloadConfig: [
                    { key: 'entity', alias: 'entity' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'collectionAlias', alias: 'collectionAlias' },
                    { key: 'operation', alias: 'operation', value: ENTITY_WINDOW_OPERATIONS.CREATE },
                    { key: 'lazyLoadEntityData', alias: 'lazyLoadEntityData', value: false },
                    { key: 'moduleName', alias: 'moduleName', value: ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL }
                ]
            }
        ]
    },
    [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: {
        [ENTITY_WINDOW_OPERATIONS.CREATE]: [
            {
                collectionAliased: true,
                type: actionTypes.ENTITY_WINDOW.SUBMIT_INSERT,
                description: '##key##createButtonLabel###Create',
                disabledKey: 'errors',
                settings: {
                    type: 'primary',
                    tables: [clientTableName, jobTableName]
                },
                payloadConfig: [
                    { key: 'entity', alias: 'tableData' },
                    { key: 'entityPayloadAlias', alias: 'entityPayloadAlias', value: 'tableData' },
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' },
                    { key: 'stopEditing', alias: 'stopEditing', value: true }
                ]
            },
            {
                moduleAliased: true,
                type: actionTypes.ENTITY_WINDOW.CLOSE_GLOBAL_CREATE_MODAL,
                description: '##key##cancelButtonLabel###Cancel',
                settings: {
                    type: 'tertiary'
                },
                payloadConfig: [
                    { key: 'tableName', alias: 'tableName' },
                    { key: 'moduleName', alias: 'moduleName' }
                ]
            }
        ]
    }
};

export const getNewEntity = (sectionsConfig, tableName, entityAdditionalFieldsConfig) => {
    let entity = {};
    const sectionConfig = sectionsConfig[tableName];
    const additionalFieldsConfig = entityAdditionalFieldsConfig[tableName];

    const setFieldInitialValue = (field) => {
        if (!field.fields) {
            return null;
        }

        if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL) {
            entity[field.valueKey.name] = setFieldInitialValue(field.valueKey);
        } else if (field.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL) {
            entity[field.startDateField.name] = setFieldInitialValue(field.startDateField);
            entity[field.endDateField.name] = setFieldInitialValue(field.endDateField);
        }

        entity[field.name] = null;
        field.fields.forEach((field) => entity[field.name] = setFieldInitialValue(field));
    };

    sectionConfig
        .filter(section => section.sectionType === ENTITY_WINDOW_SECTION_TYPES.FIELDS_LIST_SECTION_TYPE ||
            section.sectionType === ENTITY_WINDOW_SECTION_TYPES.MILESTONES_SECTION_TYPE ||
            section.sectionType === ENTITY_WINDOW_SECTION_TYPES.TAB_SECTION_TYPE ||
            section.sectionType === ENTITY_WINDOW_SECTION_TYPES.CRITERIA_ESTIMATE_BUDGET_SECTION_TYPE ||
            section.sectionType === ENTITY_WINDOW_SECTION_TYPES.RESOURCE_SUMMARY_SECTION_TYPE ||
            section.sectionType === ENTITY_WINDOW_SECTION_TYPES.OVERLAPPING_BOOKING_SECTION_TYPE)
        .forEach((section) => {
            section.fields.forEach((field) => {
                entity[field.name] = setFieldInitialValue(field);
            });

            section.requestedfields && section.requestedfields.forEach((field) => {
                entity[field.name] = setFieldInitialValue(field);
            });

            if ((section.subSections || []).length > 1) {
                entity = { ...entity, ...getNewEntity({ [tableName]: section.subSections }, tableName, { [tableName]: [] }) };
            }

            if ((section.subSectionItems || []).length > 1) {
                entity = { ...entity, ...getNewEntity({ [tableName]: section.subSectionItems }, tableName, { [tableName]: [] }) };
            }
        });

    additionalFieldsConfig.forEach((additionalField) => {
        entity[additionalField.name] = null;
    });

    entity[`${tableName}_changedate`] = null;
    entity[`${tableName}_change_resource_guid`] = null;

    return entity;
};

const entityTemplates = {
    [bookingTableName]: getNewEntity(sections, bookingTableName, additionalEntityFields),
    [jobTableName]: getNewEntity(sections, jobTableName, additionalEntityFields),
    [resourceTableName]: getNewEntity(sections, resourceTableName, additionalEntityFields),
    [clientTableName]: getNewEntity(sections, clientTableName, additionalEntityFields),
    [rolerequestTableName]: getNewEntity(sections, rolerequestTableName, additionalEntityFields),
    [roleRequestGroupTableName]: getNewEntity(sections, roleRequestGroupTableName, additionalEntityFields),
    [roleMarketplaceTableName]: getNewEntity(sections, roleMarketplaceTableName, additionalEntityFields)
};

const bookingFieldValueExplanations = {
    [fieldsConfig.bookingStatusSectionField.name]: {
        explanations: {
            [fieldsConfig.bookingStatusSectionField.FIELD_VALUE_CONSTS.UNCONFIRMED]: '##key##bookingStatusFieldExplanation###Resource will remain available in the booked time'
        }
    },
    [fieldsConfig.bookingNonWorkSectionField.name]: {
        explanations: {
            [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: '##key##nonWorkSectionFieldExplanation###Contingency hours will be booked on the non-working days'
        }
    },
    [fieldsConfig.bookingResourcesSectionField.name]: {
        explanations: {
            [BOOKING_RESOURCES_FIELD_VALUE_EXPLANATION_KEY]: '##key##resourceSectionFieldExplanation###Adding multiple resources will create a booking for each one'
        }
    }
};

const jobFieldValueExplanations = {
    [fieldsConfig.jobIsConfidentialField.name]: {
        explanations: {
            [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: '##key##jobIsConfidentialFieldExplanation###Confidential jobs can only be seen by people who have access'
        }
    }
};

const roleRequestFieldValueExplanations = {
    [fieldsConfig.roleRequestNonWorkSectionField.name]: {
        explanations: {
            [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: '##key##nonWorkSectionFieldExplanation###Contingency hours will be booked on the non-working days'
        }
    },
    [fieldsConfig.roleRequestGroupGuidField.name]: {
        explanations: {
            [ROLEREQUEST_ROLEREQUESTGROUP_FIELD_EXPLANATION_KEY]: '##key##rolerequestRolerequestGroupFieldExplanation###Leaving this blank will create a role outside of a scenario.'
        }
    }
};

const roleMarketplaceFieldValueExplanations = {
    [fieldsConfig.roleMarketplaceCriteriaMatchField.name]: {
        explanations: {
            [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: '##key##roleMarketplaceCriteriaMatchExplanation###Applicants must match the requirement'
        }
    }
};

const tableViewBookingFieldValueExplanations = {
    ...bookingFieldValueExplanations,
    [fieldsConfig.bookingStatusSectionField.name]: {
        explanations: {
            [fieldsConfig.bookingStatusSectionField.FIELD_VALUE_CONSTS.UNCONFIRMED]: '##key##tableViewBookingStatusFieldExplanation###Unconfirmed ${bookingSingularLowerAlias} will be visible on Plans page. ${resourceSingularCapitalAlias} will remain available in booked time.'
        }
    }
};

const fieldValueExplanations = {
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: {
        [TABLE_NAMES.BOOKING]: {},
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: {},
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: {
        [TABLE_NAMES.RESOURCE]: {}
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {
        [TABLE_NAMES.RESOURCE]: {}
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: {
        [TABLE_NAMES.RESOURCE]: {}
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]: {
        [TABLE_NAMES.RESOURCE]: {}
    },
    [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {},
    [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: {},
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: {
        [TABLE_NAMES.ROLEREQUEST]: {}
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {
        [TABLE_NAMES.ROLEREQUESTGROUP]: {}
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {
        [TABLE_NAMES.ROLEMARKETPLACE]: {
            ...roleMarketplaceFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {
        [TABLE_NAMES.ROLEMARKETPLACE]: {
            ...roleMarketplaceFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        },
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...bookingFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {
            ...roleRequestFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {}
    },
    [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {
        [TABLE_NAMES.ROLEREQUEST]: {}
    },
    [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: {
        [TABLE_NAMES.BOOKING]: {
            ...tableViewBookingFieldValueExplanations
        },
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: {
        [TABLE_NAMES.BOOKING]: {}
    },
    [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    },
    [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: {
        [TABLE_NAMES.JOB]: {
            ...jobFieldValueExplanations
        }
    }
};

const getCommentsStateModel = () => {
    return {
        pagedComments: {},
        commentsChanges: createChangesCollection(
            'id',
            [],
            [],
            []
        )
    };
};

const modalWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    cleanEntity: {},
    messages: [],
    activeTab: ENTITY_WINDOW_TAB_KEYS.DETAILS,
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const simplifiedModalWindow = {
    ...modalWindow,
    prepopulatedFieldsReadOnly: true,
    dynamicLoadMandatoryFields: true
};

const batchModalWindow = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: '',
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    },
    showEditAllTab: true
};

const jobsPageModalWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const jobsPageDetailsPaneWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const jobsPageRoleGroupListDetailsPaneWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const jobsPageRoleGroupListBatchDetailsPaneWindow = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: ''
};

const resourcesPageModalWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const resourcesPageDetailsPaneWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const roleGroupDetailsPaneWindow = { ...jobsPageDetailsPaneWindow };

const detailsPaneWindow = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    messages: [],
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const plannerPageBatchDetailsPaneWindow = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: ''
};

const plannerPageAssigneeBudgetModal = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    cleanEntity: {},
    messages: [],
    activeTab: ENTITY_WINDOW_TAB_KEYS.DETAILS,
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const jobsPageBatchDetailsPaneWindow = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: ''
};

const resourcesPageBatchDetailsPaneWindow = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: ''
};

const resourceRoleFormByNameWindow = {
    isBatch: true,
    visible: false,
    loading: false,
    operation: '',
    windows: {},
    tableName: '',
    showEditAllTab: false,
    showDefaultTitle: false,
    batchIds: [],
    showTitleCounter: false,
    showPluralForm: false
};

const roleInboxBatchDetailsPane = {
    isBatch: true,
    visible: false,
    operation: '',
    windows: {},
    tableName: ''
};

const roleInboxBatchModal = {
    isBatch: true,
    visible: false,
    collectionAlias: '',
    operation: '',
    windows: {},
    tableName: '',
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    },
    showEditAllTab: true
};

const roleInboxAssigneeBudgetModal = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    cleanEntity: {},
    messages: [],
    activeTab: ENTITY_WINDOW_TAB_KEYS.DETAILS,
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const roleGroupModal = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    cleanEntity: {},
    messages: [],
    activeTab: ENTITY_WINDOW_TAB_KEYS.DETAILS,
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};

const roleInboxPublishRoleModal = {
    visible: false,
    loading: false,
    table: null,
    collectionAlias: '',
    operation: '',
    entityId: null,
    entity: {},
    uiEntity: {},
    cleanEntity: {},
    messages: [],
    activeTab: ENTITY_WINDOW_TAB_KEYS.DETAILS,
    jumpTo: {
        visible: false,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    }
};


const manageRoleTemplatesModal = {
    isBatch: true,
    visible: false,
    showTitleCounter: false,
    operation: '',
    windows: {},
    tableName: '',
    jumpTo: {
        visible: true,
        title: '##key##jumpToSectionTitle###Jump To',
        style: 'entityDetailsWindowJumpToNav',
        span: 7
    },
    showEditAllTab: false
};

export const sectionsTableNames = Object.keys(sections);

export default {
    settings: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]
            },
            sections: {
                ...simplifiedSections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]
            },
            sections: {
                ...moveToSections
            },
            titles: {
                ...moveToTitles
            },
            entityTemplates: {
                ...entityTemplates
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    fieldsConfig.rolerequestIsCallerApplicant,
                    fieldsConfig.rolerequestCallerApplyDate
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]
            },
            sections: {
                ...assigneesBudgetModalSections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    ROLEREQUEST_FIELDS.CHARGEMODE
                ]
            },
            skipSystemSections: true
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            },
            additionalEntityFieldsByTableName: {
                [TABLE_NAMES.RESOURCE]: [
                    {
                        ...fieldsConfig.resourceAvailabilityField
                    },
                    {
                        ...fieldsConfig.resourceAvailableTimeField
                    }
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            },
            additionalEntityFieldsByTableName: {
                [TABLE_NAMES.RESOURCE]: [
                    {
                        ...fieldsConfig.resourceAvailabilityField
                    },
                    {
                        ...fieldsConfig.resourceAvailableTimeField
                    }
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]
            },
            sections: {
                ...roleGroupListSections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    fieldsConfig.rolerequestIsCallerApplicant,
                    fieldsConfig.rolerequestCallerApplyDate
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]
            },
            sections: {
                ...roleGroupListSections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    fieldsConfig.rolerequestIsCallerApplicant,
                    fieldsConfig.rolerequestCallerApplyDate
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]
            },
            sections: {
                ...simplifiedSections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]
            },
            sections: {
                ...assigneesBudgetModalSections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    fieldsConfig.rolerequestChargeModeField,
                    fieldsConfig.roleRequestStartSectionField,
                    fieldsConfig.roleRequestEndSectionField,
                    fieldsConfig.roleRequestFixedTimeSectionField
                ]
            },
            skipSystemSections: true
        },
        [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]
            },
            sections: {
                ...roleGroupModalSections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]
            },
            entityTemplates: {
                ...entityTemplates
            },
            skipSystemSections: false,
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]
            },
            sections: {
                ...publishRoleModalSections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]
            },
            entityTemplates: {
                ...entityTemplates
            },
            skipSystemSections: true,
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]
            },
            sections: {
                ...publishRoleModalSections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]
            },
            entityTemplates: {
                ...entityTemplates
            },
            skipSystemSections: true,
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]
            },
            sections: {
                ...moveToSections
            },
            titles: {
                ...moveToTitles
            },
            entityTemplates: {
                ...entityTemplates
            }
        },
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]
            },
            sections: {
                ...sections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields,
                [TABLE_NAMES.ROLEREQUEST]: [
                    ...additionalEntityFields[TABLE_NAMES.ROLEREQUEST],
                    fieldsConfig.rolerequestIsCallerApplicant,
                    fieldsConfig.rolerequestCallerApplyDate
                ]
            }
        },
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...moduleTitles[ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]
            },
            sections: { ...sections },
            titles: { ...moduleTitles[ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL] },
            entityTemplates: { ...entityTemplates },
            additionalEntityFields: { ...additionalEntityFields }
        },
        [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]
            },
            sections: { ...sections },
            titles: { ...moduleTitles[ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL] },
            entityTemplates: { ...entityTemplates },
            additionalEntityFields: { ...additionalEntityFields }
        },
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        },
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]
            },
            sections: {
                ...simplifiedSections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            }
        },
        [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: {
            actions: { ...actions[ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL] },
            sections: { ...sections },
            titles: { ...titles },
            entityTemplates: { ...entityTemplates },
            additionalEntityFields: { ...additionalEntityFields }
        },
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: {
            actions: {
                ...actions[ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]
            },
            sections: {
                ...sections
            },
            titles: {
                ...titles
            },
            entityTemplates: {
                ...entityTemplates
            },
            additionalEntityFields: {
                ...additionalEntityFields
            }
        }
    },
    window: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: {
            ...simplifiedModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]: {
            ...simplifiedModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT,
            showDefaultTitle: true
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: {
            ...batchModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {
            ...detailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: {
            ...plannerPageBatchDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: {
            ...plannerPageAssigneeBudgetModal,
            moduleName: ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {
            ...jobsPageModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: {
            ...jobsPageDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
            ...roleGroupDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: {
            ...jobsPageBatchDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: {
            ...batchModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: {
            ...resourcesPageModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {
            ...resourcesPageDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: {
            ...resourcesPageBatchDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]: {
            ...batchModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL
        },
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: {
            ...resourceRoleFormByNameWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: {
            ...jobsPageRoleGroupListDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: {
            ...jobsPageRoleGroupListBatchDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {
            ...detailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: {
            ...detailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
            hideFooter: true
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {
            ...detailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: {
            ...roleInboxBatchDetailsPane,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: {
            ...roleInboxBatchModal,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: {
            ...simplifiedModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: {
            ...roleInboxAssigneeBudgetModal,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL
        },
        [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {
            ...roleGroupModal,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL,
            showDefaultTitle: false
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {
            ...roleInboxPublishRoleModal,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
            showDefaultTitle: false,
            populatePageAlias: true
        },
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {
            ...roleInboxPublishRoleModal,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL
        },
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: {
            ...simplifiedModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
            showDefaultTitle: true
        },
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {
            ...detailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE
        },
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL
        },
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {
            ...manageRoleTemplatesModal,
            moduleName: ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL
        },
        [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL
        },
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL
        },
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: {
            ...simplifiedModalWindow,
            moduleName: ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED
        },
        [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL
        },
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: {
            ...modalWindow,
            moduleName: ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL
        },
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {
            ...resourcesPageDetailsPaneWindow,
            moduleName: ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE
        }
    },
    autoComplete: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: {},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE] :{},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL] :{},
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE] :{},
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL] :{},
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: {},
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: {},
        [ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: {},
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: {},
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: {},
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: {},
        [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: {},
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED]: {},
        [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: {},
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: {}
    },
    fieldValueExplanations: {
        ...fieldValueExplanations
    },
    comments: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: getCommentsStateModel(),
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: getCommentsStateModel()
    },
    workHistory: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: workHistory[ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: workHistory[ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: workHistory[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: workHistory[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: workHistory[ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: workHistory[ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: workHistory[ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: workHistory[ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]
    },
    fieldValueMessages: {}
};