import { ENTITY_WINDOW_MODULES } from '../../constants';
import { FILTER_FIELD_NAMES, TABLE_NAMES } from '../../constants/globalConsts';
import { ENTITY_WINDOW_CUSTOM_CONTROL_TYPES } from '../../constants/entityWindowConsts';
import {
    CONTROL_FIELD_TYPES,
    CUSTOM_READONLY_FIELD_TYPES,
    CONTROL_FIELD_DISPLAY_TYPES,
    BOOKING_RESOURCE_GUIDS,
    BOOKING_PROFITPERHOUR_FIELDNAME,
    CHARGERATE_CURRENT_VALUE_FIELDNAME,
    ROLEREQUEST_PROFITPERHOUR_FIELDNAME,
    BOOKING_FIXEDTIME_VALUES,
    ROLEREQUEST_FIXEDTIME_VALUES,
    BOOKING_REVENUE_RATES_FIELDS_ROW,
    BOOKING_COST_RATES_FIELDS_ROW,
    BOOKING_PROFIT_RATES_FIELDS_ROW,
    ROLEREQUEST_PROFIT_RATES_FIELDS_ROW,
    R<PERSON><PERSON>EQUEST_COST_RATES_FIELDS_ROW,
    ROLEREQUEST_REVENUE_RATES_FIELDS_ROW,
    ROLEREQUEST_ROLEGROUP_SELECTION_FIELD,
    ROLEREQUESTGROUP_FIELDS,
    ROLEREQUEST_FIELDS,
    FIELD_DATA_TYPES,
    ROLEREQUESTRESOURCE_FIELDS,
    ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,
    ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD,
    ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME,
    ROLEREQUEST_ESTIMATE_REVENUE_RATES_FIELDS_ROW,
    ROLEREQUEST_ESTIMATE_COST_RATES_FIELDS_ROW,
    ROLEREQUEST_ESTIMATE_PROFIT_RATES_FIELDS_ROW,
    JOB_TIME_RAGHEALTH_GUID,
    JOB_COST_RAGHEALTH_GUID,
    JOB_QUALITY_RAGHEALTH_GUID,
    JOB_TOTAL_RAGHEALTH_GUID
} from '../../constants/fieldConsts';
import { resourceUserStatus } from '../../constants/tablesConsts';
import { BOOKING_RESOURCES_FIELD_VALUE_EXPLANATION_KEY, ROLEREQUEST_ROLEREQUESTGROUP_FIELD_EXPLANATION_KEY, ROLEREQUEST_FTE_FIELD_EXPLANATION_KEY } from '../../constants/fieldValueExplanationsConsts';
import { RESOURCE_FTE_CONSTS, ROLE_ENTITY_TYPES } from '../../constants/rolesConsts';

const bookingTableName = TABLE_NAMES.BOOKING;
const resourceTableName = TABLE_NAMES.RESOURCE;
const jobTableName = TABLE_NAMES.JOB;
const clientTableName = TABLE_NAMES.CLIENT;
const rolerequestTableName = TABLE_NAMES.ROLEREQUEST;
const roleRequestGroupTableName = TABLE_NAMES.ROLEREQUESTGROUP;
const roleShortlistTableName = TABLE_NAMES.ROLESHORTLIST;
const roleMarketplaceTableName = TABLE_NAMES.ROLEMARKETPLACE;

export const rolerequestIsCallerApplicant = {
    editable: false,
    table: TABLE_NAMES.ROLEREQUEST,
    name: 'rolerequest_is_caller_applicant'
};

export const rolerequestCallerApplyDate = {
    editable: false,
    table: TABLE_NAMES.ROLEREQUEST,
    name: 'rolerequest_caller_apply_date'
};

const lookupFilterLines = [
    {
        'field': resourceUserStatus.fieldName,
        'operator': 'Equals',
        'value': true
    }
];

//BOOKING FIELDS
export const bookingStartSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_start`
};

export const bookingEndSectionField = {
    editable: true,
    showValueExplanation: true,
    table: bookingTableName,
    name: `${bookingTableName}_end`
};

export const bookingResourceSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_resource_guid`,
    lookupFilterLines,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create'],
        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: ['contextual edit', 'read', 'edit', 'create']
    }
};

export const bookingResourcesSectionField = {
    editable: true,
    table: bookingTableName,
    linkTable: resourceTableName,
    dataType: FIELD_DATA_TYPES.ID,
    actualFieldName: `${bookingTableName}_resource_guid`,
    showValueExplanation: true,
    explanationValueKey: BOOKING_RESOURCES_FIELD_VALUE_EXPLANATION_KEY,
    name: BOOKING_RESOURCE_GUIDS,
    label: 'Resource',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL,
    lookupFilterLines,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['edit', 'read']
    }
};

export const bookingJobSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_job_guid`
};

export const bookingStatusSectionField = {
    editable: true,
    showValueExplanation: true,
    FIELD_VALUE_CONSTS: {
        UNCONFIRMED: 'Unconfirmed'
    },
    table: bookingTableName,
    name: `${bookingTableName}_bookingtype_guid`
};

export const bookingFixedHoursSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_fixedtime`
};

export const bookingNotesSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_notes`
};

export const bookingTrueLoadingSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_true_loading`
};

export const bookingLoadingSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_loading`
};

export const bookingTimeSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_time`
};

export const bookingFixedTimeSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_fixedtime`
};

export const bookingHoursPerDaySectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_hours_per_day`
};

export const bookingNonWorkSectionField = {
    editable: true,
    showValueExplanation: true,
    table: bookingTableName,
    name: `${bookingTableName}_nonwork`
};

export const bookingWorkActivitySectionField = {
    editable: true,
    showValueExplanation: true,
    table: bookingTableName,
    name: `${bookingTableName}_workactivity_guid`
};

export const timeAllocationSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_time_allocation`,
    readOnlyExplanation: true,
    label: '##key##timeAllocationTitle###Time allocation',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL,
    mandatory: true,
    leadingFieldName: bookingFixedTimeSectionField.name,
    valueKey: { defaultValue: 0, name: bookingFixedTimeSectionField.name, table: bookingTableName, editable: true, suffix: '##key##fixedTimeSectionSuffix###hrs', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_VALUE },
    fields: [
        { name: bookingLoadingSectionField.name, table: bookingTableName, editable: true, suffix: '##key##loadingSectionSuffix###% of working hours', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 0 },
        { name: bookingTimeSectionField.name, table: bookingTableName, editable: true, suffix: '##key##timeSectionSuffix###hours booked in total', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 1 },
        { name: bookingHoursPerDaySectionField.name, table: bookingTableName, editable: true, suffix: '##key##hoursPerDaySuffix###hours per day', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 2 }
    ],
    multifield: true,
    dataFields: [bookingLoadingSectionField.name, bookingTimeSectionField.name, bookingHoursPerDaySectionField.name],
    className: 'timeAllocationField'
};

export const dateRangeSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_date_range`,
    label: '##key##dateRangeLabel###Date range',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL,
    mandatory: true,
    leadingFieldName: bookingStartSectionField.name,
    showValueExplanation: true,
    startDateField: { name: bookingStartSectionField.name, table: bookingTableName, editable: true, display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.DATE_RANGE },
    endDateField: { name: bookingEndSectionField.name, table: bookingTableName, editable: true, display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.DATE_RANGE },
    fields: [
        {
            name: bookingNonWorkSectionField.name,
            table: bookingTableName,
            editable: true,
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            fieldText: '##key##nonWorkSectionFieldText###Include non-working days',
            type: CONTROL_FIELD_TYPES.ADDITIONAL_FIELD,
            showLabel: false,
            inside: true,
            showValueExplanation: true
        }
    ],
    multifield: true,
    dataFields: [bookingStartSectionField.name, bookingEndSectionField.name],
    className: 'dateRange'
};

export const bookingChargeModeField = {
    editable: false,
    table: bookingTableName,
    name: `${bookingTableName}_chargemode`,
    mappedDisplayValues: [
        { value: 0, displayValue: '##key##bookingOwnResourceChargeModeLabel###Resource charge rate' },
        { value: 1, displayValue: '##key##bookingDifferentResourceChargeModeLabel###Different charge rate' },
        { value: 2, displayValue: '##key##bookingCustomChargeModeLabel###Custom rate' }
    ],
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'create']
    }
};

export const rolerequestChargeModeField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_chargemode`,
    mappedDisplayValues: [
        { value: 0, displayValue: '##key##rolerequestOwnResourceChargeModeLabel###Resource charge rate' },
        { value: 1, displayValue: '##key##rolerequestDifferentResourceChargeModeLabel###Different charge rate' },
        { value: 2, displayValue: '##key##rolerequestCustomChargeModeLabel###Custom rate' }
    ],
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'create']
    }
};

export const rolerequestEstimateChargeModeField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ESTIMATE_CHARGEMODE,
    mappedDisplayValues: [
        { value: 1, displayValue: '##key##rolerequestEstimateBudgetLabel###Estimate budget' },
        { value: 2, displayValue: '' }
    ],
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'create']
    }
};

export const resourceChargeRateCurrentValueField = {
    editable: false,
    table: resourceTableName,
    name: CHARGERATE_CURRENT_VALUE_FIELDNAME,
};

export const bookingResourceChargeRateCurrentValueField = {
    ...resourceChargeRateCurrentValueField,
    requestFieldName: `${bookingTableName}_resource_guid.chargerate_current_value`
};

export const rolerequestResourceChargeRateCurrentValueField = {
    ...resourceChargeRateCurrentValueField,
    requestFieldName: `${rolerequestTableName}_resource_guid.chargerate_current_value`
};

export const bookingOverriddenChargeRateField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_overridden_chargerate_guid`
};

export const rolerequestOverriddenChargeRateField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_overridden_chargerate_guid`
};

//dummy field with no control
export const bookingCustomChargeRateField = {
    editable: false,
    table: bookingTableName,
    name: 'booking_custom_charge_rate_dummy_field'
};

export const rolerequestCustomChargeRateField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_custom_charge_rate_dummy_field`
};

export const bookingRevenuePerHourField = {
    editable: false,
    table: bookingTableName,
    editableWhen: { fieldName: bookingChargeModeField.name, value: 2 },
    name: `${bookingTableName}_revenueperhour`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['read', 'edit', 'contextual edit']
    }
};

export const rolerequestRevenuePerHourField = {
    editable: false,
    table: rolerequestTableName,
    editableWhen: { fieldName: rolerequestChargeModeField.name, value: 2 },
    name: `${rolerequestTableName}_revenueperhour`
};

export const rolerequestEstimateRevenuePerHourField = {
    editable: false,
    table: rolerequestTableName,
    editableWhen: { fieldName: rolerequestEstimateChargeModeField.name, value: 2 },
    name: ROLEREQUEST_FIELDS.ESTIMATE_REVENUEPERHOUR,
    readOnlyModuleNames: [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
    ]
};

export const bookingCostPerHourField = {
    editable: false,
    table: bookingTableName,
    editableWhen: { fieldName: bookingChargeModeField.name, value: 2 },
    name: `${bookingTableName}_costperhour`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['read', 'edit', 'contextual edit']
    }
};

export const rolerequestCostPerHourField = {
    editable: false,
    table: rolerequestTableName,
    editableWhen: { fieldName: rolerequestChargeModeField.name, value: 2 },
    name: `${rolerequestTableName}_costperhour`
};

export const rolerequestEstimateCostPerHourField = {
    editable: false,
    table: rolerequestTableName,
    editableWhen: { fieldName: rolerequestEstimateChargeModeField.name, value: 2 },
    name: ROLEREQUEST_FIELDS.ESTIMATE_COSTPERHOUR,
    readOnlyModuleNames: [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
    ]
};

export const rolerequestDemandFulfilledField = {
    name: ROLEREQUEST_FIELDS.DEMAND_FULFILLED,
    editable: false,
    table: rolerequestTableName
};

// Get value from booking_revenueperhour - booking_costperhour
export const bookingProfitPerHourField = { // App field - driven epics to subtract and get value
    editable: false,
    table: bookingTableName,
    name: BOOKING_PROFITPERHOUR_FIELDNAME,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['read', 'edit', 'contextual edit']
    }
};

// Get value from rolerequest_revenueperhour - rolerequest_costperhour
export const rolerequestProfitPerHourField = { // App field - driven epics to subtract and get value
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_PROFITPERHOUR_FIELDNAME
};

export const rolerequestEstimateProfitPerHourField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME
};

export const bookingTotalRevenueField = {
    editable: false,
    table: bookingTableName,
    name: `${bookingTableName}_totalrevenue`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit']
    },
    showOnReadOnlyMode: true
};

export const rolerequestTotalRevenueField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_totalrevenue`,
    showOnReadOnlyMode: true
};

export const rolerequestEstimateTotalRevenueField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE,
    showOnReadOnlyMode: true
};

export const bookingTotalCostField = {
    editable: false,
    table: bookingTableName,
    name: `${bookingTableName}_totalcost`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit']
    },
    showOnReadOnlyMode: true
};

export const rolerequestTotalCostField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_totalcost`,
    showOnReadOnlyMode: true
};

export const rolerequestEstimateTotalCostField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ESTIMATE_TOTALCOST,
    showOnReadOnlyMode: true
};

export const bookingTotalProfitField = {
    editable: false,
    table: bookingTableName,
    name: `${bookingTableName}_totalprofit`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit']
    },
    showOnReadOnlyMode: true
};

export const rolerequestTotalProfitField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_totalprofit`,
    showOnReadOnlyMode: true
};

export const rolerequestEstimateTotalProfitField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ESTIMATE_TOTALPROFIT,
    showOnReadOnlyMode: true
};

export const rolerequestResourceTotalCost = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALCOST
};

export const rolerequestResourceTotalRevenue = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALREVENUE
};

export const rolerequestResourceTotalProfit = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.ROLEREQUESTRESOURCE_TOTALPROFIT
};

export const rolerequestCreatedOnField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.CREATEON,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['create', 'edit', 'read']
    }
};

export const rolerequestDescriptionField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.DESCRIPTION
};

export const bookingChargeModeSectionField = {
    editable: true,
    table: bookingTableName,
    name: `${bookingTableName}_chargemode_section_field`,
    label: '##key##chargeRateFieldsControlTitle###Charge rate',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL,
    mandatory: false,
    leadingFieldName: bookingChargeModeField.name,
    valueKey: {
        defaultValue: 0,
        name: bookingChargeModeField.name,
        table: bookingTableName,
        editable: true,
        type: CONTROL_FIELD_TYPES.RADIO_VALUE
    },
    fields: [
        {
            ...bookingResourceChargeRateCurrentValueField,
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            mandatory: false,
            alwaysShowControl: true,
            label: '##key##bookingResourceChargeRateLabel###Resource charge rate',
            key: 0
        },
        {
            ...bookingOverriddenChargeRateField,
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            mandatory: true,
            hideWhenNotSelected: true,
            className: 'blockRadioOption',
            label: '##key##bookingOverriddenChargeRateLabel###Use different charge rate',
            key: 1
        },
        {
            ...bookingCustomChargeRateField,
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            mandatory: false,
            withoutControl: true,
            label: '##key##bookingCustomChargeRateLabel###Use custom rate',
            key: 2
        }
    ],
    multifield: true,
    dependantFields: [
        bookingRevenuePerHourField,
        bookingTotalRevenueField,
        bookingCostPerHourField,
        bookingTotalCostField,
        bookingProfitPerHourField,
        bookingTotalProfitField
    ],
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['read']
    },
    className: 'chargeRateField'
};

const commonRolerequestChargeModeSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_chargemode_section_field`,
    label: '##key##chargeRateFieldsControlTitle###Charge rate',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL,
    mandatory: false,
    leadingFieldName: rolerequestChargeModeField.name,
    valueKey: {
        defaultValue: 0,
        name: rolerequestChargeModeField.name,
        table: rolerequestTableName,
        editable: true,
        type: CONTROL_FIELD_TYPES.RADIO_VALUE
    },
    multifield: true,
    className: 'chargeRateField'
};

const commonResourceChargeRateSectionField = {
    ...rolerequestResourceChargeRateCurrentValueField,
    display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
    type: CONTROL_FIELD_TYPES.RADIO_OPTION,
    mandatory: false,
    alwaysShowControl: true,
    label: '##key##rolerequestResourceChargeRateLabel###Resource charge rate',
    key: 0
};

const commonDifferentChargeRateSectionField = {
    ...rolerequestOverriddenChargeRateField,
    display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
    className: 'blockRadioOption',
    type: CONTROL_FIELD_TYPES.RADIO_OPTION,
    mandatory: true,
    hideWhenNotSelected: true,
    label: '##key##rolerequestOverriddenChargeRateLabel###Use different charge rate',
    key: 1
};

const commonCustomChargeRateSectionField = {
    ...rolerequestCustomChargeRateField,
    display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
    type: CONTROL_FIELD_TYPES.RADIO_OPTION,
    mandatory: false,
    withoutControl: true,
    label: '##key##rolerequestCustomChargeRateLabel###Use custom rate',
    key: 2
};

export const rolerequestChargeModeSectionField = {
    ...commonRolerequestChargeModeSectionField,
    fields: [
        commonResourceChargeRateSectionField,
        commonDifferentChargeRateSectionField,
        commonCustomChargeRateSectionField
    ],
    dependantFields: [
        rolerequestRevenuePerHourField,
        rolerequestTotalRevenueField,
        rolerequestCostPerHourField,
        rolerequestTotalCostField,
        rolerequestProfitPerHourField,
        rolerequestTotalProfitField
    ]
};

export const criteriaRoleChargeModeSectionField = {
    ...commonRolerequestChargeModeSectionField,
    fields: [
        commonResourceChargeRateSectionField,
        {
            ...commonDifferentChargeRateSectionField,
            withoutControl: true,
            mandatory: false
        },
        commonCustomChargeRateSectionField
    ]
};

export const rolerequestDiaryGroupSectionField = {
    name: ROLEREQUEST_FIELDS.DIARY_GROUP,
    editable: false,
    table: TABLE_NAMES.ROLEREQUEST,
    display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
    mandatory: false,
    label: '##key##rolerequestDiaryForEstimationLabel###Diary for estimation'
};

export const rolerequestChargeRateChargeModeSectionField = {
    name: ROLEREQUEST_FIELDS.CHARGE_RATE_GUID,
    editable: true,
    table: TABLE_NAMES.ROLEREQUEST,
    className: 'blockRadioOption',
    display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
    type: CONTROL_FIELD_TYPES.RADIO_OPTION,
    mandatory: true,
    alwaysShowControl: true,
    label: '##key##selectChargeRateLabel###Select a charge rate',
    key: 1,
    isLinked: true,
    readOnlyModuleNames: [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
    ]
};

export const criteriaRoleEstimatesChargeModeSectionField = {
    ...commonRolerequestChargeModeSectionField,
    name: ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD,
    label: '##key##estimatedBudgetLabel###Estimated budget',
    leadingFieldName: rolerequestEstimateChargeModeField.name,
    valueKey: {
        defaultValue: 1,
        name: rolerequestEstimateChargeModeField.name,
        table: rolerequestTableName,
        editable: true,
        type: CONTROL_FIELD_TYPES.RADIO_VALUE
    },
    fields: [
        rolerequestChargeRateChargeModeSectionField,
        {
            ...commonCustomChargeRateSectionField,
            label: '##key##customChargeRateLabel###Custom charge rate'
        }
    ],
    dependantFields: [
        rolerequestEstimateRevenuePerHourField,
        rolerequestEstimateCostPerHourField,
        rolerequestEstimateProfitPerHourField,
        rolerequestEstimateTotalCostField,
        rolerequestEstimateTotalRevenueField,
        rolerequestEstimateTotalProfitField
    ],
    readOnlyModuleNames: [
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
        ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
    ]
};

export const bookingRevenueRatesRowField = {
    editable: false,
    table: bookingTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: BOOKING_REVENUE_RATES_FIELDS_ROW,
    actualFieldName: `${bookingTableName}_totalrevenue`,
    label: '##key##bookingRevenueRatesRowTitle###Revenue',
    fields: [
        bookingRevenuePerHourField,
        bookingTotalRevenueField
    ],
    multifield: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['read']
    }
};

const commonRolerequestRevenueRatesRowField = {
    editable: false,
    table: rolerequestTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: ROLEREQUEST_REVENUE_RATES_FIELDS_ROW,
    label: '##key##rolerequestRevenueRatesRowTitle###Revenue',
    multifield: true,
    className: 'customRateRoleRequest'
};

export const rolerequestRevenueRatesRowField = {
    ...commonRolerequestRevenueRatesRowField,
    actualFieldName: `${rolerequestTableName}_totalrevenue`,
    fields: [
        rolerequestRevenuePerHourField,
        rolerequestTotalRevenueField
    ]
};

export const rolerequestEstimateRevenueRatesRowField = {
    ...commonRolerequestRevenueRatesRowField,
    name: ROLEREQUEST_ESTIMATE_REVENUE_RATES_FIELDS_ROW,
    actualFieldName: ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE,
    fields: [
        rolerequestEstimateRevenuePerHourField,
        rolerequestEstimateTotalRevenueField
    ]
};

export const bookingCostRatesRowField = {
    editable: false,
    table: bookingTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: BOOKING_COST_RATES_FIELDS_ROW,
    actualFieldName: `${bookingTableName}_totalcost`,
    label: '##key##bookingCostRatesRowTitle###Cost',
    fields: [
        bookingCostPerHourField,
        bookingTotalCostField
    ],
    multifield: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['read']
    }
};

export const commonRolerequestCostRatesRowField = {
    editable: false,
    table: rolerequestTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    multifield: true,
    className: 'customRateRoleRequest',
    label: '##key##rolerequestCostRatesRowTitle###Cost',
    name: ROLEREQUEST_COST_RATES_FIELDS_ROW
};

export const rolerequestCostRatesRowField = {
    ...commonRolerequestCostRatesRowField,
    actualFieldName: `${rolerequestTableName}_totalcost`,
    fields: [
        rolerequestCostPerHourField,
        rolerequestTotalCostField
    ]
};

export const rolerequestEstimateCostRatesRowField = {
    ...commonRolerequestCostRatesRowField,
    name: ROLEREQUEST_ESTIMATE_COST_RATES_FIELDS_ROW,
    actualFieldName: ROLEREQUEST_FIELDS.ESTIMATE_TOTALCOST,
    fields: [
        rolerequestEstimateCostPerHourField,
        rolerequestEstimateTotalCostField
    ]
};

export const bookingProfitRatesRowField = {
    editable: false,
    table: bookingTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: BOOKING_PROFIT_RATES_FIELDS_ROW,
    actualFieldName: `${bookingTableName}_totalprofit`,
    label: '##key##bookingProfitRatesRowTitle###Profit',
    fields: [
        bookingProfitPerHourField,
        bookingTotalProfitField
    ],
    multifield: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['read']
    }
};

const commonRolerequestProfitRatesRowField = {
    editable: false,
    table: rolerequestTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: ROLEREQUEST_PROFIT_RATES_FIELDS_ROW,
    label: '##key##rolerequestProfitRatesRowTitle###Profit',
    multifield: true
};

export const rolerequestProfitRatesRowField = {
    ...commonRolerequestProfitRatesRowField,
    actualFieldName: `${rolerequestTableName}_totalprofit`,
    fields: [
        rolerequestProfitPerHourField,
        rolerequestTotalProfitField
    ]
};

export const rolerequestEstimateProfitRatesRowField = {
    ...commonRolerequestProfitRatesRowField,
    name: ROLEREQUEST_ESTIMATE_PROFIT_RATES_FIELDS_ROW,
    actualFieldName: ROLEREQUEST_FIELDS.ESTIMATE_TOTALPROFIT,
    fields: [
        rolerequestEstimateProfitPerHourField,
        rolerequestEstimateTotalProfitField
    ]
};

export const bookingReadonlyChargeRatesRowField = {
    editable: false,
    table: bookingTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: 'Booking charge rates view mode fields row',
    label: '##key##bookingViewModeChargeRatesTitle###Rates',
    fields: [
        bookingChargeModeField,
        {
            ...bookingResourceChargeRateCurrentValueField,
            showWhen: { fieldName: bookingChargeModeField.name, value: 0 }
        },
        {
            ...bookingOverriddenChargeRateField,
            showWhen: { fieldName: bookingChargeModeField.name, value: 1 },
            editable: false
        }
    ],
    multifield: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit']
    },
    showOnReadOnlyMode: true
};

export const rolerequestReadonlyChargeRatesRowField = {
    editable: false,
    table: rolerequestTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: 'Booking charge rates view mode fields row',
    label: '##key##bookingViewModeChargeRatesTitle###Rates',
    fields: [
        rolerequestChargeModeField,
        {
            ...rolerequestResourceChargeRateCurrentValueField,
            showWhen: { fieldName: rolerequestChargeModeField.name, value: 0 }
        },
        {
            ...rolerequestOverriddenChargeRateField,
            showWhen: { fieldName: rolerequestChargeModeField.name, value: 1 },
            editable: false
        }
    ],
    multifield: true,
    showOnReadOnlyMode: true
};

export const criteriaRolerequestReadonlyChargeRateRowFields = {
    editable: false,
    table: rolerequestTableName,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL,
    name: 'Booking charge rates view mode fields row',
    label: '##key##bookingViewModeChargeRatesTitle###Rates',
    fields: [
        rolerequestChargeModeField,
        {
            ...rolerequestResourceChargeRateCurrentValueField,
            showWhen: { fieldName: rolerequestChargeModeField.name, value: 0 }
        }
    ],
    multifield: true,
    showOnReadOnlyMode: true
};

export const bookingDiaryDaysField = {
    editable: false,
    table: bookingTableName,
    name: `${bookingTableName}_diary_days`
};

//JOB FIELDS
export const jobDescriptionField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_description`
};

export const jobIsConfidentialField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_isconfidential`,
    showValueExplanation: true
};

export const jobCodeField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_code`
};

export const jobClientField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_client_guid`
};

export const jobEngagementleadField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_engagementlead_resource_guid`
};

export const jobStatusField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_jobstatus_guid`
};

export const jobDiaryGroupField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_diarygroup_guid`
};

export const jobMilestonesField = {
    editable: true,
    table: jobTableName,
    name: 'job_milestones',
    withoutLabel: true,
    hideLabelWhenBlank: true
};

export const jobStartDateField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_start`
};

export const jobEndDateField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_end`
};

export const jobLocationField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_location_guid`
};

export const jobPreviousJobField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_previous_job_guid`
};

export const jobNextJobField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_next_job_guid`
};

export const jobCurrentDepartmentField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_current_department_guid`
};

export const jobDivisionField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_division_guid`
};

export const jobBillabilityType = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_isExcludeFromBillability`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'read', 'edit']
    }
};

export const jobOpportynity = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_opportunity_percent`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'read', 'edit']
    }
};

export const jobUtilisationType = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_isExcludeFromUtilisation`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: ['contextual edit', 'read'],
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['contextual edit', 'read', 'edit']
    }
};

export const jobChargeTypeField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_chargetype_guid`
};

export const jobBudgetField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_budget`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.CURRENCY_CONTROL
};

export const jobFixedPriceField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_fixedprice`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.CURRENCY_CONTROL
};

export const jobRevenuePercentageTargetField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_revenuepercentagetarget`,
};

export const jobTotalRevenueField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_totalrevenue`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.READONLY_CURRENCY_CONTROL,
    caption: {
        dependant: jobFixedPriceField.name,
        percentage: jobRevenuePercentageTargetField.name,
        translationSection: 'target'
    },
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create']
    }
};

export const jobBudgetConsumedField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_budgetconsumed`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit']
    }
};

export const jobTotalCostField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_totalcost`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.READONLY_CURRENCY_CONTROL,
    caption: {
        dependant: jobBudgetField.name,
        percentage: jobBudgetConsumedField.name,
        translationSection: 'budget'
    },
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create']
    }
};

export const jobTotalProfitField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_totalprofit`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.READONLY_CURRENCY_CONTROL,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit']
    }
};

export const jobBillingTypeField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_billingtype_guid`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.RADIO_LOOKUP_CONTROL
};

export const jobProfitMarginTargetField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_profitmargintarget`
};

export const jobMarginPercentageTargetField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_marginpercentagetarget`,
};

export const jobProfitMarginField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_profitmargin`,
    caption: {
        dependant: jobProfitMarginTargetField.name,
        percentage: jobMarginPercentageTargetField.name,
        translationSection: 'target'
    },
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create']
    }
};

export const jobHoursBudgetField = {
    editable: true,
    table: jobTableName,
    name: `${jobTableName}_hoursbudget`,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.CURRENCY_CONTROL
};

export const jobHoursPercentageBudgetField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_hourspercentagebudget`,
};

export const jobTotalHoursBookedField = {
    editable: false,
    table: jobTableName,
    name: `${jobTableName}_totalhoursbooked`,
    caption: {
        dependant: jobHoursBudgetField.name,
        percentage: jobHoursPercentageBudgetField.name,
        translationSection: 'budget'
    },
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create']
    }
};

export const jobTimeHealthField = {
    editable: true,
    table: jobTableName,
    name: JOB_TIME_RAGHEALTH_GUID,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.HEALTH_CONTROL
};

export const jobCostHealthField = {
    editable: true,
    table: jobTableName,
    name: JOB_COST_RAGHEALTH_GUID,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.HEALTH_CONTROL
};

export const jobQualityHealthField = {
    editable: true,
    table: jobTableName,
    name: JOB_QUALITY_RAGHEALTH_GUID,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.HEALTH_CONTROL
};

export const jobTotalHealthField = {
    editable: false,
    table: jobTableName,
    name: JOB_TOTAL_RAGHEALTH_GUID,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.HEALTH_CONTROL
};

//RESOURCE FIELDS
export const resourceDescriptionField = {
    editable: false,
    table: resourceTableName,
    name: `${resourceTableName}_description`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['create', 'edit', 'read'],
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['create', 'edit', 'read']
    }
};

export const resourceFirstNameField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_firstname`
};

// Added a new field to make the summary visible in the edit window.
export const resourceSummaryField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_summary`
};

export const resourceLastLogin = {
    editable: false,
    table: resourceTableName,
    name: `${resourceTableName}_last_login`
};

export const resourceLastNameField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_lastname`
};

export const resourceJobTitleField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_rolename`
};

export const resourceLocalGradeField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_localgrade_guid`
};


export const resourceDivisionField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_division_guid`
};

export const resourceCurrDeparmentField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_current_department_guid`
};

export const resourceEmailField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_email`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['edit'],
        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['edit']
    }
};

export const resourceManagerField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_manager_resource_guid`
};

export const resourceLocationField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_location_guid`
};

export const resourceCellphoneField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_cellphone`
};

export const resourceDiaryGroupField = {
    editable: true,
    table: resourceTableName,
    name: 'Diary Group',
    fna: 'diaries'
};

export const resourceCMeRed = {
    editable: false,
    table: resourceTableName,
    name: 'resource_cmered',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit'],
    }
};

export const resourceCMeBlue = {
    editable: false,
    table: resourceTableName,
    name: 'resource_cmeblue',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit'],
    }
};

export const resourceCMeGreen = {
    editable: false,
    table: resourceTableName,
    name: 'resource_cmegreen',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit'],
    }
};

export const resourceCMeYellow = {
    editable: false,
    table: resourceTableName,
    name: 'resource_cmeyellow',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit'],
    }
};

export const resourceChargeRateField = {
    editable: true,
    table: resourceTableName,
    name: 'Charge Rate'
};

export const resourceDiaryDaysField = {
    editable: true,
    table: resourceTableName,
    name: `${resourceTableName}_diary_days`
};

export const resourceIDField = {
    editable: false,
    table: resourceTableName,
    name: `${resourceTableName}_guid`
};

export const resourceAvailabilityField = {
    editable: true,
    table: resourceTableName,
    name: 'availability'
};

export const resourceAvailableTimeField = {
    editable: true,
    table: resourceTableName,
    name: 'available_time'
};


export const resourceOverlappingBookingField = {
    editable: false,
    table: resourceTableName,
    name: `${resourceTableName}_overlapping_bookings`
};

export const resourceOverlappingRolerequestField = {
    editable: false,
    table: resourceTableName,
    name: `${resourceTableName}_overlapping_rolerequests`
};

export const roleShortlistGuid = {
    editable: false,
    table: roleShortlistTableName,
    name: `${roleShortlistTableName}_guid`
};

export const resourceDefaultCapableFTE = {
    editable: false,
    table: resourceTableName,
    name: RESOURCE_FTE_CONSTS.DEFAULT_FTE_VALUE
};

export const resourceSurrogateID = {
    editable: false,
    table: resourceTableName,
    name: FILTER_FIELD_NAMES.RESOURCE_SURROGATE_ID
};

export const resourceMaxCapableFTE = {
    editable: false,
    table: resourceTableName,
    name: RESOURCE_FTE_CONSTS.MAX_FTE_VALUE
};

//CLIENT FIELDS

export const clientDescriptionField = {
    editable: true,
    table: clientTableName,
    name: `${clientTableName}_description`
};

export const clientCodeField = {
    editable: true,
    table: clientTableName,
    name: `${clientTableName}_code`
};

export const clientCurrentDepartmentField = {
    editable: true,
    table: clientTableName,
    name: `${clientTableName}_current_department_guid`
};

export const clientDivisionField = {
    editable: true,
    table: clientTableName,
    name: `${clientTableName}_division_guid`
};

//ROLE MARKETPLACE FIELDS
export const roleMarketplaceCategoryField = {
    editable: true,
    table: roleMarketplaceTableName,
    name: `${roleMarketplaceTableName}_rolecategory_guid`,
    placeholder: '##key##roleMarketplaceCategoryPlaceholder###Add category'
};

export const roleMarketplacePublishDateField = {
    editable: true,
    table: roleMarketplaceTableName,
    name: `${roleMarketplaceTableName}_publishedon`,
    mandatory: true
};

export const roleMarketplaceCriteriaMatchField = {
    editable: true,
    table: roleMarketplaceTableName,
    name: `${roleMarketplaceTableName}_criteriamatch`,
    showValueExplanation: true
};

// ROLE REQUEST GROUP FIELDS

export const roleRequestGroupInfoField = {
    editable: true,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_info`
};

export const roleRequestGroupStartDateField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_roles_start`
};

export const roleRequestGroupEndDateField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_roles_end`
};

export const roleRequestGroupTotalCostField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_totalcost`
};

export const roleRequestGroupTotalRevenueField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_totalrevenue`
};

export const roleRequestGroupTotalProfitField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_totalprofit`
};

export const roleRequestGroupField = {
    editable: false,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_description`
};

export const roleRequestGroupJobField = {
    editable: true,
    table: roleRequestGroupTableName,
    name: `${roleRequestGroupTableName}_${jobTableName}_guid`
};

//RESORCE ROLE FORM FIELDS
export const roleRequestResourceField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_resource_guid`,
    lookupFilterLines,
    editableWhen: { fieldName: ROLEREQUEST_FIELDS.HASCRITERIA, value: false },
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['create'],
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['read', 'contextual edit']
    }
};

export const roleRequestGroupGuidField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_${roleRequestGroupTableName}_guid`,
    showValueExplanation: true,
    explanationValueKey: ROLEREQUEST_ROLEREQUESTGROUP_FIELD_EXPLANATION_KEY,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['edit', 'create', 'read']
    }
};

export const roleRequestJobGuidField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_${jobTableName}_guid`,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['edit', 'create', 'read']
    }
};

export const roleRequestFilledPercentageField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.FILLED_PERCENTAGE
    //Add excludeModules if it needs in the future
};

export const roleRequestHasPotentialConflictsField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.HAS_POTENTIAL_CONFLICTS
};

export const roleRequestContainsRequirements = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.CONTAINS_REQUIREMENTS
};

export const roleRequestJobGroupSectionField = {
    editable: true,
    className: 'dependantListControl',
    table: rolerequestTableName,
    name: ROLEREQUEST_ROLEGROUP_SELECTION_FIELD,
    leadingFieldName: roleRequestJobGuidField.name,
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DEPENDANT_LIST_CONTROL,
    fields: [
        {
            ...roleRequestJobGuidField,
            className: 'primaryField'
        },
        {
            ...roleRequestGroupGuidField,
            dependancyField: roleRequestJobGuidField.name,
            fieldFilter: {
                name: roleRequestJobGuidField.name,
                actualFieldName: ROLEREQUESTGROUP_FIELDS.JOB_GUID,
                operator: 'Contains'
            }
        }
    ],
    multifield: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['edit', 'create', 'read'],
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: ['read'],
        [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: ['create']
    }
};

export const roleRequestDescription = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_description`,
    placeholder: '##key##rolerequestDescriptionPlaceholder###e.g, Project Manager',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['contextual edit', 'read']
    }
};

export const roleRequestStatusField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_rolerequeststatus_guid`,
    showValueExplanation: true,
    hasCustomReadonlyComponent: CUSTOM_READONLY_FIELD_TYPES.TAG,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['read', 'contextual edit']
    }
};

export const roleRequestWorkActivitySectionField = {
    editable: true,
    showValueExplanation: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_workactivity_guid`
};

export const roleRequestNotesSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_notes`
};

export const roleRequestHasCriteriaField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_hascriteria`
};

export const roleRequestStartSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_start`
};

export const roleRequestEndSectionField = {
    editable: true,
    showValueExplanation: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_end`
};

export const roleRequestNonWorkSectionField = {
    editable: true,
    showValueExplanation: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_nonwork`
};

export const roleRequestDateRangeSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_date_range`,
    label: '##key##datesRequiredLabel###Dates required',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL,
    mandatory: true,
    leadingFieldName: roleRequestStartSectionField.name,
    showValueExplanation: true,
    startDateField: { name: roleRequestStartSectionField.name, table: rolerequestTableName, editable: true, display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.DATE_RANGE },
    endDateField: { name: roleRequestEndSectionField.name, table: rolerequestTableName, editable: true, display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.DATE_RANGE },
    fields: [
        {
            name: roleRequestNonWorkSectionField.name,
            table: rolerequestTableName,
            editable: true,
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            fieldText: '##key##nonWorkSectionFieldText###Include non-working days',
            type: CONTROL_FIELD_TYPES.ADDITIONAL_FIELD,
            showLabel: false,
            inside: true,
            showValueExplanation: true
        }
    ],
    multifield: true,
    dataFields: [roleRequestStartSectionField.name, roleRequestEndSectionField.name],
    className: 'dateRange'
};

export const roleRequestFixedTimeSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_fixedtime`
};

export const roleRequestLoadingSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_loading`
};

export const roleRequestTimeSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_time`
};

export const roleRequestHoursPerDaySectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_hours_per_day`
};

export const roleRequestFullTimeEquivalentField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_fte`
};

export const roleRequestResourceDemandCount = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_resourcedemand_count`
};

export const roleRequestResourceDemandCountRadioOption = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_resourcedemand_count_radio_option`
};

export const roleRequestTimeAllocationSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_time_allocation`,
    readOnlyExplanation: true,
    label: '##key##timeAllocationTitle###Time allocation',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL,
    mandatory: true,
    leadingFieldName: roleRequestFixedTimeSectionField.name,
    valueKey: { defaultValue: 0, name: roleRequestFixedTimeSectionField.name, table: rolerequestTableName, editable: true, suffix: '##key##fixedTimeSectionSuffix###hrs', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_VALUE },
    fields: [
        { name: roleRequestLoadingSectionField.name, table: rolerequestTableName, editable: true, suffix: '##key##loadingSectionSuffix###% of working hours', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 0 },
        { name: roleRequestTimeSectionField.name, table: rolerequestTableName, editable: true, suffix: '##key##hoursInTotalSuffix###hours in total', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 1 },
        { name: roleRequestHoursPerDaySectionField.name, table: rolerequestTableName, editable: true, suffix: '##key##hoursPerDaySuffix###hours per day', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 2 },
        { name: roleRequestFullTimeEquivalentField.name, table: rolerequestTableName, isHidden: true, editable: true, prefix: '##key##FTESufix###FTE', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 3, showValueExplanation: true, explanationValueKey: ROLEREQUEST_FTE_FIELD_EXPLANATION_KEY },
        { name: roleRequestResourceDemandCount.name, table: rolerequestTableName, isHidden: true, prefix: '##key##numberOfResourcesPrefix###Number of resources', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 4, showValueExplanation: true }
    ],
    multifield: true,
    className: 'timeAllocationField'
};

export const criteriaRoleRequestTimeAllocationSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: `criteria_${rolerequestTableName}_time_allocation`,
    isCriteriaSpecific: true,
    readOnlyExplanation: true,
    label: '##key##timeAllocationTitle###Time allocation',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL,
    mandatory: true,
    leadingFieldName: roleRequestFixedTimeSectionField.name,
    valueKey: { defaultValue: 3, name: roleRequestFixedTimeSectionField.name, table: rolerequestTableName, editable: true, suffix: '##key##fixedTimeSectionSuffix###hrs', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_VALUE },
    fields: [
        {
            name: roleRequestLoadingSectionField.name,
            table: rolerequestTableName,
            editable: true,
            suffix: '##key##loadingSectionSuffix###% of working hours',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            key: 0,
            isHidden: true
        },
        {
            name: roleRequestTimeSectionField.name,
            table: rolerequestTableName,
            editable: true,
            suffix: '##key##hoursInTotalSuffix###hours in total',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            key: 1,
            isHidden: true
        },
        {
            name: roleRequestHoursPerDaySectionField.name,
            table: rolerequestTableName,
            editable: true,
            suffix: '##key##hoursPerDaySuffix###hours per day',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            key: 2,
            excludeFieldConditions: [
                {
                    type: 'shouldUseMultipleAssignees'
                }
            ]
        },
        {
            name: roleRequestFullTimeEquivalentField.name,
            table: rolerequestTableName,
            editable: true, prefix: '##key##FTESuffix###FTE',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            key: 3,
            showValueExplanation:
                true,
            explanationValueKey: ROLEREQUEST_FTE_FIELD_EXPLANATION_KEY,
            excludeFieldConditions: [
                {
                    type: 'shouldNotUseMultipleAssignees'
                }
            ]
        },
        {
            name: roleRequestResourceDemandCountRadioOption.name,
            table: rolerequestTableName,
            editable: true,
            label: '##key##Radio option###Radio option',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.CONTAINER,
            key: 4,
            showValueExplanation: true,
            fields: [
                { name: roleRequestResourceDemandCount.name, table: rolerequestTableName, editable: true, prefix: "##key##numberOfResourcesPrefix###Number of resources", display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 4, showValueExplanation: true },
                { name: roleRequestHoursPerDaySectionField.name, table: rolerequestTableName, isHidden: true, editable: true, prefix: '##key##forLabel###for', suffix: '##key##hoursPerDaySuffix###hours per day', display: CONTROL_FIELD_DISPLAY_TYPES.NESTED, type: CONTROL_FIELD_TYPES.RADIO_OPTION, key: 4 },
            ],
            excludeFieldConditions: [
                {
                    type: 'shouldNotUseMultipleAssignees'
                }
            ]
        },
        {
            name: roleRequestResourceDemandCount.name,
            table: rolerequestTableName,
            editable: true,
            suffix: '##key##numberOfResourcesPrefix###Number of resources',
            display: CONTROL_FIELD_DISPLAY_TYPES.NESTED,
            type: CONTROL_FIELD_TYPES.RADIO_OPTION,
            key: 5,
            excludeFieldConditions: [
                {
                    type: 'shouldUseMultipleAssignees'
                }
            ]
        },
    ],
    multifield: true,
    className: 'timeAllocationField',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['read', 'contextual edit']
    }
};

export const roleRequestDiaryDaysField = {
    editable: false,
    table: rolerequestTableName,
    name: `${rolerequestTableName}_diary_days`
};

export const roleRequestInfoSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.INFO
};

export const roleRequestIsTemplateSectionField = {
    editable: true,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.IS_TEMPLATE
};

export const roleRequestPublicationGuid = {
    editable: true,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.PUBLICATION_GUID
};

export const roleRequestSurrogateIdField = {
    editable: false,
    table: rolerequestTableName,
    name: ROLEREQUEST_FIELDS.SURROGATE_ID
};

export const rolerequestResourcesSectionField = {
    editable: true,
    table: rolerequestTableName,
    linkTable: resourceTableName,
    dataType: FIELD_DATA_TYPES.ID,
    actualFieldName: `${rolerequestTableName}_resource_guid`,
    name: 'rolerequest_resource_guids',
    label: 'Resource',
    type: ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL,
    lookupFilterLines,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: ['create', 'edit'],
        [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['edit', 'create', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL]: ['edit', 'read'],
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: ['contextual edit', 'read', 'edit'],
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: ['read']
    }
};

export const fieldConfigMap = {
    [bookingTableName]: {
        [bookingStartSectionField.name]: bookingStartSectionField,
        [bookingEndSectionField.name]: bookingEndSectionField,
        [bookingResourceSectionField.name]: bookingResourceSectionField,
        [bookingResourcesSectionField.name]: bookingResourcesSectionField,
        [bookingJobSectionField.name]: bookingJobSectionField,
        [bookingStatusSectionField.name]: bookingStatusSectionField,
        [bookingLoadingSectionField.name]: bookingLoadingSectionField,
        [bookingFixedHoursSectionField.name]: bookingFixedHoursSectionField,
        [bookingNotesSectionField.name]: bookingNotesSectionField,
        [bookingWorkActivitySectionField.name]: bookingWorkActivitySectionField,
        [bookingChargeModeSectionField.name]: bookingChargeModeSectionField,
        [bookingRevenuePerHourField.name]: bookingRevenuePerHourField,
        [bookingCostPerHourField.name]: bookingCostPerHourField,
        [bookingProfitPerHourField.name]: bookingProfitPerHourField,
        [bookingTotalCostField.name]: bookingTotalCostField,
        [bookingTotalRevenueField.name]: bookingTotalRevenueField,
        [bookingTotalProfitField.name]: bookingTotalProfitField,
        [bookingChargeModeField.name]: bookingChargeModeField,
        [bookingDiaryDaysField.name]: bookingDiaryDaysField
    },
    [jobTableName]: {
        [jobDescriptionField.name]: jobDescriptionField,
        [jobCodeField.name]: jobCodeField,
        [jobClientField.name]: jobClientField,
        [jobEngagementleadField.name]: jobEngagementleadField,
        [jobStatusField.name]: jobStatusField,
        [jobDiaryGroupField.name]: jobDiaryGroupField,
        [jobStartDateField.name]: jobStartDateField,
        [jobEndDateField.name]: jobEndDateField,
        [jobLocationField.name]: jobLocationField,
        [jobOpportynity.name]: jobOpportynity,
        [jobBillabilityType.name]: jobBillabilityType,
        [jobUtilisationType.name]: jobUtilisationType,
        [jobBudgetField.name]: jobBudgetField,
        [jobTotalRevenueField.name]: jobTotalRevenueField,
        [jobTotalCostField.name]: jobTotalCostField,
        [jobTotalProfitField.name]: jobTotalProfitField,
        [jobBudgetConsumedField.name]: jobBudgetConsumedField,
        [jobMilestonesField.name]: jobMilestonesField,
        [jobProfitMarginField.name]: jobProfitMarginField
    },
    [resourceTableName]: {
        [resourceDescriptionField.name]: resourceDescriptionField,
        [resourceFirstNameField.name]: resourceFirstNameField,
        [resourceLastNameField.name]: resourceLastNameField,
        [resourceCellphoneField.name]: resourceCellphoneField,
        [resourceLocalGradeField.name]: resourceLocalGradeField,
        [resourceDivisionField.name]: resourceDivisionField,
        [resourceCurrDeparmentField.name]: resourceCurrDeparmentField,
        [resourceDiaryGroupField.name]: resourceDiaryGroupField,
        [resourceEmailField.name]: resourceEmailField,
        [resourceJobTitleField.name]: resourceJobTitleField,
        [resourceManagerField.name]: resourceManagerField,
        [resourceLocationField.name]: resourceLocationField,
        [resourceChargeRateField.name]: resourceChargeRateField,
        [resourceLastLogin.name]: resourceLastLogin
    }
};

export const ENTITY_WINDOW_FIELDS_BLANK_VALUES_MAP = {
    [bookingLoadingSectionField.name]: '...',
    [bookingTimeSectionField.name]: '...',
    [bookingHoursPerDaySectionField.name]: '...',
    [bookingRevenuePerHourField.name]: '-',
    [bookingCostPerHourField.name]: '-',
    [bookingProfitPerHourField.name]: '-',
    [bookingTotalRevenueField.name]: '-',
    [bookingTotalCostField.name]: '-',
    [bookingTotalProfitField.name]: '-',
    [bookingResourceChargeRateCurrentValueField.name]: 'No rate set',
    [jobBudgetField.name]: '0.00',
    [roleRequestLoadingSectionField.name]: '...',
    [roleRequestTimeSectionField.name]: '...',
    [roleRequestHoursPerDaySectionField.name]: '...',
    [rolerequestRevenuePerHourField.name]: '-',
    [rolerequestCostPerHourField.name]: '-',
    [rolerequestProfitPerHourField.name]: '-',
    [rolerequestTotalRevenueField.name]: '-',
    [rolerequestTotalCostField.name]: '-',
    [rolerequestTotalProfitField.name]: '-',
    [rolerequestResourceChargeRateCurrentValueField.name]: 'No rate set',
    [ROLEREQUESTRESOURCE_FIELDS.REVENUE_PER_HOUR]: '-',
    [ROLEREQUESTRESOURCE_FIELDS.COST_PER_HOUR]: '-',
    [ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME]: '-',
    [ROLEREQUESTRESOURCE_FIELDS.TOTAL_REVENUE]: '-',
    [ROLEREQUESTRESOURCE_FIELDS.TOTAL_COST]: '-',
    [ROLEREQUESTRESOURCE_FIELDS.TOTAL_PROFIT]: '-',
    [ROLEREQUEST_FIELDS.ESTIMATE_REVENUEPERHOUR]: '-',
    [ROLEREQUEST_FIELDS.ESTIMATE_COSTPERHOUR]: '-',
    [ROLEREQUEST_FIELDS.ESTIMATE_PROFITPERHOUR]: '-',
    [ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE]: '-',
    [ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME]: '-'
};

export const baseRoleDuplicateFields = {
    [roleRequestDescription.name]: roleRequestDescription,
    [roleRequestResourceField.name]: roleRequestResourceField,
    [roleRequestWorkActivitySectionField.name]: roleRequestWorkActivitySectionField,
    [roleRequestNotesSectionField.name]: roleRequestNotesSectionField,
    [rolerequestChargeModeField.name]: rolerequestChargeModeField,
    [rolerequestCostPerHourField.name]: rolerequestCostPerHourField,
    [rolerequestRevenuePerHourField.name]: rolerequestRevenuePerHourField,
    [rolerequestOverriddenChargeRateField.name]: rolerequestOverriddenChargeRateField,
    [roleRequestGroupGuidField.name]: roleRequestGroupGuidField,
    [resourceChargeRateCurrentValueField.name]: resourceChargeRateCurrentValueField,
    [roleRequestHasCriteriaField.name]: roleRequestHasCriteriaField,
    [roleRequestStartSectionField.name]: roleRequestStartSectionField,
    [roleRequestEndSectionField.name]: roleRequestEndSectionField,
    [roleRequestHoursPerDaySectionField.name]: roleRequestHoursPerDaySectionField,
    [roleRequestTimeSectionField.name]: roleRequestTimeSectionField,
    [roleRequestLoadingSectionField.name]: roleRequestLoadingSectionField,
    [roleRequestNonWorkSectionField.name]: roleRequestNonWorkSectionField,
    [roleRequestFixedTimeSectionField.name]: roleRequestFixedTimeSectionField,
    [roleRequestFullTimeEquivalentField.name]: roleRequestFullTimeEquivalentField,
    [roleRequestResourceDemandCount.name]: roleRequestResourceDemandCount,
    [roleRequestInfoSectionField.name]: roleRequestInfoSectionField,
    [rolerequestEstimateTotalCostField.name]: rolerequestEstimateTotalCostField,
    [rolerequestEstimateTotalRevenueField.name]: rolerequestEstimateTotalRevenueField,
    [rolerequestEstimateTotalProfitField.name]: rolerequestEstimateTotalProfitField,
    [criteriaRoleEstimatesChargeModeSectionField.name]: criteriaRoleEstimatesChargeModeSectionField,
    [rolerequestEstimateChargeModeField.name]: rolerequestEstimateChargeModeField,
    [rolerequestCustomChargeRateField.name]: rolerequestCustomChargeRateField,
    [rolerequestEstimateRevenuePerHourField.name]: rolerequestEstimateRevenuePerHourField,
    [rolerequestEstimateCostPerHourField.name]: rolerequestEstimateCostPerHourField,
    [rolerequestEstimateProfitPerHourField.name]: rolerequestEstimateProfitPerHourField,
    [rolerequestChargeRateChargeModeSectionField.name]: rolerequestChargeRateChargeModeSectionField,
    [rolerequestResourceTotalRevenue.name]: rolerequestResourceTotalRevenue,
    [roleRequestJobGuidField.name]: roleRequestJobGuidField
};

const getAllowedSystemInfoSectionFields = (tableName) => {
    return [
        `${tableName}_updatedon`,
        `${tableName}_updatedby_resource_guid`,
        `${tableName}_createdon`,
        `${tableName}_createdby_resource_guid`,
        `${tableName}_externalid`
    ];
};

export const getSystemInfoSectionFields = (tableName, systemFieldInfos = {}) => {
    const allowedSystemInfoSectionFields = getAllowedSystemInfoSectionFields(tableName);

    return allowedSystemInfoSectionFields
        .reduce((obj, key) => {
            obj = {
                ...obj,
                [key]: {
                    ...systemFieldInfos[key],
                    readOnly: true, //to be removed when fieldStructure API endpoint returns SystemMaintained fields with readOnly: true (currently returns false)
                    excludeModules: {
                        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: ['edit'],
                        [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]: ['create'],
                        [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: ['edit'],
                        [ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL]: ['create'],
                        [ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL]: ['create']
                    }
                }
            };

            return obj;
        }, {});
};

export const timeAllocationFixedTimesValuesByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_FIXEDTIME_VALUES,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIXEDTIME_VALUES
};

export const timeAllocationLoadingSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingLoadingSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestLoadingSectionField
};

export const timeAllocationTimeSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingTimeSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestTimeSectionField
};

export const timeAllocationNonWorkSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingNonWorkSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestNonWorkSectionField
};

export const timeAllocationSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: timeAllocationSectionField,
    [TABLE_NAMES.ROLEREQUEST]: {
        [ROLE_ENTITY_TYPES.ROLE_BY_NAME]: roleRequestTimeAllocationSectionField,
        [ROLE_ENTITY_TYPES.ROLE_BY_CRITERIA]: criteriaRoleRequestTimeAllocationSectionField,
    }
};

export const chargeModeSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingChargeModeSectionField,
    [TABLE_NAMES.ROLEREQUEST]: {
        [ROLE_ENTITY_TYPES.ROLE_BY_NAME]: rolerequestChargeModeSectionField,
        [ROLE_ENTITY_TYPES.ROLE_BY_CRITERIA]: criteriaRoleEstimatesChargeModeSectionField
    }
};

export const dateRangeSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: dateRangeSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestDateRangeSectionField
};

export const diarySectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingDiaryDaysField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestDiaryDaysField
};

export const resourceSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingResourceSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestResourceField
};

export const startDateSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingStartSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestStartSectionField
};

export const endDateSectionFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingEndSectionField,
    [TABLE_NAMES.ROLEREQUEST]: roleRequestEndSectionField
};

export const rejectReasonSectionFieldByTableName = {
    [TABLE_NAMES.ROLEREQUEST]: roleRequestStatusField
};

export const revenuePerHourFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingRevenuePerHourField,
    [TABLE_NAMES.ROLEREQUEST]: {
        [ROLE_ENTITY_TYPES.ROLE_BY_NAME]: rolerequestRevenuePerHourField,
        [ROLE_ENTITY_TYPES]: rolerequestEstimateRevenuePerHourField
    }
};

export const costPerHourFieldByTableName = {
    [TABLE_NAMES.BOOKING]: bookingCostPerHourField,
    [TABLE_NAMES.ROLEREQUEST]: {
        [ROLE_ENTITY_TYPES.ROLE_BY_NAME]: rolerequestCostPerHourField,
        [ROLE_ENTITY_TYPES]: rolerequestEstimateCostPerHourField
    }
};