import createPagedDataModel from '../pagedData';
import { createFilterPaneModel } from '../filterPane/filterPane';
import {
    resourcesPageFilterGroupsConfig
} from '../filterPane/pagesFilterGroupsConfig';
import {
    TABLE_NAMES,
    BASE_FILTER_OPTIONS,
    FILTER_FIELD_NAMES
} from '../../constants/globalConsts';
import { resourcesPageCommandBarConfig } from '../commandBar/resourcesPageCommandBar';
import { RESOURCE_DESCRIPTION } from '../../constants/fieldConsts';
import { DATA_GRID_DENSITY_KEYS, SORT_ASCENDING } from '../../constants';
import { getDetailsPaneTemplate } from '../detailsPane/detailsPaneTemplate';

export default {
    pageState:{},
    uiOptions: {
        pageNumber: 1,
        density: DATA_GRID_DENSITY_KEYS.DEFAULT
    },
    tableName: TABLE_NAMES.RESOURCE,
    pagedData: {
        [TABLE_NAMES.RESOURCE]: createPagedDataModel(TABLE_NAMES.RESOURCE, TABLE_NAMES.RESOURCE, 20)
    },
    tableDatas: {},
    defaultSortOrder:
        {
            field: RESOURCE_DESCRIPTION,
            order: SORT_ASCENDING
        }
    ,
    selection: {
        fields: [],
        order: {
            orderFields: [
                {
                    field: RESOURCE_DESCRIPTION,
                    order: SORT_ASCENDING
                }
            ]
        },
        filter: {}
    },
    filters: {
        resource: {
            ...createFilterPaneModel(
                TABLE_NAMES.RESOURCE,
                TABLE_NAMES.RESOURCE,
                '',
                resourcesPageFilterGroupsConfig
            ),
            title: TABLE_NAMES.RESOURCE,
            baseFilter: {
                applied: false,
                selectedBaseFilter: BASE_FILTER_OPTIONS.ACTIVE
            }
        }
    },
    fieldOptions: {
        [RESOURCE_DESCRIPTION]: { loaded: false },
        [FILTER_FIELD_NAMES.RESOURCE_JOBTITLE]: { loaded: false },
        [FILTER_FIELD_NAMES.RESOURCE_GRADE]: { loaded: false },
        [FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME]: { loaded: false },
        [FILTER_FIELD_NAMES.RESOURCE_DEPARTMENT]: { loaded: false },
        [FILTER_FIELD_NAMES.RESOURCE_DIVISION]: { loaded: false },
        ["Charge Rate"]: { loaded: false },
    },
    displayFields: [
        RESOURCE_DESCRIPTION,
        FILTER_FIELD_NAMES.RESOURCE_JOBTITLE,
        FILTER_FIELD_NAMES.RESOURCE_GRADE,
        FILTER_FIELD_NAMES.RESOURCE_MANAGER_NAME,
        FILTER_FIELD_NAMES.RESOURCE_DEPARTMENT,
        FILTER_FIELD_NAMES.RESOURCE_DIVISION,
        "Charge Rate"
    ],
    linkFields: [RESOURCE_DESCRIPTION],
    fixedFields: [RESOURCE_DESCRIPTION],
    contextMenuField: RESOURCE_DESCRIPTION,
    dynamicComponentFields: [RESOURCE_DESCRIPTION],
    commandBarConfig : resourcesPageCommandBarConfig,
    detailsPane: getDetailsPaneTemplate()
};
