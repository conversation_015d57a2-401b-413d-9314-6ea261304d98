import { ENTITY_WINDOW_MODULES, FILTER_FIELD_NAMES, TABLE_NAMES } from '../../constants';
import { JOB_CLIENT_GUID, JOB_DESCRIPTION, JOB_END_DATE, JOB_MILESTONES, JOB_RESOURCE_WORKED_HOURS, JOB_START_DATE } from '../../constants/fieldConsts';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import createPagedDataModel from '../pagedData';

const baseConfig = {
    tableName: TABLE_NAMES.JOB,
    pagedData: {
        [TABLE_NAMES.JOB]: createPagedDataModel(TABLE_NAMES.JOB, TABLE_NAMES.JOB, 5)
    },
    tableDatas: {},
    defaultSortOrder: {
        field: JOB_END_DATE,
        order: 'Descending'
    },
    workHistoryData: {
        resourceGuid: ''
    },
    selection: {
        fields: [],
        order: {
            orderFields: [
                {
                    field: JOB_END_DATE,
                    order: 'Descending'
                }
            ]
        },
        filter: {
        }
    },
    dynamicComponentFields: [],
    contextMenuField: '',
    fixedFields: [JOB_DESCRIPTION],
    enableMultipleSelection: false,
    omitFields : [JOB_DESCRIPTION, FILTER_FIELD_NAMES.JOB_FTE_RANGE, JOB_MILESTONES]
};

export const workHistory = {
    [PROFILE_PAGE_ALIAS]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            },
            [JOB_RESOURCE_WORKED_HOURS]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_START_DATE,
            JOB_END_DATE,
            JOB_RESOURCE_WORKED_HOURS
        ],
        linkFields: [JOB_DESCRIPTION]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        fixedColumnWidth: 30,
        linkFields: [JOB_DESCRIPTION]
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: [JOB_DESCRIPTION]
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: [JOB_DESCRIPTION]
    },
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: []
    },
    [ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: []
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: []
    },
    [ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: []
    },
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: {
        ...baseConfig,
        uiOptions: {
            pageNumber: 1,
            density: null,
            profilePageMaxFieldsSelection: 2
        },
        fieldOptions: {
            [JOB_DESCRIPTION]: {
                loaded: false
            },
            [JOB_CLIENT_GUID]: {
                loaded: false
            },
            [JOB_START_DATE]: {
                loaded: false
            },
            [JOB_END_DATE]: {
                loaded: false
            }
        },
        displayFields: [
            JOB_DESCRIPTION,
            JOB_CLIENT_GUID,
            JOB_END_DATE
        ],
        scrollX: true,
        fixedColumnWidth: 30,
        linkFields: []
    }
};