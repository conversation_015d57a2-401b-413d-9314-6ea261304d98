import { OPERATORS, FILTER_TYPES, FILTER_FIELD_NAMES, TABLE_NAMES, FIELD_DATA_TYPES, PLANNER_PAGE_ALIAS } from '../../constants';
import { ROLEREQUESTRESOURCE_FIELDS } from '../../constants/fieldConsts';
import { ROLE_AND_REQUEST_FEATURE_SWITCH, FILTER_SUBTYPES } from '../../constants/globalConsts';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';

const { SKILL, SKILL_LEVEL, SKILL_NO_LEVEL, RESOURCE_GUID, RESOURCE_TYPE, AVAILABILITY, AVAILABLE_HOURS, UTILISATION, JOB_GUID, JOB_CODE, JOB_START, JOB_END, JOB_CLIENT_GUID, JO<PERSON>_ENGAGEMENTLEAD_GUID,
    BOOKING_IS_ASSIGNED, BOOKING_START, BOOKING_END, RESOURCE_DEPARTMENT, RESOURCE_GRADE, RESOURCE_MANAGER, RESOURCE_LOCATION, RESOURCE_JOBTITLE,
    JOB_LOCATION, JOB_PREVIOUS_JOB_GUID, JOB_NEXT_JOB_GUID, JOB_STATUS, BOOKING_STATUS, BOOKING_NOTES, BOOKING_WORK_ACTIVITY, BOOKING_TOTAL_COST, BOOKING_TOTAL_REVENUE, BOOKING_TOTAL_PROFIT, JOB_CHARGE_CODE, JOB_FTE_REFERENCE_DIARY, RESOURCE_STAFFNO, JOB_I_MANAGE, JOB_ACTION_REQUIRED, JOB_FTE_RANGE, JOB_FTE_TOTAL,
    ROLE_START, ROLE_END, ROLE_NAME, ROLE_NOTES, ROLE_TOTAL_PROFIT, ROLE_TOTAL_REVENUE, ROLE_TOTAL_COST, ROLE_WORK_ACTIVITY, ROLE_RESOURCE, ROLE_GROUP, ROLE_JOB, ROLE_HASCRITERIA, ROLE_STATUS, BOOKING_FTE_ASSIGNED, ROLE_APPLIED_TO, ROLE_AVAILABLE_FOR, JOB_IS_CONFIDENTIAL, JOB_IS_EXCLUDE_FROM_UTILIZATION, JOB_IS_EXCLUDE_FROM_BILLABILITY, RESOURCE_USERSTATUS,
    JOB_COSTS_BUDGET, JOB_COSTS_AS_BOOKED, JOB_COSTS_AS_PERCENTAGE_OF_BUDGET, JOB_REVENUE_TARGET, JOB_REVENUE_AS_BOOKED, JOB_REVENUE_AS_PERCENTAGE_OF_TARGET, JOB_PROFIT_MARGIN_TARGET, JOB_PROFIT_MARGIN_AS_BOOKED, JOB_MARGIN_AS_PERCENTAGE_OF_TARGET, JOB_HOURS_BUDGET, JOB_TOTAL_HOURS_BOOKED, JOB_HOURS_AS_PERCENTAGE_OF_BUDGET,
    JOB_CURRENT_DEPARTMENT_GUID, JOB_DIVISION_GUID
} = FILTER_FIELD_NAMES;

const getUpdatedOnFilterModel = (tableName) => {
    const updatedOnFieldName = `${tableName}_updatedon`;

    return {
        tableName,
        fieldName: updatedOnFieldName,
        fieldAlias: 'updatedon',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    };
};

const getUpdatedByFilterModel = (tableName) => {
    const updatedByFieldName = `${tableName}_updatedby_resource_guid`;

    return {
        tableName,
        fieldName: updatedByFieldName,
        fieldAlias: 'updatedby',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    };
};

const getCreatedOnFilterModel = (tableName) => {
    const createdOnFieldName = `${tableName}_createdon`;

    return {
        tableName,
        fieldName: createdOnFieldName,
        fieldAlias: 'createdon',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    };
};

const getCreatedByFilterModel = (tableName) => {
    const createdByFieldName = `${tableName}_createdby_resource_guid`;

    return {
        tableName,
        fieldName: createdByFieldName,
        fieldAlias: 'createdby',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    };
};

const ROLEREQUESTRESOURCE_FILTERS = {
    STATUS_GUID: {
        tableName: TABLE_NAMES.ROLEREQUESTRESOURCE,
        fieldName: ROLEREQUESTRESOURCE_FIELDS.STATUS_GUID,
        operator: OPERATORS.DB_OPERATORS.CONTAINS
    },
    RESOURCE_GUID: {
        tableName: TABLE_NAMES.ROLEREQUESTRESOURCE,
        fieldName: ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID,
        operator: OPERATORS.DB_OPERATORS.CONTAINS
    }
};

const RESOURCE_GROUP_FILTERS = {
    SKILL: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: SKILL,
        fieldAlias: 'Skills',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.SKILL,
        noLevelFieldName: SKILL_NO_LEVEL,
        noLevelOperator: OPERATORS.DB_OPERATORS.EQUALS,
        withLevelFieldName: SKILL_LEVEL,
        withLevelOperator: OPERATORS.DB_OPERATORS.CONTAINS
    },
    RESOURCE_GUID: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_GUID,
        fieldAlias: 'Resource Name',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    AVAILABILITY: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: AVAILABILITY,
        fieldAlias: 'Availability',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE_SENSITIVE
    },
    AVAILABLE_HOURS: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: AVAILABLE_HOURS,
        fieldAlias: 'Available hours',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE_SENSITIVE,
        subType: FILTER_SUBTYPES.INPUT_NUMBER_HOURS
    },
    UTILISATION: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: UTILISATION,
        fieldAlias: 'Utilisation',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE_SENSITIVE
    },
    DIVISION: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: FILTER_FIELD_NAMES.RESOURCE_DIVISION,
        fieldAlias: 'Division',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    DEPARTMENT: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_DEPARTMENT,
        fieldAlias: 'Department',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    RESOURCE_MANAGER: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_MANAGER,
        fieldAlias: 'Line Manager',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    LOCATION: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_LOCATION,
        fieldAlias: 'Location',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    RESOURCE_GRADE: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_GRADE,
        fieldAlias: 'Grade',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    RESOURCE_TYPE: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_TYPE,
        fieldAlias: 'Employment type',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_TITLE: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_JOBTITLE,
        fieldAlias: 'Title',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    RESOURCE_UPDATED_ON: getUpdatedOnFilterModel(TABLE_NAMES.RESOURCE),
    RESOURCE_UPDATED_BY: getUpdatedByFilterModel(TABLE_NAMES.RESOURCE),
    RESOURCE_CREATED_ON: getCreatedOnFilterModel(TABLE_NAMES.RESOURCE),
    RESOURCE_CREATED_BY: getCreatedByFilterModel(TABLE_NAMES.RESOURCE),
    RESOURCE_STAFFNO: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_STAFFNO,
        fieldAlias: 'Employee Id',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    USER_STATUS: {
        tableName: TABLE_NAMES.RESOURCE,
        fieldName: RESOURCE_USERSTATUS,
        fieldAlias: 'Active user',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.BOOLEAN
    }
};

const JOB_GROUP_FILTERS = {
    JOB_GUID: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_GUID,
        fieldAlias: 'Job Name',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_START: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_START,
        fieldAlias: 'Job Start',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    JOB_END: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_END,
        fieldAlias: 'Job End',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.LESS_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    JOB_CLIENT_GUID: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_CLIENT_GUID,
        fieldAlias: 'Client',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_ENGAGEMENTLEAD_GUID: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_ENGAGEMENTLEAD_GUID,
        fieldAlias: 'Line Manager',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_CURRENT_DEPARTMENT_GUID: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_CURRENT_DEPARTMENT_GUID,
        fieldAlias: 'Job department',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_DIVISION_GUID: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_DIVISION_GUID,
        fieldAlias: 'Job division',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_LOCATION: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_LOCATION,
        fieldAlias: 'Job location',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_PREVIOUS_JOB: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_PREVIOUS_JOB_GUID,
        fieldAlias: 'Previous Related Job',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_NEXT_JOB: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_NEXT_JOB_GUID,
        fieldAlias: 'Next Related Job',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_STATUS: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_STATUS,
        fieldAlias: 'Job status',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_OPPORTUNITY_PERCENT: {
        tableName: TABLE_NAMES.JOB,
        fieldName: FILTER_FIELD_NAMES.JOB_OPPORTUNITY_PERCENT,
        fieldAlias: 'Job opportunity percent',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC
    },
    JOB_CODE: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_CODE,
        fieldAlias: 'Job reference code',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    JOB_CHARGE_CODE: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_CHARGE_CODE,
        fieldAlias: 'Charge type',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_FTE_REFERENCE_DIARY: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_FTE_REFERENCE_DIARY,
        fieldAlias: 'FTE reference diary',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    JOB_UPDATED_ON: getUpdatedOnFilterModel(TABLE_NAMES.JOB),
    JOB_UPDATED_BY: getUpdatedByFilterModel(TABLE_NAMES.JOB),
    JOB_CREATED_ON: getCreatedOnFilterModel(TABLE_NAMES.JOB),
    JOB_CREATED_BY: getCreatedByFilterModel(TABLE_NAMES.JOB),
    JOB_I_MANAGE: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_I_MANAGE,
        fieldAlias: 'Job I manage',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.BOOLEAN,
        filterFieldDataType: FIELD_DATA_TYPES.BOOL
    },
    JOB_ACTION_REQUIRED: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_ACTION_REQUIRED,
        fieldAlias: 'Job Action Required',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.INT
    },
    JOB_FTE_RANGE: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_FTE_RANGE,
        fieldAlias: 'FTE in range',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.NUMERIC_PARAMETER,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    JOB_FTE_TOTAL: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_FTE_TOTAL,
        fieldAlias: 'FTE Total',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    JOB_IS_CONFIDENTIAL: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_IS_CONFIDENTIAL,
        fieldAlias: 'Confidential',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.BOOLEAN,
        filterFieldDataType: FIELD_DATA_TYPES.BOOL
    },
    JOB_IS_EXCLUDE_FROM_UTILIZATION: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_IS_EXCLUDE_FROM_UTILIZATION,
        fieldAlias: 'Exclude bookings from utilisation',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.BOOLEAN,
        filterFieldDataType: FIELD_DATA_TYPES.BOOL
    },
    JOB_IS_EXCLUDE_FROM_BILLABILITY: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_IS_EXCLUDE_FROM_BILLABILITY,
        fieldAlias: 'Exclude from billability',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.BOOLEAN,
        filterFieldDataType: FIELD_DATA_TYPES.BOOL
    },
    JOB_COSTS_BUDGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_COSTS_BUDGET,
        fieldAlias: 'Costs budget',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_COSTS_AS_BOOKED: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_COSTS_AS_BOOKED,
        fieldAlias: 'Costs as booked',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_COSTS_AS_PERCENTAGE_OF_BUDGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
        fieldAlias: 'Costs as percentage of budget',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_REVENUE_TARGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_REVENUE_TARGET,
        fieldAlias: 'Revenue target',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_REVENUE_AS_BOOKED: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_REVENUE_AS_BOOKED,
        fieldAlias: 'Revenue as booked',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_REVENUE_AS_PERCENTAGE_OF_TARGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
        fieldAlias: 'Revenue as percentage of target',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_PROFIT_MARGIN_TARGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_PROFIT_MARGIN_TARGET,
        fieldAlias: 'Profit margin target',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_PROFIT_MARGIN_AS_BOOKED: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_PROFIT_MARGIN_AS_BOOKED,
        fieldAlias: 'Profit margin as booked',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_MARGIN_AS_PERCENTAGE_OF_TARGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
        fieldAlias: 'Margin as percentage of target',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_HOURS_BUDGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_HOURS_BUDGET,
        fieldAlias: 'Hours budget',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_TOTAL_HOURS_BOOKED: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_TOTAL_HOURS_BOOKED,
        fieldAlias: 'Total hours booked',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
    JOB_HOURS_AS_PERCENTAGE_OF_BUDGET: {
        tableName: TABLE_NAMES.JOB,
        fieldName: JOB_HOURS_AS_PERCENTAGE_OF_BUDGET,
        fieldAlias: 'Hours as percentage of budget',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
    },
};

const MARKETPLACE_GROUP_FILTERS = {
    ROLE_APPLIED_TO: {
        tableName: TABLE_NAMES.ROLECRITERIA,
        fieldName: ROLE_APPLIED_TO,
        fieldAlias: 'I have applied to',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMBER,
        filterFieldDataType: FIELD_DATA_TYPES.INT
    },
    ROLE_AVAILABLE_FOR: {
        tableName: TABLE_NAMES.ROLECRITERIA,
        fieldName: ROLE_AVAILABLE_FOR,
        fieldAlias: 'I am available for',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMBER,
        filterFieldDataType: FIELD_DATA_TYPES.INT
    }
};

const BOOKING_GROUP_FILTERS = {
    BOOKING_IS_ASSIGNED: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_IS_ASSIGNED,
        fieldAlias: 'Unassigned',
        icon: 'booking',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.CHECKBOX,
        inlineFilter: true,
        uiOppositeValue: true
    },
    BOOKING_START: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_START,
        fieldAlias: 'Booking Start',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    BOOKING_END: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_END,
        fieldAlias: 'Booking End',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.LESS_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    BOOKING_STATUS: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_STATUS,
        fieldAlias: 'Booking status',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    BOOKING_NOTES: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_NOTES,
        fieldAlias: 'Booking Notes',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    BOOKING_WORK_ACTIVITY: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_WORK_ACTIVITY,
        fieldAlias: 'Work Activity',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    BOOKING_UPDATED_ON: getUpdatedOnFilterModel(TABLE_NAMES.BOOKING),
    BOOKING_UPDATED_BY: getUpdatedByFilterModel(TABLE_NAMES.BOOKING),
    BOOKING_CREATED_ON: getCreatedOnFilterModel(TABLE_NAMES.BOOKING),
    BOOKING_CREATED_BY: getCreatedByFilterModel(TABLE_NAMES.BOOKING),
    BOOKING_TOTAL_COST: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_TOTAL_COST,
        fieldAlias: 'Cost',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    BOOKING_TOTAL_REVENUE: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_TOTAL_REVENUE,
        fieldAlias: 'Revenue',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    BOOKING_TOTAL_PROFIT: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_TOTAL_PROFIT,
        fieldAlias: 'Profit',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    BOOKING_FTE_ASSIGNED: {
        tableName: TABLE_NAMES.BOOKING,
        fieldName: BOOKING_FTE_ASSIGNED,
        fieldAlias: 'FTE assigned',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    }
};

const ROLEREQUEST_FILTERS = {
    ROLE_NAME: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_NAME,
        fieldAlias: 'Role name',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    ROLE_RESOURCE: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_RESOURCE,
        fieldAlias: 'Assigned to',
        icon: 'role',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    ROLE_GROUP: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_GROUP,
        fieldAlias: 'Role group',
        icon: 'role-group',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    ROLE_JOB: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_JOB,
        fieldAlias: 'Job',
        icon: 'job',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    ROLE_START: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_START,
        fieldAlias: 'Role start',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.GREATER_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    ROLE_END: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_END,
        fieldAlias: 'Role End',
        icon: 'calendar',
        operator: OPERATORS.DB_OPERATORS.LESS_THAN_OR_EQUAL,
        type: FILTER_TYPES.DATE
    },
    ROLE_NOTES: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_NOTES,
        fieldAlias: 'Role Notes',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.LIKE,
        type: FILTER_TYPES.TEXT
    },
    ROLE_WORK_ACTIVITY: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_WORK_ACTIVITY,
        fieldAlias: 'Work Activity',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.CONTAINS,
        type: FILTER_TYPES.MULTY_VALUES
    },
    ROLE_UPDATED_ON: getUpdatedOnFilterModel(TABLE_NAMES.ROLEREQUEST),
    ROLE_UPDATED_BY: getUpdatedByFilterModel(TABLE_NAMES.ROLEREQUEST),
    ROLE_CREATED_ON: getCreatedOnFilterModel(TABLE_NAMES.ROLEREQUEST),
    ROLE_CREATED_BY: getCreatedByFilterModel(TABLE_NAMES.ROLEREQUEST),
    ROLE_TOTAL_COST: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_TOTAL_COST,
        fieldAlias: 'Cost',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    ROLE_TOTAL_REVENUE: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_TOTAL_REVENUE,
        fieldAlias: 'Revenue',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    ROLE_TOTAL_PROFIT: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_TOTAL_PROFIT,
        fieldAlias: 'Profit',
        icon: 'user',
        operator: OPERATORS.DB_OPERATORS.EQUALS,
        type: FILTER_TYPES.NUMERIC,
        filterFieldDataType: FIELD_DATA_TYPES.FLOAT
    },
    ROLE_HASCRITERIA: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_HASCRITERIA,
        operator: OPERATORS.DB_OPERATORS.CONTAINS
    },
    ROLE_STATUS: {
        tableName: TABLE_NAMES.ROLEREQUEST,
        fieldName: ROLE_STATUS,
        operator: OPERATORS.DB_OPERATORS.CONTAINS
    }
};

const sharedRolesFilterFields = [
    ROLEREQUEST_FILTERS.ROLE_GROUP,
    ROLEREQUEST_FILTERS.ROLE_START,
    ROLEREQUEST_FILTERS.ROLE_END,
    ROLEREQUEST_FILTERS.ROLE_NOTES,
    ROLEREQUEST_FILTERS.ROLE_WORK_ACTIVITY,
    ROLEREQUEST_FILTERS.ROLE_UPDATED_ON,
    ROLEREQUEST_FILTERS.ROLE_UPDATED_BY,
    ROLEREQUEST_FILTERS.ROLE_CREATED_ON,
    ROLEREQUEST_FILTERS.ROLE_CREATED_BY,
    ROLEREQUEST_FILTERS.ROLE_TOTAL_COST,
    ROLEREQUEST_FILTERS.ROLE_TOTAL_REVENUE,
    ROLEREQUEST_FILTERS.ROLE_TOTAL_PROFIT
];

const resourceFilters = [
    RESOURCE_GROUP_FILTERS.RESOURCE_GUID,
    RESOURCE_GROUP_FILTERS.RESOURCE_GRADE,
    RESOURCE_GROUP_FILTERS.SKILL,
    RESOURCE_GROUP_FILTERS.AVAILABILITY,
    RESOURCE_GROUP_FILTERS.AVAILABLE_HOURS,
    RESOURCE_GROUP_FILTERS.UTILISATION,
    RESOURCE_GROUP_FILTERS.DIVISION,
    RESOURCE_GROUP_FILTERS.DEPARTMENT,
    RESOURCE_GROUP_FILTERS.LOCATION,
    RESOURCE_GROUP_FILTERS.JOB_TITLE,
    RESOURCE_GROUP_FILTERS.RESOURCE_MANAGER,
    RESOURCE_GROUP_FILTERS.RESOURCE_TYPE,
    RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_ON,
    RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_BY,
    RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_ON,
    RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_BY,
    RESOURCE_GROUP_FILTERS.RESOURCE_STAFFNO,
    RESOURCE_GROUP_FILTERS.USER_STATUS
];

const plannerResourceViewFilterGroups = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE,
            JOB_GROUP_FILTERS.JOB_COSTS_BUDGET,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_TARGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_HOURS_BUDGET,
            JOB_GROUP_FILTERS.JOB_TOTAL_HOURS_BOOKED,
            JOB_GROUP_FILTERS.JOB_HOURS_AS_PERCENTAGE_OF_BUDGET
        ],
        visible: true
    },
    {
        title: 'Resource',
        tableName: TABLE_NAMES.RESOURCE,
        operator: '',
        icon: 'user-manager',
        filters: resourceFilters,
        visible: true
    },
    {
        title: 'Booking',
        tableName: TABLE_NAMES.BOOKING,
        operator: '',
        icon: 'booking',
        filters: [
            BOOKING_GROUP_FILTERS.BOOKING_START,
            BOOKING_GROUP_FILTERS.BOOKING_END,
            BOOKING_GROUP_FILTERS.BOOKING_STATUS,
            BOOKING_GROUP_FILTERS.BOOKING_NOTES,
            BOOKING_GROUP_FILTERS.BOOKING_WORK_ACTIVITY,
            BOOKING_GROUP_FILTERS.BOOKING_UPDATED_ON,
            BOOKING_GROUP_FILTERS.BOOKING_UPDATED_BY,
            BOOKING_GROUP_FILTERS.BOOKING_CREATED_ON,
            BOOKING_GROUP_FILTERS.BOOKING_CREATED_BY,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_COST,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_REVENUE,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_PROFIT,
            BOOKING_GROUP_FILTERS.BOOKING_FTE_ASSIGNED
        ],
        visible: true
    },
    {
        title: 'Roles',
        tableName: TABLE_NAMES.ROLEREQUEST,
        operator: '',
        icon: 'role',
        filters: [
            ROLEREQUEST_FILTERS.ROLE_NAME,
            ...sharedRolesFilterFields
        ],
        visible: !ROLE_AND_REQUEST_FEATURE_SWITCH
    }
];

const plannerJobsViewFilterGroups = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_FTE_RANGE,
            JOB_GROUP_FILTERS.JOB_FTE_TOTAL,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE,
            JOB_GROUP_FILTERS.JOB_COSTS_BUDGET,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_TARGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_HOURS_BUDGET,
            JOB_GROUP_FILTERS.JOB_TOTAL_HOURS_BOOKED,
            JOB_GROUP_FILTERS.JOB_HOURS_AS_PERCENTAGE_OF_BUDGET
        ],
        visible: true
    },
    {
        title: 'Resource',
        tableName: TABLE_NAMES.RESOURCE,
        operator: '',
        icon: 'user-manager',
        filters: [
            RESOURCE_GROUP_FILTERS.RESOURCE_GUID,
            RESOURCE_GROUP_FILTERS.RESOURCE_GRADE,
            // RESOURCE_GROUP_FILTERS.SKILL, // TODO Commented for future implementation
            RESOURCE_GROUP_FILTERS.DIVISION,
            RESOURCE_GROUP_FILTERS.DEPARTMENT,
            RESOURCE_GROUP_FILTERS.LOCATION,
            RESOURCE_GROUP_FILTERS.JOB_TITLE,
            RESOURCE_GROUP_FILTERS.RESOURCE_MANAGER,
            RESOURCE_GROUP_FILTERS.RESOURCE_TYPE,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_STAFFNO,
            RESOURCE_GROUP_FILTERS.USER_STATUS
        ],
        visible: true
    },
    {
        title: 'Booking',
        tableName: TABLE_NAMES.BOOKING,
        operator: '',
        icon: 'booking',
        filters: [
            BOOKING_GROUP_FILTERS.BOOKING_START,
            BOOKING_GROUP_FILTERS.BOOKING_END,
            BOOKING_GROUP_FILTERS.BOOKING_STATUS,
            BOOKING_GROUP_FILTERS.BOOKING_NOTES,
            BOOKING_GROUP_FILTERS.BOOKING_WORK_ACTIVITY,
            BOOKING_GROUP_FILTERS.BOOKING_UPDATED_ON,
            BOOKING_GROUP_FILTERS.BOOKING_UPDATED_BY,
            BOOKING_GROUP_FILTERS.BOOKING_CREATED_ON,
            BOOKING_GROUP_FILTERS.BOOKING_CREATED_BY,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_COST,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_REVENUE,
            BOOKING_GROUP_FILTERS.BOOKING_TOTAL_PROFIT,
            BOOKING_GROUP_FILTERS.BOOKING_FTE_ASSIGNED
        ],
        visible: true
    },
    {
        title: 'Roles',
        tableName: TABLE_NAMES.ROLEREQUEST,
        operator: '',
        icon: 'role',
        filters: [
            ROLEREQUEST_FILTERS.ROLE_NAME,
            ...sharedRolesFilterFields
        ],
        visible: !ROLE_AND_REQUEST_FEATURE_SWITCH
    }
];

const plannerPageFilterGroupsConfig = {
    resource: plannerResourceViewFilterGroups,
    job: plannerJobsViewFilterGroups
};

const jobsPageFilterGroupsConfig = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_FTE_TOTAL,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE,
            JOB_GROUP_FILTERS.JOB_COSTS_BUDGET,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_TARGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_HOURS_BUDGET,
            JOB_GROUP_FILTERS.JOB_TOTAL_HOURS_BOOKED,
            JOB_GROUP_FILTERS.JOB_HOURS_AS_PERCENTAGE_OF_BUDGET
        ]
    }
];

const jobsPageBaseFilterGroupsConfig = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_I_MANAGE,
            JOB_GROUP_FILTERS.JOB_ACTION_REQUIRED
        ]
    }
];

const roleInboxPageFilterGroupsConfig = [
    {
        title: 'Role',
        tableName: TABLE_NAMES.ROLEREQUEST,
        operator: '',
        icon: 'role',
        filters: [
            ROLEREQUEST_FILTERS.ROLE_RESOURCE,
            ROLEREQUEST_FILTERS.ROLE_JOB,
            ...sharedRolesFilterFields
        ],
        subFiltersMap:
        {
            [ROLE_RESOURCE]: ROLEREQUESTRESOURCE_FILTERS.RESOURCE_GUID
        }
    }
];

const roleInboxPageBaseFilterGroupsConfig = [
    {
        tableName: TABLE_NAMES.ROLEREQUEST,
        filters: [
            ROLEREQUEST_FILTERS.ROLE_HASCRITERIA,
            ROLEREQUEST_FILTERS.ROLE_STATUS
        ],
        subFiltersMap:
        {
            [ROLE_STATUS]: ROLEREQUESTRESOURCE_FILTERS.STATUS_GUID
        }
    }
];

const marketplacePageFilterGroupsConfig = [
    {
        title: 'Role',
        tableName: TABLE_NAMES.ROLEREQUEST,
        operator: '',
        icon: 'role',
        filters: [
            ROLEREQUEST_FILTERS.ROLE_RESOURCE,
            ROLEREQUEST_FILTERS.ROLE_JOB,
            ...sharedRolesFilterFields
        ]
    }
];

const marketplacePageBaseFilterGroupsConfig = [
    {
        title: 'Role',
        tableName: TABLE_NAMES.ROLEREQUEST,
        operator: '',
        icon: 'role',
        filters: [
            MARKETPLACE_GROUP_FILTERS.ROLE_APPLIED_TO,
            MARKETPLACE_GROUP_FILTERS.ROLE_AVAILABLE_FOR
        ]
    }
];

const tableViewResourceViewFilterGroups = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE,
            JOB_GROUP_FILTERS.JOB_COSTS_BUDGET,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_TARGET,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_TARGET,
            JOB_GROUP_FILTERS.JOB_PROFIT_MARGIN_AS_BOOKED,
            JOB_GROUP_FILTERS.JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
            JOB_GROUP_FILTERS.JOB_HOURS_BUDGET,
            JOB_GROUP_FILTERS.JOB_TOTAL_HOURS_BOOKED,
            JOB_GROUP_FILTERS.JOB_HOURS_AS_PERCENTAGE_OF_BUDGET
        ],
        visible: true
    },
    {
        title: 'Resource',
        tableName: TABLE_NAMES.RESOURCE,
        operator: '',
        icon: 'user-manager',
        filters: [
            RESOURCE_GROUP_FILTERS.RESOURCE_GUID,
            RESOURCE_GROUP_FILTERS.RESOURCE_GRADE,
            RESOURCE_GROUP_FILTERS.SKILL,
            RESOURCE_GROUP_FILTERS.AVAILABILITY,
            RESOURCE_GROUP_FILTERS.AVAILABLE_HOURS,
            RESOURCE_GROUP_FILTERS.UTILISATION,
            RESOURCE_GROUP_FILTERS.DIVISION,
            RESOURCE_GROUP_FILTERS.DEPARTMENT,
            RESOURCE_GROUP_FILTERS.LOCATION,
            RESOURCE_GROUP_FILTERS.JOB_TITLE,
            RESOURCE_GROUP_FILTERS.RESOURCE_MANAGER,
            RESOURCE_GROUP_FILTERS.RESOURCE_TYPE,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_STAFFNO,
            RESOURCE_GROUP_FILTERS.USER_STATUS
        ],
        visible: true
    }
];

const tableViewJobsViewFilterGroups = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_FTE_RANGE,
            JOB_GROUP_FILTERS.JOB_FTE_TOTAL,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE
        ],
        visible: true
    },
    {
        title: 'Resource',
        tableName: TABLE_NAMES.RESOURCE,
        operator: '',
        icon: 'user-manager',
        filters: [
            RESOURCE_GROUP_FILTERS.RESOURCE_GUID,
            RESOURCE_GROUP_FILTERS.RESOURCE_GRADE,
            RESOURCE_GROUP_FILTERS.DIVISION,
            RESOURCE_GROUP_FILTERS.DEPARTMENT,
            RESOURCE_GROUP_FILTERS.LOCATION,
            RESOURCE_GROUP_FILTERS.JOB_TITLE,
            RESOURCE_GROUP_FILTERS.RESOURCE_MANAGER,
            RESOURCE_GROUP_FILTERS.RESOURCE_TYPE,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_UPDATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_ON,
            RESOURCE_GROUP_FILTERS.RESOURCE_CREATED_BY,
            RESOURCE_GROUP_FILTERS.RESOURCE_STAFFNO,
            RESOURCE_GROUP_FILTERS.USER_STATUS
        ],
        visible: true
    }
];

const tableViewPageFilterGroupsConfig = {
    resource: tableViewResourceViewFilterGroups,
    job: tableViewJobsViewFilterGroups
};

const peopleFinderFilterGroupsConfig = {
    [PLANNER_PAGE_ALIAS]: [
        {
            title: 'Resource',
            tableName: TABLE_NAMES.RESOURCE,
            operator: '',
            icon: 'user-manager',
            filters: resourceFilters,
            visible: true
        }
    ],
    [PROFILE_PAGE_ALIAS]: [
        {
            title: 'Resource',
            tableName: TABLE_NAMES.RESOURCE,
            operator: '',
            icon: 'user-manager',
            filters: [
                RESOURCE_GROUP_FILTERS.RESOURCE_GUID,
                RESOURCE_GROUP_FILTERS.RESOURCE_GRADE,
                RESOURCE_GROUP_FILTERS.SKILL,
                RESOURCE_GROUP_FILTERS.DEPARTMENT,
                RESOURCE_GROUP_FILTERS.LOCATION,
                RESOURCE_GROUP_FILTERS.JOB_TITLE,
                RESOURCE_GROUP_FILTERS.RESOURCE_MANAGER,
                RESOURCE_GROUP_FILTERS.RESOURCE_TYPE
            ],
            visible: true
        }
    ]
};

const jobFilterDialogFilterGroupConfig = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_FTE_TOTAL,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE
        ],
        visible: true
    }
];

const massDuplicateJobsFilterGroupConfig = [
    {
        title: 'Job',
        tableName: TABLE_NAMES.JOB,
        operator: '',
        icon: 'job',
        filters: [
            JOB_GROUP_FILTERS.JOB_GUID,
            JOB_GROUP_FILTERS.JOB_CLIENT_GUID,
            JOB_GROUP_FILTERS.JOB_STATUS,
            JOB_GROUP_FILTERS.JOB_ENGAGEMENTLEAD_GUID,
            JOB_GROUP_FILTERS.JOB_CURRENT_DEPARTMENT_GUID,
            JOB_GROUP_FILTERS.JOB_DIVISION_GUID,
            JOB_GROUP_FILTERS.JOB_LOCATION,
            JOB_GROUP_FILTERS.JOB_PREVIOUS_JOB,
            JOB_GROUP_FILTERS.JOB_NEXT_JOB,
            JOB_GROUP_FILTERS.JOB_CODE,
            JOB_GROUP_FILTERS.JOB_OPPORTUNITY_PERCENT,
            JOB_GROUP_FILTERS.JOB_CHARGE_CODE,
            JOB_GROUP_FILTERS.JOB_FTE_REFERENCE_DIARY,
            JOB_GROUP_FILTERS.JOB_UPDATED_ON,
            JOB_GROUP_FILTERS.JOB_UPDATED_BY,
            JOB_GROUP_FILTERS.JOB_CREATED_ON,
            JOB_GROUP_FILTERS.JOB_CREATED_BY,
            JOB_GROUP_FILTERS.JOB_START,
            JOB_GROUP_FILTERS.JOB_END,
            JOB_GROUP_FILTERS.JOB_FTE_TOTAL,
            JOB_GROUP_FILTERS.JOB_IS_CONFIDENTIAL,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_UTILIZATION,
            JOB_GROUP_FILTERS.JOB_IS_EXCLUDE_FROM_BILLABILITY,
            JOB_GROUP_FILTERS.JOB_I_MANAGE
        ],
        visible: true
    }
];

const resourcesPageFilterGroupsConfig = [
    {
        title: 'Resource',
        tableName: TABLE_NAMES.RESOURCE,
        operator: '',
        icon: 'user',
        filters: [...resourceFilters]
    }
];

export {
    plannerPageFilterGroupsConfig,
    jobsPageFilterGroupsConfig,
    jobsPageBaseFilterGroupsConfig,
    roleInboxPageFilterGroupsConfig,
    roleInboxPageBaseFilterGroupsConfig,
    peopleFinderFilterGroupsConfig,
    marketplacePageFilterGroupsConfig,
    marketplacePageBaseFilterGroupsConfig,
    tableViewPageFilterGroupsConfig,
    jobFilterDialogFilterGroupConfig,
    massDuplicateJobsFilterGroupConfig,
    resourcesPageFilterGroupsConfig
};