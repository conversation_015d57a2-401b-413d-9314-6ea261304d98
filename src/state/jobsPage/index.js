import createPagedDataModel from '../pagedData';
import { createFilterPaneModel } from '../filterPane/filterPane';
import {
    jobsPageFilterGroupsConfig,
    jobsPageBaseFilterGroupsConfig
} from '../filterPane/pagesFilterGroupsConfig';
import { jobsPageCommandBarConfig } from '../commandBar/jobsPageCommandBar';
import {
    TABLE_NAMES,
    DEFAULT_BASE_FILTER_OPTION
} from '../../constants/globalConsts';

export default {
    pageState:{},
    uiOptions: {},
    tableName: TABLE_NAMES.JOB,
    pagedData: {
        job: createPagedDataModel(TABLE_NAMES.JOB, TABLE_NAMES.JOB, 20)
    },
    tableDatas: {},
    defaultSortOrder:
        {
            field: 'job_description',
            order: 'Ascending'
        }
    ,
    selection: {
        fields: [],
        order: {
            orderFields: [
                {
                    field: 'job_description',
                    order: 'Ascending'
                }
            ]
        },
        filter: {

        }
    },
    filters: {
        job: {
            ...createFilterPaneModel(
                TABLE_NAMES.JOB,
                TABLE_NAMES.JOB,
                '',
                jobsPageFilterGroupsConfig
            ),
            title: TABLE_NAMES.JOB,
            baseFilter: {
                applied: false,
                selectedBaseFilter: DEFAULT_BASE_FILTER_OPTION,
                ...createFilterPaneModel(
                    TABLE_NAMES.JOB,
                    TABLE_NAMES.JOB,
                    '',
                    jobsPageBaseFilterGroupsConfig
                )
            }
        }
    },
    fieldOptions: {
        job_guid: {
            loaded: false
        },
        job_description: {
            loaded: false
        },
        job_jobstatus_guid: {
            loaded: false
        },
        job_client_guid: {
            loaded: false
        },
        job_start: {
            loaded: false
        },
        job_end: {
            loaded: false
        },
        job_totalactionablerequests: {
            loaded: false
        },
        job_totalrolegroups: {
            loaded: false
        },
        job_chargetype_guid: {
            loaded: false
        },
        job_totalprofit: {
            loaded: false
        },
        job_engagementlead_resource_guid: {
            loaded: false
        },
        job_updatedby_resource_guid: {
            loaded: false
        }
    },
    displayFields: [],
    linkFields: ['job_description', 'job_totalrolegroups'],
    commandBarConfig : jobsPageCommandBarConfig,
    roleGroupCreationModal: {
        visible: false,
        entityId: null,
        entityName: null,
        surrogateId: null
    }
};
