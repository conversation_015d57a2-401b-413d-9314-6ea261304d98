import { COMMAND_BAR_WORKSPACES_SECTION, SET_CONFIG } from "../../actions/actionTypes";
import { COMMAND_BAR_MENUS_COMPONENT_TYPES, COMMAND_BAR_MENUS_SECTION_KEYS } from "../../constants/commandBarConsts";
import { getMenusSectionIndexByKey } from "../../utils/commandBarUtils";
import initialState from "../initialState";

const { DIVIDER, MENU_ITEM } = COMMAND_BAR_MENUS_COMPONENT_TYPES;

const getMenuItemLabel = (labelTemplate, label) => {
    return labelTemplate ? {labelTemplate, label} : {label};
};

export const getWorkspacesSectionMenuItem = (menuItemProps) => {
    const { labelTemplate, label, onClickActionType = null, plansSectionPlanGuid, style = {}, icon = null, className = "" } = menuItemProps;

    return {
        ...getMenuItemLabel(labelTemplate, label),
        type: MENU_ITEM,
        onClickActionType,
        plansSectionPlanGuid,
        style,
        icon,
        className
    }
}

export const getWorkspacesSectionSaveChangesButton = (labels) => {
    let button = {};
    
    button = {
        label: labels.saveChangesToPublicLabel,
        labelTemplate: 'saveChangesToPublicLabel',
        type: MENU_ITEM,
        onClickActionType: '' // to be replaced with the action for saving changes to public
    };

    return button;
}

export const getPlansSectionSaveAsNewWorkspaceButton = (labels) => {
    let button = {};

    button = {
        label: labels.saveAsNewWorkspaceLabel,
        labelTemplate: 'saveAsNewWorkspaceLabel',
        type: MENU_ITEM,
        onClickActionType: '' // to be replaced with the action for saving as new workspace
    };

    return button;
};

export function workspacesSectionModelCreator (publicWorkspaces = [], privateWorkspaces = [], messages) {
    const { defaultWorkspaceLabel, newWorkspaceLabel, saveChangesToPublicLabel, saveAsNewWorkspaceLabel, manageMyWorkspacesLabel, privateWorkspacesLabel, publicWorkspacesLabel, noPublicWorkspacesCreatedLabel, noPrivateWorkspacesCreatedLabel } = messages;
    if(publicWorkspaces.length === 0){
        publicWorkspaces.push(getWorkspacesSectionMenuItem({ labelTemplate: 'noPublicWorkspacesCreatedLabel', label: noPublicWorkspacesCreatedLabel }));
    }
    if(privateWorkspaces.length === 0){
        privateWorkspaces.push(getWorkspacesSectionMenuItem({ labelTemplate: 'noPrivateWorkspacesCreatedLabel', label: noPrivateWorkspacesCreatedLabel }));
    }

    return {
        label: defaultWorkspaceLabel,
        type: 'Menu',
        closedIcon: "down",
        openIcon: "up",
        manageMyPlansWindowVisible: false,
        triggerSubMenuAction: "click",
        items: [
            {
                label: newWorkspaceLabel,
                labelTemplate: 'newWorkspaceLabel',
                type: MENU_ITEM,
                onClickActionType: '', // to be replaced with the action for creating a new workspace
                icon: {
                    type: 'plus',
                    position: 'left'
                }
            },
            { type: DIVIDER },
            getWorkspacesSectionSaveChangesButton({saveChangesToPublicLabel}),
            getPlansSectionSaveAsNewWorkspaceButton({ saveAsNewWorkspaceLabel }),
            { type: DIVIDER },
            {
                label: manageMyWorkspacesLabel,
                labelTemplate: 'manageMyWorkspacesLabel',
                type: MENU_ITEM,
                onClickActionType: '', // to be replaced with the action for managing my workspaces,
                showEllipsis: true
            },
            { type: DIVIDER },
            {
                label: defaultWorkspaceLabel,
                labelTemplate: 'defaultWorkspaceLabel',
                type: MENU_ITEM,
                onClickActionType: '', // to be replaced with the action for selecting the default workspace,
                icon: {
                    type: 'team',
                    position: 'left'
                }
            },
            { type: DIVIDER },
            {
                label: privateWorkspacesLabel,
                labelTemplate: 'privateWorkspacesLabel',
                type: 'SubMenu',
                items: privateWorkspaces,
                scroll: true
            },
            {
                label: publicWorkspacesLabel,
                labelTemplate: 'publicWorkspacesLabel',
                type: 'SubMenu',
                items: publicWorkspaces,
                scroll: true
            }
        ]
    };
};

const plansSectionIndex = getMenusSectionIndexByKey(initialState.jobsPage.commandBarConfig.menusSection, COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);
export default function workspacesSectionReducer(state = initialState.jobsPage.commandBarConfig.menusSection[plansSectionIndex], action) {
    switch (action.type) {
        case COMMAND_BAR_WORKSPACES_SECTION.POPULATE: {
            
            const workspacesMessages = state.workspacesMessages;

            return {
                ...state,
                ...workspacesSectionModelCreator([], [], workspacesMessages)
            };
        }
        case SET_CONFIG: {
            const { config } = action.payload;

            return {
                ...state,
                ...config.menusSection[plansSectionIndex]
            };
        }
        default:
            return state;
    }
}