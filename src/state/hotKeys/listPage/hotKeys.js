import HOT_KEYS_ACTION_TYPES from './actionTypes';
import { HOT_KEYS_STRATEGY_NAMES, OS } from '../../../constants/hotKeysConsts';

export default {
    [HOT_KEYS_ACTION_TYPES.ADD_JOB]: {
        [OS.CROSS_PLATFORM]: [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['j']
            },
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.COMBINATION,
                values: ['Shift', 'J'],
                aliases: ['Shift', 'j']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_JOB]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['e']
            },
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.COMBINATION,
                values: ['Shift', 'E'],
                aliases: ['Shift', 'e']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.DUPLICATE_JOB]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.COMBINATION,
                values: ['Shift', 'd'],
                aliases: ['Shift', 'D']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.DELETE_JOB]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['Delete']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_RESOURCE]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['e']
            },
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.COMBINATION,
                values: ['Shift', 'E'],
                aliases: ['Shift', 'e']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.ADD_MENU]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SEQUENCE,
                values: ['m', 'a'],
                aliases: ['m', 'a']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_MENU]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SEQUENCE,
                values: ['m', 'e'],
                aliases: ['m', 'e']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_COMPACT]: {
        [OS.CROSS_PLATFORM]: [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['0']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_DEFAULT]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['1']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_EXPANDED]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.SINGLE_KEY,
                values: ['2']
            }
        ]
    },
    [HOT_KEYS_ACTION_TYPES.SHOW_HELP]: {
        [OS.CROSS_PLATFORM]:  [
            {
                strategy: HOT_KEYS_STRATEGY_NAMES.COMBINATION,
                values: ['Shift', '?'],
                aliases: ['?']
            }
        ]
    }
}