import HOT_KEYS_ACTION_TYPES from './actionTypes';
import HOT_KEYS from './hotKeys';
import { HOT_KEYS_HELP_WINDOW_SECTIONS } from '../../../constants/hotKeysConsts';
import { getHotKeysConfigByStrategy } from '../../../selectors/hotKeysSelectors';
import { getHotKeys } from '../utils';
import { ENTITY_ACCESS_TYPES, ENTITY_ACTION_KEYS } from '../../../constants/entityAccessConsts';
import { TABLE_NAMES } from '../../../constants/globalConsts';


const {
    MENUS: MENUS_SECTION_NAME,
    JOBS: JOBS_SECTION_NAME,
    DISPLAY_DENSITY: DISPLAY_DENSITY_SECTION_NAME,
    RESOURCES: RESOURCES_SECTION_NAME,
    HELP: HELP_SECTION_NAME
} = HOT_KEYS_HELP_WINDOW_SECTIONS;

export const listPageHotKeysConfig = {
    [HOT_KEYS_ACTION_TYPES.ADD_JOB]: {
        name: '##key##addLabel###Add',
        section: JOBS_SECTION_NAME,
        functionalAccess: 'CreateJob',
        entityAccess: ENTITY_ACCESS_TYPES.CREATE,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.ADD_JOB),
        tableName: TABLE_NAMES.JOB
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_JOB]: {
        name: '##key##editLabel###Edit',
        section: JOBS_SECTION_NAME,
        functionalAccess: 'EditJob',
        entityAccess: ENTITY_ACCESS_TYPES.EDIT,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.EDIT_JOB),
        tableName: TABLE_NAMES.JOB
    },
    [HOT_KEYS_ACTION_TYPES.DUPLICATE_JOB]: {
        name: '##key##duplicateLabel###Duplicate Job',
        section: JOBS_SECTION_NAME,
        actionKey: ENTITY_ACTION_KEYS.DUPLICATE_JOB,
        functionalAccess: 'CreateBooking',
        entityAccess: ENTITY_ACCESS_TYPES.CREATE,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.DUPLICATE_JOB),
        tableName: TABLE_NAMES.JOB
    },
    [HOT_KEYS_ACTION_TYPES.DELETE_JOB]: {
        name: '##key##deleteLabel###Delete',
        section: JOBS_SECTION_NAME,
        functionalAccess: 'DeleteJob',
        entityAccess: ENTITY_ACCESS_TYPES.DELETE,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.DELETE_JOB),
        tableName: TABLE_NAMES.JOB
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_RESOURCE]: {
        name: '##key##editLabel###Edit',
        section: RESOURCES_SECTION_NAME,
        functionalAccess: 'EditResource',
        entityAccess: ENTITY_ACCESS_TYPES.EDIT,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.EDIT_RESOURCE),
        tableName: TABLE_NAMES.RESOURCE
    },
    [HOT_KEYS_ACTION_TYPES.ADD_MENU]: {
        name: '##key##addMenuLabel###Add menu',
        section: MENUS_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.ADD_MENU)
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_MENU]: {
        name: '##key##editMenuLabel###Edit menu',
        section: MENUS_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.EDIT_MENU)
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_COMPACT]: {
        name: '##key##compactDensityLabel###Set display density to compact',
        section: DISPLAY_DENSITY_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_COMPACT)
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_DEFAULT]: {
        name: '##key##defaultDensityLabel###Set display density to default',
        section: DISPLAY_DENSITY_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_DEFAULT)
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_EXPANDED]: {
        name: '##key##expandedDensityLabel###Set display density to expanded',
        section: DISPLAY_DENSITY_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_EXPANDED)
    },
    [HOT_KEYS_ACTION_TYPES.SHOW_HELP]: {
        name: '##key##helpWindowLabel###Help window',
        section: HELP_SECTION_NAME,
        hotKeys: getHotKeys(HOT_KEYS, HOT_KEYS_ACTION_TYPES.SHOW_HELP)
    }
};

export const listPageHotKeysConfigByStrategy = getHotKeysConfigByStrategy(listPageHotKeysConfig);