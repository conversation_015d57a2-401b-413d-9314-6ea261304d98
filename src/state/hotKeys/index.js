import { jobsPageHotKeysConfig } from './jobsPage/hotKeysConfig';
import { plannerPageHotKeysConfig } from './plannerPage/hotKeysConfig';
import { adminPageHotKeysConfig } from './adminPage/hotKeysConfig';
import { JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS, adminSettingConsts } from '../../constants';
import { ROLE_INBOX_PAGE_ALIAS } from '../../constants/roleInboxPageConsts';
import { roleInboxHotKeysConfig } from './roleInbox/hotKeysConfig';
import { LIST_PAGE_ALIAS } from '../../constants/listPageConsts';
import { listPageHotKeysConfig } from './listPage/hotKeysConfig';

export const HOT_KEYS_PAGE_CONFIG_MAP = {
    [JOBS_PAGE_ALIAS]: jobsPageHotKeysConfig,
    [LIST_PAGE_ALIAS]: listPageHotKeysConfig,
    [PLANNER_PAGE_ALIAS]: plannerPageHotKeysConfig,
    [adminSettingConsts.ADMIN_SETTINGS_ALIAS]: adminPageHotKeysConfig,
    [ROLE_INBOX_PAGE_ALIAS]: roleInboxHotKeysConfig
};