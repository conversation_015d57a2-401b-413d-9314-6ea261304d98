import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import ReviewSection from '../../lib/reviewSection/reviewSection';

/**
 * Redux container for connecting ReviewSection with global state.
 *
 * @param {Object} props
 * @param {number} props.reviewCount
 * @param {function} props.fetchReviews
 * @param {function} props.submitReview
 */
class ConnectedReviewSection extends React.Component {
    static propTypes = {
        reviewCount: PropTypes.number.isRequired,
        fetchReviews: PropTypes.func.isRequired,
        submitReview: PropTypes.func.isRequired
    };

    handleSubmitReview = () => {
        // this.props.submitReview();
        // TBD
        console.log('Settings clicked');
    };

    render() {
        const { reviewCount } = this.props;

        return (
            <ReviewSection
                reviewCount={reviewCount}
                onSubmitReview={this.handleSubmitReview}
            />
        );
    }
}

const mapStateToProps = (state) => ({
    reviewCount: state?.reviews?.reviewCount || 0 // Assuming reviewCount is stored in state.reviews
});

const mapDispatchToProps = {
    fetchReviews: () => { }, // Placeholder for actual fetchReviews action
    submitReview: () => { } // Placeholder for actual submitReview action
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(ConnectedReviewSection);
