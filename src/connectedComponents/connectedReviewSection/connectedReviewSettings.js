import { connect } from 'react-redux';
import { getEntityWindowReviewSettingsSelector } from '../../selectors/entityWindowSelectors';
import ReviewSettings from '../../lib/reviewSection/reviewSettings';

const mapStateToProps = (state) => {
    const {
        headerTitle = '',
        saveChangesButtonLabel = '',
        cancelBtnLabel = '',
        resourceSkillsReviewLabel = '',
        reviewerOnThisJobLabel = '',
        eligibleForReviewLabel = '',
        reviewerOnThisJobCaptionLabel = '',
        pastBookingLabel = '',
        allBookedLabel = '',
        clearSkillsReviewsLabel = ''
    } = getEntityWindowReviewSettingsSelector(state);

    return {
        headerProps: {
            title: headerTitle
        },
        footerProps: {
            saveChangesButtonLabel,
            cancelBtnLabel
        },
        bodyProps: {
            resourceSkillsReviewLabel,
            reviewerOnThisJobLabel,
            eligibleForReviewLabel,
            reviewerOnThisJobCaptionLabel,
            pastBookingLabel,
            allBookedLabel,
            clearSkillsReviewsLabel
        }
    };
};

const mapDispatchToProps = (dispatch) => {
    return {};
};

const mergeProps = (stateProps, dispatchProps, ownProps) => {
    return {
        ...ownProps,
        ...stateProps,
        ...dispatchProps
    };
};

const ConnectedReviewSettings = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ReviewSettings);

export { ConnectedReviewSettings };