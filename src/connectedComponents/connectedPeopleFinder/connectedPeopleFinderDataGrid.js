import React from 'react';
import { connect } from 'react-redux';
import { PEOPLE_FINDER_DIALOG_ALIAS } from '../../constants/peopleFinderConst';
import { DataGrid } from '../../lib/dataGrid';
import { commonDataGridMapStateToProps } from '../connectedDataGrid/common/mapStateToProps';
import { createDataGridColumnsPropsBuilder } from '../connectedDataGrid/common/columnBuilders';
import { getFieldPrefixSelector, getFieldSuffixSelector } from '../../selectors/tableFieldsSelectors';
import { getColumnAvatarConfig, getContextualDropdownStaticMessagesSelector } from '../../selectors/dataGridPageSelectors';
import { PAGINATION_KEY } from '../../constants/jobsPageConsts';
import { getApplicationFNAs, hasFunctionalAccessLocal } from '../../selectors/applicationFnasSelectors';
import { getAccessibleEntitiesIdsSelector } from '../../selectors/userEntityAccessSelectors';
import { getEntityInfoSelector } from '../../selectors/entityStructureSelectors';
import { getEntityAlias } from '../../utils/entityStructureUtils';
import { commonDataGridMergeProps } from '../connectedDataGrid/common/mergeProps';
import { compose } from 'redux';
import { configuredWithEmptyStates } from '../../components/containers/hocs/emptyStates';
import { DATA_GRID_IDS, DATA_GRID_PAGED_DATA_SUFFIX, PAGINATION_TYPE } from '../../constants/dataGridConsts';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { commonDataGridMapDispatchToProps } from '../connectedDataGrid/common/mapDispatchToProps';
import { selectEdits } from '../../actions/tableDataActions';
import { RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMERED, RESOURCE_CMEYELLOW, RESOURCE_DESCRIPTION, RESOURCE_FIELDS } from '../../constants/fieldConsts';
import { TABLE_NAMES, ENTITY_WINDOW_MODULES } from '../../constants';
import { ResourceInfoCell } from '../../lib/dataGrid/entryRenderers/customRenderers';
import store from '../../store/configureStore';
import { getSelectedPeopleFinderActionSelector } from '../../selectors/peopleFinderSelectors';
import { entityWindowOpen } from '../../actions/entityWindowActions';
import { PLANNER_PAGE_ALIAS } from '../../constants/plannerConsts';
import { promptAction } from '../../actions/promptActions';
import { withScrollPagination } from '../../components/containers/hocs/withScrollPagination';
import { DATA_GRID_NO_MATCHING_ITEMS_STATE } from '../../constants/dataGridConsts';
import { getEmptyStateConfig } from '../../utils/commonDataGridUtils';
import { populateStringTemplates, translateConfig } from '../../utils/translationUtils';
import { ENTITY_WINDOW_OPERATIONS } from '../../constants/entityWindowConsts';
import { dataGridSortChanged } from '../../actions/dataGridActions';
import { clearPagedDataAction } from '../../actions/pagedDataActions';
import { PROFILE_PAGE } from '../../pages/pages';
import { copyUrlToClipboard, getCopyProfileUrl, getEntityWindowDataBasedOnAlias } from '../../utils/peopleFinderUtils';
import { peopleFinderSetSuccessToaster } from '../../actions/peopleFinderActions';
import { AvailabilityColorBadge } from '../../lib/percentColorBadges';
import { ARIA_ATTRIBUTES, LICENSE_KEYS_ADMIN_SETTINGS } from '../../constants/globalConsts';
import { getLicenseValuesByKeySelector, getRowAriaLabelSelector } from '../../selectors/commonSelectors';
import { SkillExpiryBadge } from '../../components/skillExpiryBadge/skillExpiryBadge';

export const mapStateToProps = (state) => {
    const commonGridProps = commonDataGridMapStateToProps(PEOPLE_FINDER_DIALOG_ALIAS, state, PAGINATION_TYPE.VERTICAL_SCROLL);
    const {
        selectedRowKeys = [],
        tableName, loading,
        getIsLinkField,
        getIsColumnSortable,
        getIsDynamicComponent,
        dataSource,
        rowCount
    } = commonGridProps;

    const fnas = getApplicationFNAs(state);
    const dataPage = state[PEOPLE_FINDER_DIALOG_ALIAS];

    const getIsLinkCell = (fieldName, cellData) => {
        if (selectedRowKeys.length > 0 && !selectedRowKeys.includes(cellData[`${tableName}_guid`])) return false;

        return getIsLinkField(fieldName);
    };

    const getCellClassName = () => '';

    const getFieldSuffixWrapped = getFieldSuffixSelector(state);
    const getFieldSuffix = (fieldInfo) => getFieldSuffixWrapped(fieldInfo);

    const contextualDropdownLabels = getContextualDropdownStaticMessagesSelector(state);

    const getUserHasFunctionalAccess = (functionAccess) => hasFunctionalAccessLocal(fnas, functionAccess);
    const getAccessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state);
    const getEntityInfo = getEntityInfoSelector(state);
    const resourceEntityInfo = getEntityInfo(TABLE_NAMES.RESOURCE);
    const resourceEntityAlias = getEntityAlias(resourceEntityInfo, { singularForm: false, capitalized: false, fallbackValue: TABLE_NAMES.RESOURCE });

    const renderVerticalLoadingRow = dataSource.length < rowCount;

    const buildRoleResourceDetailsCellProps = (displayValue, cellData, additionalProps) => {
        const { avatarConfig, getCellClassName } = additionalProps;

        const cellValue = {
            [RESOURCE_FIELDS.DESCRIPTION]: displayValue,
            [RESOURCE_FIELDS.GUID]: cellData[RESOURCE_FIELDS.GUID],
            [RESOURCE_CMERED]: cellData[RESOURCE_CMERED],
            [RESOURCE_CMEBLUE]: cellData[RESOURCE_CMEBLUE],
            [RESOURCE_CMEGREEN]: cellData[RESOURCE_CMEGREEN],
            [RESOURCE_CMEYELLOW]: cellData[RESOURCE_CMEYELLOW]
        };

        const cellClassName = getCellClassName(RESOURCE_FIELDS.GUID, cellData);

        return {
            avatarConfig,
            value: cellValue,
            descriptionField: RESOURCE_FIELDS.DESCRIPTION,
            avatarIdField: RESOURCE_FIELDS.GUID,
            getFieldInfo: () => { },
            tableName: TABLE_NAMES.RESOURCE,
            config: { width: 200, showLoading: false, showDescription: false, colSpan: [8, 16], cellClassName }
        };
    };

    const getDynamicComponent = (displayValue, data, fieldName, isLink, onClick) => {
        const { licenseCmeEnabled } = LICENSE_KEYS_ADMIN_SETTINGS;
        const cMeEnabled = (getLicenseValuesByKeySelector(state)(licenseCmeEnabled) || {}).subscribedCount;

        if (fieldName === RESOURCE_FIELDS.SKILL_EXPIRY_STATUS) {
            return (<SkillExpiryBadge skillExpiryStatus={displayValue} />);
        }

        if (fieldName === RESOURCE_FIELDS.AVAILABILITY) {
            const resourceSummaryStaticMessages = getTranslationsSelector(state, { sectionName: 'resourceSummarySection' });
            const availability = Math.round(displayValue);
            const available_time = Math.round(data[RESOURCE_FIELDS.AVAILABLE_TIME]);
            const badgeProps = { availability, available_time, staticMessages: resourceSummaryStaticMessages };

            return (<AvailabilityColorBadge {...badgeProps} />);
        }

        if (fieldName === RESOURCE_FIELDS.DESCRIPTION) {
            const cellProps = {
                isLink,
                onClick,
                colourProfiling: true,
                cMeEnabled,
                ...buildRoleResourceDetailsCellProps(displayValue, data, { avatarConfig: getColumnAvatarConfig(state), getCellClassName })
            };

            return (<ResourceInfoCell {...cellProps} />);
        }
    };

    const getEmptyConfigMessages = (state, emptyStateConfig) => {
        const { noMatchingItemsState = {} } = emptyStateConfig;
        const { internationalization } = state;
        const translatedEmptyStateConfigNoMatching = translateConfig(noMatchingItemsState.props, noMatchingItemsState.props, internationalization.translation.dataGrid);
        const peopleFinderStaticLabels = populateStringTemplates(translatedEmptyStateConfigNoMatching, { resourceEntityAlias });

        const translatedEmptyStateConfig = {
            ...emptyStateConfig,
            noMatchingItemsState: {
                ...noMatchingItemsState,
                props: peopleFinderStaticLabels
            }
        };

        return translatedEmptyStateConfig;
    };

    const getEmptyStateConfigKey = (dataPage, loading) => {
        const { pagedData, tableName } = dataPage;
        let state = null;

        if (!loading && pagedData[tableName].rowCount === 0) {
            state = DATA_GRID_NO_MATCHING_ITEMS_STATE;
        }

        return state;
    };

    const cellsBuilderUtils = {
        getIsLinkCell,
        getCellClassName,
        getFieldPrefix: getFieldPrefixSelector(state),
        getFieldSuffix,
        getFieldBlankValue: () => null,
        getDynamicComponent: getDynamicComponent,
        getIsDynamicComponent: getIsDynamicComponent,
        getIsColumnSortable: getIsColumnSortable
    };

    const peopleFinderTranslatedMessages = getTranslationsSelector(state, { sectionName: PEOPLE_FINDER_DIALOG_ALIAS });
    const getSelectRowAriaLabel = getRowAriaLabelSelector(state);

    return {
        ...commonGridProps,
        gridColumnsPropsBuilder: createDataGridColumnsPropsBuilder(PEOPLE_FINDER_DIALOG_ALIAS, [])(state, cellsBuilderUtils),
        emptyStateConfig: getEmptyConfigMessages(state, getEmptyStateConfig(PEOPLE_FINDER_DIALOG_ALIAS)),
        emptyStateConfigKey: getEmptyStateConfigKey(dataPage, loading),
        useTablePagination: false,
        withoutCustomPagination: true,
        paginationKey: PAGINATION_KEY,
        className: 'peopleFinderGrid',
        getUserHasFunctionalAccess,
        getAccessibleEntitiesIds,
        getEntityInfo,
        contextualDropdownLabels,
        enableMultipleSelection: dataPage.enableMultipleSelection,
        scroll: { x: true, y: 250 },
        expandedRowRender: null,
        peopleFinderTranslatedMessages,
        alias: dataPage.configAlias,
        renderVerticalLoadingRow: renderVerticalLoadingRow,
        tableParentVisible: dataPage.visible,
        getSelectRowAriaLabel,
        id: DATA_GRID_IDS.PEOPLE_FINDER_GRID_ID
    };
};

export const mapDispatchToProps = (dispatch) => {
    return {
        ...commonDataGridMapDispatchToProps(PEOPLE_FINDER_DIALOG_ALIAS, dispatch),
        dispatchDataGridSortChange: (fieldName) => {
            dispatch(dataGridSortChanged(PEOPLE_FINDER_DIALOG_ALIAS, fieldName));
            dispatch(clearPagedDataAction(`${PEOPLE_FINDER_DIALOG_ALIAS}_${DATA_GRID_PAGED_DATA_SUFFIX}`, TABLE_NAMES.RESOURCE));
        },
        dispatchOnRowCheckboxClicked: (tableDataAlias, tableName, editableGuids) => {
            dispatch(
                selectEdits(
                    tableDataAlias,
                    {
                        dataGuid: tableName,
                        editableGuids
                    }
                )
            );
        },
        dispatchOnContextualOpenEntityWindowClick: (data, tableName) => {
            const state = store.getState();
            const { entityWindowData, collectionAlias } = getSelectedPeopleFinderActionSelector(state)({ tableName, resourceGuid: data[`${TABLE_NAMES.RESOURCE}_guid`] });

            const dispatchAction = entityWindowOpen(
                ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED,
                tableName,
                collectionAlias,
                ENTITY_WINDOW_OPERATIONS.CREATE,
                entityWindowData,
                undefined,
                false
            );
            dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
        },
        dispatchOnEditEntityWindowClick: (cellFieldName, rowEntity, tableName, operation, alias) => {
            const entityId = rowEntity[`${tableName}_guid`];
            const { entityWindowModule = '', collectionAlias = '' } = getEntityWindowDataBasedOnAlias(alias);
            dispatch(
                entityWindowOpen(
                    entityWindowModule,
                    tableName,
                    collectionAlias,
                    operation,
                    {},
                    entityId
                )
            );
        },
        dispatchOnContextualGoToProfileClick: (userId) => {
            const url = getCopyProfileUrl(userId, PROFILE_PAGE.navigationLink);
            window.open(url, '_blank', 'noopener,noreferrer');
        },
        dispatchOnContextualCopyProfileUrlClick: (userId, message) => {
            const url = getCopyProfileUrl(userId, PROFILE_PAGE.navigationLink);
            copyUrlToClipboard(url);
            dispatch(
                peopleFinderSetSuccessToaster(message)
            );
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    const { getData, tableName, getEntityInfo, getUserHasFunctionalAccess, getAccessibleEntitiesIds, contextualDropdownLabels, peopleFinderTranslatedMessages, commonTranslatedMessages, alias } = propsFromState;
    const { dispatchOnContextualOpenEntityWindowClick, dispatchOnEditEntityWindowClick, dispatchOnContextualGoToProfileClick, dispatchOnContextualCopyProfileUrlClick } = propsFromDispatch;

    const getContextualProps = ({ selectedRowKeys = [] }) => (data) => {

        return {
            actions: {
                createBookingPeopleFinder: () => dispatchOnContextualOpenEntityWindowClick(data, TABLE_NAMES.BOOKING),
                createRoleByNamePeopleFinder: () => dispatchOnContextualOpenEntityWindowClick(data, TABLE_NAMES.ROLEREQUEST),
                editResource: () => {
                    dispatchOnEditEntityWindowClick(
                        {},
                        getData(tableName, data[`${tableName}_guid`]),
                        tableName,
                        ENTITY_WINDOW_OPERATIONS.EDIT,
                        alias
                    );
                },
                goToProfile: () => dispatchOnContextualGoToProfileClick(data.resource_surrogate_id),
                copyProfileUrl: () => dispatchOnContextualCopyProfileUrlClick(data.resource_surrogate_id, peopleFinderTranslatedMessages.profileUrlCopied)
            },
            labels: contextualDropdownLabels,
            actionProps: {
                entitiesIds: [data[`${tableName}_guid`]]
            },
            getEntityInfo,
            getAccessibleEntitiesIds,
            getUserHasFunctionalAccess,
            disabled: selectedRowKeys.length > 0 && !selectedRowKeys.includes(data[`${tableName}_guid`]),
            alias,
            rowKey: data.key
        };
    };

    const dataGridRowSelectionProps = {
        fixed: true,
        columnTitle: (<></>),
        getCheckboxProps: (rowEntity) => {
            const { paginationKey, selectedRowKeys } = propsFromState;
            const { key } = rowEntity;
            const disableBasedOnSelection = selectedRowKeys.length > 0 && !selectedRowKeys.includes(key);
            const selectRowAriaLabelText = propsFromState.getSelectRowAriaLabel(rowEntity[RESOURCE_DESCRIPTION]);

            return {
                disabled: key === paginationKey || disableBasedOnSelection,
                [ARIA_ATTRIBUTES.ARIA_LABEL]: selectRowAriaLabelText
            };
        },
        selectedRowKeys: propsFromState.selectedRowKeys,
        onChange: (selectedRowKeys) => {
            //to make it select only one resouce for time being
            if (selectedRowKeys.length > 1) return;

            propsFromDispatch.dispatchOnRowCheckboxClicked(
                `${PEOPLE_FINDER_DIALOG_ALIAS}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                propsFromState.tableName,
                selectedRowKeys
            );
        }
    };

    const columns = propsFromState.gridColumnsPropsBuilder({
        getData,
        getFieldInfo: propsFromState.wrappedGetFieldInfo,
        dispatchDataGridSortChange: propsFromDispatch.dispatchDataGridSortChange,
        dispatchOnCellClick: (cellField, data, tableName, operation = ENTITY_WINDOW_OPERATIONS.READ) => {
            propsFromDispatch.dispatchOnEditEntityWindowClick(cellField, data, tableName, operation, alias);
        },
        getContextualProps: getContextualProps(propsFromState),
        addOptionsColumn: false,
        fixedColumnWidth: 250
    });

    return {
        ...commonDataGridMergeProps(propsFromState, propsFromDispatch, ownProps),
        columns,
        dataGridRowSelectionProps
    };
};

const ConnectedPeopleFinderDataGrid = compose(
    connect(
        mapStateToProps,
        mapDispatchToProps,
        mergeProps
    ),
    configuredWithEmptyStates,
    withScrollPagination
)(DataGrid);
export default ConnectedPeopleFinderDataGrid;