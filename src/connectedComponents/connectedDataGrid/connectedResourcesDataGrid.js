import React from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import { batchActions } from 'redux-batched-actions';
import { getFieldInfo, getTableStructure, getIsValidNotHiddenFieldInfo } from '../../selectors/tableStructureSelectors';
import { getFieldAlias, getData } from '../../utils/commonUtils';

import { dataGridSortChanged, dataGridPageChange } from '../../actions/dataGridActions';
import { entityWindowOpen, entityWindowClose, entityWindowOpenForMultiple } from '../../actions/entityWindowActions';
import { setDetailsPaneCollapsed, setDetailsPaneVisibility, setDetailsPaneSelectedTabIsBatched, setDetailsPaneSelectedTab, setDetailsPaneDisplayTabDot } from '../../actions/detailsPaneActions';

import { DataGrid } from '../../lib/dataGrid';
import { DataGridBodyCell } from '../../lib/dataGrid/bodyCell';
import { DataGridBodyRow } from '../../lib/dataGrid/bodyRow';
import { DataGridHeaderCell } from '../../lib/dataGrid/headerCell';
import { pageSizeChanged } from '../../actions/pagedDataActions';
import { createCellFieldRenderer } from '../../lib/dataGrid/entryRenderers';
import { SORT_ASCENDING, SORT_DESCENDING, TABLE_NAMES } from '../../constants';
import { configuredWithEmptyStates } from '../../components/containers/hocs/emptyStates';
import { withScroll } from '../../components/containers/hocs/scrollableDataGrid';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { DATA_GRID_PAGED_DATA_SUFFIX, DATA_GRID_NO_ITEMS_STATE, DATA_GRID_NO_MATCHING_ITEMS_STATE, DATA_GRID_EMPTY_GRID_STATE, DATA_GRID_PAGE_SIZES_OPTIONS, DATA_GRID_IDS } from '../../constants/dataGridConsts';
import styles from '../../lib/reusableGrid/styles/styles.less';
import { ENTITY_WINDOW_OPERATIONS } from '../../constants/entityWindowConsts';
import { getFieldPrefixSelector, getFieldSuffixSelector } from '../../selectors/tableFieldsSelectors';
import { getResourcesDatagridFieldBlankValueSelector } from '../../selectors/blankValuesSelectors';
import { translateConfig } from '../../utils/translationUtils';
import { getFieldNoValueSetMessage } from '../../utils/fieldControlUtils';
import { getEntityInfoSelector, getEntitySingularAliasSelector } from '../../selectors/entityStructureSelectors';
import { TOTAL_ROLE_GROUPS, ROLE_AND_REQUEST_FEATURE_SWITCH, ARIA_ATTRIBUTES } from '../../constants/globalConsts';
import { selectEdits } from '../../actions/tableDataActions';

import store from '../../store/configureStore';
import { getColumnHeight, getColumnWidth, getDataGridRecordData, getEmptyStateConfig, getTableOptionsColumn } from '../../utils/commonDataGridUtils';
import { getApplicationFNAs, hasFunctionalAccessLocal } from '../../selectors/applicationFnasSelectors';
import { getAccessibleEntitiesIdsSelector } from '../../selectors/userEntityAccessSelectors';
import { getCommonDataGridStaticMessagesSelector, getHasResourcesActionRequired } from '../../selectors/dataGridPageSelectors';
import StaticCountBadge from '../../lib/badges/staticCountBadge';
import { getIsMultipleAssigneesEnabled } from '../../selectors/functionalityConfigurationSelectors';
import { getRowAriaLabelSelector, getStaticTranslatedMessages } from '../../selectors/commonSelectors';
import { isRaghealthField } from '../../utils/fieldUtils';
import { PAGINATION_KEY, RESOURCE_DETAILS_PANE_TAB_KEYS, RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_DP_ALIAS, RESOURCES_PAGE_ENABLE_MULTIPLE_SELECTION } from '../../constants/resourcesPageConsts';
import { RESOURCE_DESCRIPTION } from '../../constants/fieldConsts';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { PROFILE_PAGE } from '../../pages/pages';
import { getCopyProfileUrl } from '../../utils/peopleFinderUtils';

const displayEmptyStates = true;
const {
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_DETAILS_PANE,
    PLANNER_PAGE_MODAL
} = ENTITY_WINDOW_MODULES;
const { RESOURCE_KEY } = RESOURCE_DETAILS_PANE_TAB_KEYS;

const createConnectedDataGrid = (alias, entityWindowModuleName) => {
    const buildGridColumnProps = (state) => ({
        dispatchDescriptionCellClick,
        dispatchContextualResourceDropdownClick,
        dispatchOnContextualGoToProfileClick,
        dispatchDataGridSortChange,
        getFieldInfo,
        getData,
        selectedRowKeys
    }) => {
        const dataPage = state[alias];
        const useMultipleAssignees = getIsMultipleAssigneesEnabled(state);
        const { sortAscending, sortDescending } = getCommonDataGridStaticMessagesSelector(state)(dataPageTableName);

        const {
            displayFields,
            linkFields = [],
            selection,
            fieldOptions,
            tableName: dataPageTableName,
            uiOptions
        } = dataPage;

        const { density } = uiOptions;
        const tableOptionsColumnWidth = 60;
        const fnas = getApplicationFNAs(state);
        const pageAlias = getCurrentPageAliasSelector(state);

        const getUserHasFunctionalAccess = (functionAccess) => hasFunctionalAccessLocal(fnas, functionAccess);
        const getAccessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state);
        const getEntityInfo = getEntityInfoSelector(state);

        const getColumnSort = (fieldName, { orderFields }) => {
            const orderFieldIndex = orderFields.findIndex((orderField) => orderField.field === fieldName);
            let columnSort = {};

            if (orderFieldIndex > -1) {
                const { order } = orderFields[orderFieldIndex];

                columnSort = {
                    sort: order,
                    sortIconColor: 'grey',
                    sortAscending,
                    sortDescending
                };
            }

            return columnSort;
        };

        const getNewSortOrder = (columnSort) => {
            let newOrder = SORT_ASCENDING;

            if (('sort' in columnSort) && columnSort.sort === SORT_ASCENDING) {
                newOrder = SORT_DESCENDING;
            }

            return newOrder;
        };

        const getEmptyColumn = (fieldName, sortOrder) => {
            return {
                onCell: () => {
                    return {
                        width: getColumnWidth()
                    };
                },
                onHeaderCell: () => {
                    return {
                        onClick: () => {
                            const columnSort = getColumnSort(fieldName, sortOrder);

                            dispatchDataGridSortChange(
                                fieldName,
                                getNewSortOrder(columnSort)
                            );
                        }
                    };
                }
            };
        };

        const getActionRequiredColumnCell = (displayValue) => {
            let actionRequiredColumnCell = <span style={{ marginLeft:'10px' }}>-</span>;
            if (displayValue > 0) {
                actionRequiredColumnCell = <StaticCountBadge count={displayValue} />;
            }

            return actionRequiredColumnCell;
        };

        const getContextualProps = (data, contextualDropdownLabels) => {
            return {
                actions: {
                    viewResourceDetails: () => {
                        dispatchContextualResourceDropdownClick(
                            entityWindowModuleName,
                            dataPageTableName,
                            getData(dataPageTableName, data[`${dataPageTableName}_guid`]),
                            selectedRowKeys,
                            ENTITY_WINDOW_OPERATIONS.READ
                        );
                    },
                    editResource: () => {
                        dispatchContextualResourceDropdownClick(
                            entityWindowModuleName,
                            dataPageTableName,
                            getData(dataPageTableName, data[`${dataPageTableName}_guid`]),
                            selectedRowKeys,
                            ENTITY_WINDOW_OPERATIONS.EDIT
                        );
                    },
                    goToProfile: () => dispatchOnContextualGoToProfileClick(data.resource_surrogate_id)
                },
                labels: contextualDropdownLabels,
                actionProps: {
                    entitiesIds: [data[`${dataPageTableName}_guid`]]
                },
                getEntityInfo,
                getUserHasFunctionalAccess,
                getAccessibleEntitiesIds,
                rowKey: data.key,
                pageAlias
            };
        };

        const getOnClickAction = (fieldInfo, data) => {
            let result = null;
            let roleGroupClicked = fieldInfo.name === TOTAL_ROLE_GROUPS;
            let openModule = entityWindowModuleName;
            result = () => {
                dispatchDescriptionCellClick(
                    openModule,
                    dataPageTableName,
                    getData(dataPageTableName, data[`${dataPageTableName}_guid`]),
                    selectedRowKeys,
                    ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                    roleGroupClicked
                );
            };

            return result;
        };

        const getFieldPrefixWrapped = getFieldPrefixSelector(state);
        const getFieldSuffixWrapped = getFieldSuffixSelector(state);
        const getFieldBlankValue = getResourcesDatagridFieldBlankValueSelector(state);
        const getTableAlias = getEntitySingularAliasSelector(state);
        const staticLabels = getStaticTranslatedMessages(state, 'common');
        const contextualDropdownLabels = getStaticTranslatedMessages(state, 'contextualDropdown');
        const fixedColumns = [];
        let omitFields = []; //Omiited this calc field

        return (
            displayFields.filter(field => {
                const fieldInfo = getFieldInfo(field);

                return getIsValidNotHiddenFieldInfo(fieldInfo) && !fieldInfo.excludeFromTableDisplaySettings;
            }).map((fieldName) => {
                const fieldInfo = getFieldInfo(fieldName);
                const alias = getFieldAlias(fieldInfo);
                const columnSort = getColumnSort(fieldName, selection.order);
                const noValueSetMessage = getFieldNoValueSetMessage(fieldInfo, fieldInfo.table, getTableAlias, staticLabels);
                const lowerCaseFieldName = fieldName.toLowerCase();
                const fixed = lowerCaseFieldName === 'resource_description' ? 'left' : false;
                const supportsContextualMenu = lowerCaseFieldName === 'resource_description';
                const isTotalActionableRequestColumn = (lowerCaseFieldName === 'resource_totalactionablerequests');
                const isLink = linkFields.includes(lowerCaseFieldName);
                const isHealth = isRaghealthField(fieldInfo);
                const width = getColumnWidth(200, tableOptionsColumnWidth, displayFields, fixed);
                const isDynamicComponent = !ROLE_AND_REQUEST_FEATURE_SWITCH && isTotalActionableRequestColumn;

                if (fixed) {
                    fixedColumns.push(lowerCaseFieldName);
                }

                return {
                    title: alias,
                    dataIndex: lowerCaseFieldName,
                    key: lowerCaseFieldName,
                    fixed: fixed,
                    onCell: () => {
                        return {
                            width,
                            className: styles.dataGridCell,
                            style: { maxWidth: width, minWidth: width, height: getColumnHeight(density) }
                        };
                    },
                    onHeaderCell: (cell) => {
                        return {
                            isDataCell: true,
                            title: alias,
                            width,
                            style: { maxWidth: width, minWidth: width },
                            onClick: () => {
                                dispatchDataGridSortChange(
                                    fieldName,
                                    getNewSortOrder(columnSort)
                                );
                            },
                            onDragStart: () => {
                                console.log(cell.dataIndex);
                            },
                            onDrop: () => {
                                console.log(cell.dataIndex);
                            },
                            ...columnSort
                        };
                    },
                    render: (text, data) => createCellFieldRenderer(
                        dataPageTableName,
                        getFieldInfo(fieldName),
                        text,
                        {
                            loaded: fieldOptions[fieldName].loaded,
                            supportsContextualMenu,
                            contextualProps: supportsContextualMenu ? getContextualProps(data, contextualDropdownLabels) : {},
                            onClick: getOnClickAction(fieldInfo, data),
                            prefix: getFieldPrefixWrapped(fieldInfo),
                            suffix: getFieldSuffixWrapped(fieldInfo),
                            getFieldBlankValue,
                            noValueSetMessage,
                            isLink: isLink,
                            isHealth,
                            isDynamicComponent,
                            getDynamicComponent: getActionRequiredColumnCell,
                            staticLabels
                        },
                        data
                    )
                };
            })
                .concat(displayFields.length === 1 ? getEmptyColumn(displayFields[0], selection.order) : [])
                .concat(getTableOptionsColumn(tableOptionsColumnWidth, density, TABLE_NAMES.RESOURCE, fixedColumns, omitFields, useMultipleAssignees, RESOURCES_PAGE_ALIAS))
        );
    };

    const mapStateToProps = (state) => {
        const dataPage = state[alias];

        const {
            pagedData,
            uiOptions,
            tableDatas,
            tableName: dataPageTableName
        } = dataPage;

        const wrappedGetFieldInfo = (fieldName) => {
            return getFieldInfo(getTableStructure(state), dataPageTableName, fieldName) || {};
        };

        const { pageNumber } = uiOptions;
        const { pageSize, loading, rowCount, loadedPagesMap, data, currentEdits } = pagedData[dataPageTableName];
        const { pageOptionSuffix } = getCommonDataGridStaticMessagesSelector(state)(dataPageTableName);
        const getDataWrapped = (tableName, id) => {
            const collections = [
                pagedData[dataPageTableName],
                ...Object.keys(tableDatas).map(tableDataKey => tableDatas[tableDataKey])
            ];

            return getData(
                collections,
                tableName,
                id
            );
        };

        const hasAppliedFilters = (filters = {}, tableName) => {
            let { selection, baseFilter } = (filters[tableName] || {});
            if (!selection || Object.keys(selection).length === 0) {
                selection = { [tableName]: [] };
            }
            const baseFilterApplied = (baseFilter || {}).applied;

            return selection[tableName].length >= 0 || baseFilterApplied;
        };

        const getEmptyConfigMessages = (state, emptyStateConfig) => {
            const translatedEmptyStateConfigNoItem = translateConfig(emptyStateConfig.noItemsState.props, emptyStateConfig.noItemsState.props, state.internationalization.translation.dataGrid);
            const translatedEmptyStateConfigNoMatching = translateConfig(emptyStateConfig.noMatchingItemsState.props, emptyStateConfig.noMatchingItemsState.props, state.internationalization.translation.dataGrid);
            const translatedEmptyStateConfig = {
                ...emptyStateConfig,
                noItemsState: {
                    ...emptyStateConfig.noItemsState,
                    props: translatedEmptyStateConfigNoItem
                },
                noMatchingItemsState: {
                    ...emptyStateConfig.noMatchingItemsState,
                    props: translatedEmptyStateConfigNoMatching
                }
            };

            return translatedEmptyStateConfig;
        };

        const getEmptyStateConfigKey = (dataPage, loading) => {
            const { pagedData, filters, tableName } = dataPage;
            let state = null;

            if (!loading && pagedData[tableName].rowCount === 0) {
                state = DATA_GRID_EMPTY_GRID_STATE;

                if (displayEmptyStates) {
                    state = hasAppliedFilters(filters, tableName) ? DATA_GRID_NO_MATCHING_ITEMS_STATE : DATA_GRID_NO_ITEMS_STATE;
                }
            }

            return state;
        };

        const hasRows = rowCount > 0;

        let dataSource = [];
        if (!loading && hasRows) {
            const { from, to } = loadedPagesMap[pageNumber - 1];
            dataSource = data
                .slice(from, to)
                .map(getDataGridRecordData(wrappedGetFieldInfo, dataPageTableName, getDataWrapped));
        }
        const { resourcesPage, entityWindow } = state;
        const { detailsPane = {} } = resourcesPage;
        const { collapsed , selectedTabKey } = detailsPane[RESOURCES_PAGE_DP_ALIAS] || {};

        const refreshBatchDPOnResourceSelect = (entityWindow.window[RESOURCES_PAGE_BATCHED_DETAILS_PANE].visible && currentEdits.length > 0) || (detailsPane.visible && !collapsed);

        const getSelectRowAriaLabel = getRowAriaLabelSelector(state);

        return {
            loading,
            rowCount,
            pageNumber,
            pageSize,
            pageSizesOptions: DATA_GRID_PAGE_SIZES_OPTIONS,
            pageOptionSuffix,
            components: {
                body: {
                    row: DataGridBodyRow,
                    cell: DataGridBodyCell
                },
                header: {
                    cell: DataGridHeaderCell
                }
            },
            gridColumnsPropsBuilder: buildGridColumnProps(state),
            dataSource,
            gridFormat: uiOptions.density,
            wrappedGetFieldInfo,
            tableName: dataPageTableName,
            emptyStateConfig: getEmptyConfigMessages(state, getEmptyStateConfig(alias)),
            emptyStateConfigKey: getEmptyStateConfigKey(dataPage, loading),
            getData: getDataWrapped,
            useTablePagination: false,
            withoutCustomPagination: true,
            selectedRowKeys: currentEdits,
            enableMultipleSelection: RESOURCES_PAGE_ENABLE_MULTIPLE_SELECTION,
            paginationKey: PAGINATION_KEY,
            refreshBatchDPOnResourceSelect,
            className: 'resourcesGrid',
            selectedTabKey,
            renderPaginationRow: true,
            getSelectRowAriaLabel,
            id: DATA_GRID_IDS.CONNECTED_DATA_GRID_ID
        };
    };

    const mapDispatchToProps = (dispatch) => {
        return {
            dispatchDescriptionCellClick: (entityWindowModule, tableName, data, selectedRowKeys, operation = ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT) => {
                const entityId = data[`${tableName}_guid`];
                let isActionRequired = data[`${tableName}_totalactionablerequests`] > 0;
                const state = store.getState();
                const { resourcesPage } = state;
                const { currentEdits } = resourcesPage.pagedData[TABLE_NAMES.RESOURCE];
                let batchOpenModule = RESOURCES_PAGE_BATCHED_DETAILS_PANE;
                let activeTabSelected = RESOURCE_KEY;
                let selectActions = [];

                const shouldClearSelection = selectedRowKeys.indexOf(entityId) === -1;
                if (shouldClearSelection) {
                    dispatch(
                        selectEdits(
                            `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                            {
                                dataGuid: tableName,
                                editableGuids: []
                            }
                        )
                    );
                }

                const useBatchedDetailsPane = selectedRowKeys.indexOf(entityId) !== -1 && currentEdits.length > 1;
                if (useBatchedDetailsPane) {
                    isActionRequired = getHasResourcesActionRequired(currentEdits, resourcesPage.pagedData[TABLE_NAMES.RESOURCE].data);
                    dispatch(
                        entityWindowOpenForMultiple(
                            batchOpenModule,
                            tableName,
                            tableName,
                            operation,
                            [],
                            [...currentEdits]
                        )
                    );
                    dispatch(entityWindowClose(entityWindowModule));
                } else {
                    dispatch(
                        entityWindowOpen(
                            entityWindowModule,
                            tableName,
                            tableName,
                            operation,
                            {},
                            entityId
                        )
                    );
                    dispatch(entityWindowClose(batchOpenModule));
                }

                selectActions.push(setDetailsPaneSelectedTab(RESOURCES_PAGE_DP_ALIAS, activeTabSelected, RESOURCES_PAGE_DP_ALIAS));
                selectActions.push(setDetailsPaneVisibility(true, RESOURCES_PAGE_DP_ALIAS));
                selectActions.push(setDetailsPaneCollapsed(RESOURCES_PAGE_DP_ALIAS, false, RESOURCES_PAGE_DP_ALIAS));
                selectActions.push(setDetailsPaneSelectedTabIsBatched(RESOURCES_PAGE_DP_ALIAS, useBatchedDetailsPane, RESOURCES_PAGE_DP_ALIAS));
                selectActions.push(setDetailsPaneDisplayTabDot(RESOURCES_PAGE_DP_ALIAS, RESOURCE_KEY, isActionRequired, RESOURCES_PAGE_DP_ALIAS));

                dispatch(batchActions(selectActions));

            },
            dispatchContextualResourceDropdownClick: (entityWindowModule, tableName, data, selectedRowKeys, operation = ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT) => {
                const entityId = data[`${tableName}_guid`];
                const shouldClearSelection = selectedRowKeys.indexOf(entityId) === -1;

                if (shouldClearSelection) {
                    dispatch(
                        selectEdits(
                            `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                            {
                                dataGuid: tableName,
                                editableGuids: []
                            }
                        )
                    );
                }

                dispatch(entityWindowClose(PLANNER_PAGE_MODAL));
                dispatch(
                    entityWindowOpen(
                        PLANNER_PAGE_MODAL,
                        tableName,
                        tableName,
                        operation,
                        {},
                        entityId
                    )
                );
            },
            dispatchOnContextualGoToProfileClick: (userId) => {
                const url = getCopyProfileUrl(userId, PROFILE_PAGE.navigationLink);
                window.open(url, '_blank', 'noopener,noreferrer');
            },
            dispatchDataGridSortChange: (fieldName) => {
                dispatch(dataGridSortChanged(alias, fieldName));
            },
            dispatchOnPageChange: (pageNumber, pageSize) => {
                dispatch(dataGridPageChange(alias, pageNumber, pageSize));
            },
            dispatchOnMenuSelect: (tableName, pageSize) => {
                dispatch(pageSizeChanged(
                    `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                    tableName,
                    tableName,
                    pageSize
                ));
            },
            dispatchOnResourceSelected: (tableDataAlias, tableName, editableGuids, refreshBatchDPOnResourceSelect) => {
                const batchOpenModule = RESOURCES_PAGE_BATCHED_DETAILS_PANE;
                const state = store.getState();
                const { resourcesPage } = state;
                let isActionRequired = getHasResourcesActionRequired(editableGuids, resourcesPage.pagedData[TABLE_NAMES.RESOURCE].data);

                dispatch(
                    selectEdits(
                        tableDataAlias,
                        {
                            dataGuid: tableName,
                            editableGuids
                        }
                    )
                );
                if (refreshBatchDPOnResourceSelect) {
                    if (editableGuids && editableGuids.length < 1) {
                        dispatch(setDetailsPaneCollapsed(RESOURCES_PAGE_DP_ALIAS, true, RESOURCES_PAGE_DP_ALIAS));
                    } else {
                        dispatch(
                            entityWindowOpenForMultiple(
                                batchOpenModule,
                                tableName,
                                tableName,
                                ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                                [],
                                [...editableGuids]
                            )
                        );
                    }
                }
                dispatch(entityWindowClose(RESOURCES_PAGE_DETAILS_PANE));
                dispatch(setDetailsPaneSelectedTabIsBatched(RESOURCES_PAGE_DP_ALIAS, true, RESOURCES_PAGE_DP_ALIAS));
                dispatch(setDetailsPaneDisplayTabDot(RESOURCES_PAGE_DP_ALIAS, RESOURCE_KEY, isActionRequired, RESOURCES_PAGE_DP_ALIAS));
            }
        };
    };

    const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
        const columns = propsFromState.gridColumnsPropsBuilder({
            dispatchDescriptionCellClick: propsFromDispatch.dispatchDescriptionCellClick,
            dispatchContextualResourceDropdownClick: propsFromDispatch.dispatchContextualResourceDropdownClick,
            dispatchOnContextualGoToProfileClick: propsFromDispatch.dispatchOnContextualGoToProfileClick,
            dispatchDataGridSortChange: propsFromDispatch.dispatchDataGridSortChange,
            getFieldInfo: propsFromState.wrappedGetFieldInfo,
            getData: propsFromState.getData,
            selectedRowKeys: propsFromState.selectedRowKeys
        }, propsFromState.pageSize);

        const dataGridRowSelectionProps = {
            fixed: true,
            getCheckboxProps: (record) => {
                return {
                    disabled: record.key === propsFromState.paginationKey,
                    [ARIA_ATTRIBUTES.ARIA_LABEL]: propsFromState.getSelectRowAriaLabel(record[RESOURCE_DESCRIPTION])
                };
            },
            selectedRowKeys: propsFromState.selectedRowKeys,
            onChange: (selectedRowKeys) => {
                propsFromDispatch.dispatchOnResourceSelected(
                    `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                    propsFromState.tableName,
                    selectedRowKeys,
                    propsFromState.refreshBatchDPOnResourceSelect
                );
            }
        };

        return {
            ...propsFromState,
            ...propsFromDispatch,
            ...ownProps,
            columns,
            dataGridRowSelectionProps,
            onMenuSelect: (pageSize) => {
                propsFromDispatch.dispatchOnMenuSelect(
                    propsFromState.tableName,
                    pageSize
                );
            },
            onPageChange: (pageNumber, pageSize) => {
                propsFromDispatch.dispatchOnPageChange(
                    pageNumber,
                    pageSize
                );
            }
        };
    };

    return compose(
        connect(
            mapStateToProps,
            mapDispatchToProps,
            mergeProps
        ),
        configuredWithEmptyStates,
        withScroll
    )(DataGrid);
};

const ComposedDataGrid = createConnectedDataGrid(RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_DETAILS_PANE);

export {
    ComposedDataGrid as ConnectedResourcesGrid
};
