import { connect } from 'react-redux';
import { pushUrl } from '../actions/navigateActions';
import { breadcrumbLevelsMap, subPagesOptionsMap } from '../constants/breadcrumbConsts';
import BreadCrumb, { buildBreadcrumbOptions } from '../lib/breadCrumb';
import { getCommandBarConfigSelector } from '../selectors/commandBarSelectors';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS, TABLE_NAMES } from '../constants/globalConsts';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';
import { updateListView } from '../actions/listPageActions';

export const mapStateToProps = (state, ownProps) => {
    const { pageAlias, masterPageOptions, getPageState } = ownProps;
    const { masterPageAlias, masterPageDisplayName } = masterPageOptions;
    const { params } = state[pageAlias].pageState;
    const masterPageParams = (getPageState(state, masterPageAlias) || {}).params;
    const config = getCommandBarConfigSelector(state, JOBS_PAGE_ALIAS);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
    let aliasPageDisplayName = config.pageTitleSection.pageTitle;

    // When feature flag is enabled, use 'Jobs' translation instead of 'Lists'
    if (listPageAndBulkUpdateFeatureFlag) {
        const { jobsPage } = getTranslationsSelector(state, { sectionName: 'pages' });
        aliasPageDisplayName = jobsPage;
    }

    const breadcrumbOptions = params ? buildBreadcrumbOptions(breadcrumbLevelsMap[pageAlias], params, subPagesOptionsMap[masterPageDisplayName], masterPageParams, masterPageAlias, aliasPageDisplayName, listPageAndBulkUpdateFeatureFlag) : [];
    const sepratatorIconType = 'slash';

    return {
        breadcrumbOptions,
        sepratatorIconType
    };
};

export const mapDispatchToProps = (dispatch) => {
    return {
        onClick: (newParams, page, listPageAndBulkUpdateFeatureFlag) => {
            if (listPageAndBulkUpdateFeatureFlag && page === JOBS_PAGE_ALIAS) {
                dispatch(updateListView(TABLE_NAMES.JOB));
            }
            dispatch(pushUrl(newParams, page));
        }
    };
};

const ConnectedBreadcrumb = connect(
    mapStateToProps,
    mapDispatchToProps
)(BreadCrumb);

export default ConnectedBreadcrumb;
