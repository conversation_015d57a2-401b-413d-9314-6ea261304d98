import { createConnectedFilterPane } from './connectedFilterPaneCreator';
import { JOBSPAGE_FILTER_ALIAS, JOBS_PAGE_ALIAS } from '../../constants/jobsPageConsts';
import { PLANNER_PAGE_ALIAS } from '../../constants/plannerConsts';
import { getJobsPageFilters, getMarketplacePageFilters, getResourcesPageFilters, getRoleInboxPageFilters } from '../../selectors/dataGridPageSelectors';
import { ROLE_INBOX_PAGE_FILTER_ALIAS, ROLE_INBOX_PAGE_ALIAS } from '../../constants/roleInboxPageConsts';
import { PEOPLE_FINDER_DIALOG_ALIAS, PEOPLE_FINDER_DIALOG_FILTER_ALIAS } from '../../constants/peopleFinderConst';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { TABLE_NAMES } from '../../constants';
import { getPeopleFinderFilters, getIsPeopleFinderFilterAppliedSelector } from '../../selectors/peopleFinderSelectors';
import { MARKETPLACE_PAGE_ALIAS, MARKETPLACE_PAGE_FILTER_ALIAS } from '../../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../../constants/tableViewPageConsts';
import { getTableViewPageFilters } from '../../selectors/tableViewSelectors';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { getJobFilterDialogFilterSelectionSelector, getJobFilterDialogFiltersSelector } from '../../selectors/jobFilterDialogSelectors';
import { JOB_FILTER_DIALOG_ALIAS, JOB_FILTER_DIALOG_FILTER_ALIAS } from '../../constants/jobFilterDialogConsts';
import { getMassDuplicateJobsFiltersSelector } from '../../selectors/adminSettingSelectors/massDuplicateJobsSelectors';
import { MASS_DUPLICATE_JOBS_ALIAS, MASS_DUPLICATE_JOBS_FILTER_ALIAS } from '../../constants/massDuplicateJobsConsts';
import { getEntityInfoSelector } from '../../selectors/entityStructureSelectors';
import { getEntityAlias } from '../../utils/entityStructureUtils';
import { populateStringTemplates } from '../../utils/translationUtils';
import { JOB_DESCRIPTION } from '../../constants/fieldConsts';
import { getStaticMessagesSelector } from './selectors';
import { getFilterVisibleDates, getVisiblePageDates } from '../../selectors/plannerPageSelectors';
import { RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_FILTER_ALIAS } from '../../constants/resourcesPageConsts';

const ConnectedJobsPageFiltersPane = createConnectedFilterPane(
    JOBS_PAGE_ALIAS,
    JOBSPAGE_FILTER_ALIAS,
    getJobsPageFilters,
    () => {
        return {};
    }
);

const ConnectedJobFilterFiltersPane = createConnectedFilterPane(
    JOB_FILTER_DIALOG_ALIAS,
    JOB_FILTER_DIALOG_FILTER_ALIAS,
    getJobFilterDialogFiltersSelector,
    (state) => {
        const staticMessages = getStaticMessagesSelector(state);
        const { searchInputFilter = {} } = staticMessages;
        const getEntityInfo = getEntityInfoSelector(state);
        const entityInfo = getEntityInfo(TABLE_NAMES.JOB);
        const entitySingularLower = getEntityAlias(entityInfo, { singularForm: true, capitalized: false, fallbackValue: TABLE_NAMES.JOB });
        const updatedStaticMessages = populateStringTemplates(searchInputFilter, { jobAlias: entitySingularLower });

        const { placeholderLabel, filterTitle } = updatedStaticMessages;

        const filterSelection = getJobFilterDialogFilterSelectionSelector(state)(TABLE_NAMES.JOB);
        const inputFieldValue = (filterSelection.find(selectedFilter => selectedFilter.field === JOB_DESCRIPTION && selectedFilter.isInlineInputSearchFilter) || {}).value || '';

        return {
            showFilterPaneHeading: false,
            searchInputFilterConfig: {
                tableName: TABLE_NAMES.JOB,
                fieldName: JOB_DESCRIPTION,
                inputFieldValue
            },
            filterTitle,
            placeholderLabel
        };
    }
);

const ConnectedMassDuplicateJobsFiltersPane = createConnectedFilterPane(
    MASS_DUPLICATE_JOBS_ALIAS,
    MASS_DUPLICATE_JOBS_FILTER_ALIAS,
    getMassDuplicateJobsFiltersSelector,
    (state) => {
        const staticMessages = getStaticMessagesSelector(state);
        const { searchInputFilter = {} } = staticMessages;
        const getEntityInfo = getEntityInfoSelector(state);
        const entityInfo = getEntityInfo(TABLE_NAMES.JOB);
        const entitySingularLower = getEntityAlias(entityInfo, { singularForm: true, capitalized: false, fallbackValue: TABLE_NAMES.JOB });
        const updatedStaticMessages = populateStringTemplates(searchInputFilter, { jobAlias: entitySingularLower });

        const { placeholderLabel, filterTitle } = updatedStaticMessages;

        return {
            autoAdjustOverflow: false,
            showFilterPaneHeading: false,
            filterTitle,
            placeholderLabel
        };
    }
);

const ConnectedRoleInboxFiltersPane = createConnectedFilterPane(
    ROLE_INBOX_PAGE_ALIAS,
    ROLE_INBOX_PAGE_FILTER_ALIAS,
    getRoleInboxPageFilters,
    () => {
        return {};
    }
);

const ConnectedMarketplacePageFiltersPane = createConnectedFilterPane(
    MARKETPLACE_PAGE_ALIAS,
    MARKETPLACE_PAGE_FILTER_ALIAS,
    getMarketplacePageFilters,
    () => {
        return {};
    }
);

const ConnectedTableViewPageFiltersPane = createConnectedFilterPane(
    TABLE_VIEW_PAGE_ALIAS,
    TABLE_VIEW_PAGE_FILTER_ALIAS,
    getTableViewPageFilters,
    getVisiblePageDates
);

const ConnectedPeopleFinderFiltersPane = createConnectedFilterPane(
    PEOPLE_FINDER_DIALOG_ALIAS,
    PEOPLE_FINDER_DIALOG_FILTER_ALIAS,
    getPeopleFinderFilters,
    (state) => {
        const { isFilterApplied } = getIsPeopleFinderFilterAppliedSelector(state)(TABLE_NAMES.RESOURCE);
        const { filterTitle = '' } = getTranslationsSelector(state, { sectionName: PEOPLE_FINDER_DIALOG_ALIAS });
        const pageAlias = getCurrentPageAliasSelector(state);
        let result = {
            showFilterPaneHeading: false,
            showResetFilter: isFilterApplied,
            filterTitle
        };

        if (pageAlias === PLANNER_PAGE_ALIAS) {
            result = {
                ...result,
                ...getFilterVisibleDates(state, pageAlias)
            }; 
        }

        return result;
    }
);

const ConnectedResourcesPageFiltersPane = createConnectedFilterPane(
    RESOURCES_PAGE_ALIAS,
    RESOURCES_PAGE_FILTER_ALIAS,
    getResourcesPageFilters,
    () => {
        return {
            visibleDates: {
                startDate: undefined,
                endDate: undefined
            }
        };
    }
);

export {
    ConnectedJobsPageFiltersPane,
    ConnectedRoleInboxFiltersPane,
    ConnectedPeopleFinderFiltersPane,
    ConnectedMarketplacePageFiltersPane,
    ConnectedTableViewPageFiltersPane,
    ConnectedJobFilterFiltersPane,
    ConnectedMassDuplicateJobsFiltersPane,
    ConnectedResourcesPageFiltersPane
};