import { connect } from 'react-redux';
import { entityWindowClose } from '../../actions/entityWindowActions';
import { pushUrl } from '../../actions/navigateActions';
import { setPageState } from '../../actions/pageStateActions';
import { DETAILS_PANE_TAB_KEYS, JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE, TABLE_NAMES } from '../../constants';
import { ROLE_ITEM_STATUS_CLASS_NAMES } from '../../constants/rolesConsts';
import { RolesItemsList } from '../../lib/rolesList';
import { R<PERSON><PERSON><PERSON><PERSON>LISTDETAILSPAGE } from '../../pages/pages';
import { getIsJobDpItemVisibleCollapse } from '../../selectors/jobsPageRoleGroupSelectors';
import { getRolesListAvatarConfig } from '../../selectors/roleGroupDetailsPageSelectors';
import store from '../../store/configureStore';
import styles from './styles.less';
import { setShowSubTab } from '../../actions/detailsPaneActions';
import { getIsMultipleAssigneesEnabled } from '../../selectors/functionalityConfigurationSelectors';
import { getProgressBarVisibility, getRolesListTimeAllocationExplanation } from '../../utils/rolesListUtils';
import { getIsCriteriaRole, getRolerequestFilledPercentage } from '../../utils/roleRequestsUtils';
import { getRoleTimeAllocationExplanationSelector } from '../../selectors/roleRequestsSelector';
import { ROLEREQUEST_FIELDS } from '../../constants/fieldConsts';
import { getRoleResourcesInfoSelector } from '../../selectors/rolesListSelectors';
import { getEntityAliasSelector } from '../../selectors/entityStructureSelectors';
import { LIST_PAGE_ALIAS } from '../../constants/listPageConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const mapRoleRequestListItem = (useMultipleAssignees, roleRequests, getRoleTimeAllocationExplanation, getRoleResourcesInfo, listPageAndBulkUpdateFeatureFlag) => {
    return roleRequests.map(role => {
        const resourcesInfo = getRoleResourcesInfo(role, role.roleRequestId);
        const isCriteriaRole = getIsCriteriaRole(role);
        const timeAllocationExplanation = useMultipleAssignees && getRolesListTimeAllocationExplanation(resourcesInfo, role, role.rolerequestStatus, getRoleTimeAllocationExplanation);
        const rolerequestFTE = role[ROLEREQUEST_FIELDS.FTE];
        const demandedResourcesCount = role[ROLEREQUEST_FIELDS.RESOURCE_DEMAND];
        const filledPercentage = getRolerequestFilledPercentage(role);

        return {
            entity: {
                ...role,
                listPageAndBulkUpdateFeatureFlag: listPageAndBulkUpdateFeatureFlag
            },
            type: role.type,
            resourcesInfo: resourcesInfo,
            showContextMenu: role.showContextMenu,
            isCriteriaRole,
            status: {
                value: role.rolerequestStatus,
                className: ROLE_ITEM_STATUS_CLASS_NAMES[role.rolerequestStatus],
                visible: true
            },
            resourceIsInactive: role.userStatus === false,
            displayProgressBar: getProgressBarVisibility(isCriteriaRole, role.rolerequestStatus),
            timeAllocationExplanation,
            rolerequestFTE,
            demandedResourcesCount,
            filledPercentage
        };
    });
};

const mapStateToProps = (state, ownProps) => {
    const avatarConfig = getRolesListAvatarConfig(state);
    const useMultipleAssignees = getIsMultipleAssigneesEnabled(state);
    const getRoleTimeAllocationExplanation = getRoleTimeAllocationExplanationSelector(state);
    const getRoleResourcesInfo = getRoleResourcesInfoSelector(state);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
    const items = mapRoleRequestListItem(useMultipleAssignees, ownProps.roles, getRoleTimeAllocationExplanation, getRoleResourcesInfo, listPageAndBulkUpdateFeatureFlag);
    const staticMessages = {};
    const bookingAlias = getEntityAliasSelector(state, TABLE_NAMES.BOOKING, { singularForm: true, capitalized: false });

    let itemListProps = {
        HeaderComponent: null,
        onAction: () => { },
        emptyStateMessage: staticMessages.emptyStateLabel,
        emptyStateBoldLabel: staticMessages.emptyStateBoldLabel
    };
    const getItemsContextualMenuProps = (item) => {
        return null;
    };

    return {
        customContainerStyle: styles.roleListWrapper,
        items,
        avatarConfig,
        itemListProps,
        getItemsContextualMenuProps,
        useMultipleAssignees,
        bookingAlias
    };
};

const mapDispatchToProps = (dispatch, ownProps) => {
    return {
        onClick: (context, page = '', moduleName) => {
            const { value, rolerequestSurrogateId, jobName, jobGuid, jobSurrogateId, rolegroupName, rolegroupSurrogateId, roleRequestId, isCriteriaResource, listPageAndBulkUpdateFeatureFlag } = context;
            const params = {
                roleRequestId: roleRequestId,
                rolerequestName: value,
                rolerequestSurrogateId: rolerequestSurrogateId,
                rolegroupName: rolegroupName,
                rolegroupSurrogateId: rolegroupSurrogateId,
                jobName: jobName,
                jobId: jobGuid,
                jobSurrogateId: jobSurrogateId
            };

            const newParams = {
                subPageOption: {
                    subPageNavLink: ROLEGROUPLISTDETAILSPAGE.navigationLink,
                    subPagesParams: params
                }
            };

            const { rolegroupListPage = {} } = store.getState();

            const shouldRoleGroupDPItemClose = getIsJobDpItemVisibleCollapse(rolegroupListPage, ROLE_GROUP_LIST_PAGE);

            dispatch(setPageState(ROLE_GROUP_DETAILS_PAGE, params));
            dispatch(pushUrl(newParams, ROLE_GROUP_DETAILS_PAGE, listPageAndBulkUpdateFeatureFlag ? LIST_PAGE_ALIAS : JOBS_PAGE_ALIAS));
            dispatch(setShowSubTab(ROLE_GROUP_DETAILS_PAGE, DETAILS_PANE_TAB_KEYS.RESOURCE_KEY, isCriteriaResource, ROLE_GROUP_DETAILS_PAGE));

            if (shouldRoleGroupDPItemClose) {
                dispatch(entityWindowClose(moduleName));
            }
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...propsFromState,
        ...propsFromDispatch,
        ...ownProps,
        onRoleRequestListItemClick: (context, page = '') => propsFromDispatch.onRoleRequestListItemClick(context, page, propsFromState.moduleName)
    };
};

const ConnectedRolegroupDpRolesList = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(RolesItemsList);

export { ConnectedRolegroupDpRolesList };