import React from 'react';
import { debounce, groupBy, isObject } from 'lodash';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';

import { createComponent } from '../../factories/componentFactory';
import { FieldControl } from '../../lib/fieldControl';
import { getEntityDetailsWindowComponents } from '../../lib/entityWindow/entityDetailsWindow';
import { startOfDay, endOfDay, parseToUtcDate, formatToUtcISOString, getStartOfDay } from '../../utils/dateUtils';
import { MAX_NUMBER_FORM_FIELDS_CHANGE, ENTITY_WINDOW_CUSTOM_CONTROL_TYPES, ENTITY_WINDOW_TAB_KEYS, ENTITY_WINDOW_OPERATIONS, ENTITY_WINDOW_SECTION_TYPES } from '../../constants/entityWindowConsts';
import { isCustomLookupOrPlanningDataField, isTableFieldLinked } from '../../utils/tableStructureUtils';
import { mapPropsToFields as mapPropsToSkillFields } from '../connectedSkillFieldsForm.js';
import { ConnectedEntityHistoryWindow } from './audit/connectedEntityHistoryWindow';
import { getBulkEditTabComponents } from '../../lib/entityWindow/entityBulkEditWindow';
import { ConnectedEntityRoleListWindow } from './connectedWindows/connectedEntityRoleListWindow';
import { BaseEntityDetailsWindow } from '../../lib/entityWindow/baseEntityDetailsWindow';
import { fieldIsContainerType, isDependantListControl } from '../../utils/fieldControlUtils';
import { mapPropsToCriteriaBudgetFields } from '../../utils/criteriaRoleBudgetUtils';
import { getCustomMultifiedRadioControlDefaultValue, getIsCriteriaRole } from '../../utils/roleRequestsUtils';
import { ConnectedEntityRoleTemplatesListWindow } from './connectedWindows/connectedRoleTemplatesEntityWindow';
import { ROLEMARKETPLACE_FIELDS } from '../../constants/fieldConsts.js';
import { FEATURE_FLAGS } from '../../constants/globalConsts.js';

const getLinkedFieldFormValue = (fieldName, fieldValue, uiEntity, autoCompleteState, getLinkedData) => {
    let value = {};

    const isInAutoCompleteState = autoCompleteState &&
        autoCompleteState[fieldName] &&
        autoCompleteState[fieldName].isAutoCompleting;

    if (isInAutoCompleteState) {
        value = {
            id: null,
            value:  uiEntity[fieldName].displayValue || autoCompleteState[fieldName].enteredText
        };
    } else {
        const dataVal = getLinkedData(fieldName, fieldValue) || {};
        value = {
            id: uiEntity[fieldName].value,
            value: uiEntity[fieldName].displayValue || dataVal.value
        };
    }

    return value;
};

const getFieldsValues = (fields, uiEntity, getFieldInfo, tableName, getLinkedData, autoCompleteState) => {
    return fields.reduce((accumulator, field) => {
        const isContainer = fieldIsContainerType(field);

        if (isContainer && field.fields) {
            const subFiledValues = getFieldsValues(field.fields, uiEntity, getFieldInfo, tableName, getLinkedData, autoCompleteState);

            return {
                ...accumulator,
                ...subFiledValues
            };
        }

        return {
            ...accumulator,
            [field.name]: isTableFieldLinked(tableName, getFieldInfo(tableName, field.name)) ?
                getLinkedFieldFormValue(field.name, uiEntity[field.name].value, uiEntity, autoCompleteState, getLinkedData)
                : (uiEntity[field.name].displayValue || uiEntity[field.name].value)

        };
    },{});
};

const getFieldsErrors = (fields, uiEntity, fieldInfo) => {
    return fields
        .reduce((accumulator, field) => {
            const isContainer = fieldIsContainerType(field);

            if (isContainer && field.fields) {
                const subFiledValues = getFieldsErrors(field.fields, uiEntity, fieldInfo);

                return [
                    ...accumulator,
                    ...subFiledValues
                ];
            }

            return [
                ...accumulator,
                ...(uiEntity[field.name].errors || []).map(error => {
                    return {
                        ...error,
                        field: fieldInfo.name
                    };
                })
            ];
        }, []);
};

const getFieldControl = (field, entity, options, uiEntity) => {
    const fieldInfo = options.getFieldInfo(field.table, field.name);
    const controlKey = fieldInfo.name ? `${fieldInfo.name}_control` : `${field.name}_control`;

    return (
        <FieldControl
            key={controlKey}
            field={field}
            entity={entity}
            options={options}
            uiEntity={uiEntity}
        />
    );
};

export const getFormField = (props, fieldInfo) => {
    const { uiEntity, getLinkedData, entity = {}, useMultipleAssignees } = props;

    const { errors = [] } = uiEntity[fieldInfo.name] || {};
    let fieldValue = {
        value: uiEntity[fieldInfo.name].value
    };

    if (errors.length > 0) {
        fieldValue = {
            ...fieldValue,
            errors
        };
    }

    if (fieldInfo.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_RADIO_CONTROL) {
        const isCriteriaRole = getIsCriteriaRole(entity);
        const defaultValue = getCustomMultifiedRadioControlDefaultValue(fieldInfo.name, isCriteriaRole, useMultipleAssignees, fieldInfo.valueKey.defaultValue);

        fieldValue.value = {
            [fieldInfo.valueKey.name]: uiEntity[fieldInfo.valueKey.name].value || defaultValue,
            ...getFieldsValues(fieldInfo.fields, uiEntity, props.getFieldInfo, fieldInfo.table, getLinkedData, props.autoComplete)
        };

        const errors = [
            ...(uiEntity[fieldInfo.valueKey.name].errors || []),
            ...getFieldsErrors(fieldInfo.fields, uiEntity, fieldInfo)
        ];

        if (errors.length > 0) {
            fieldValue.errors = errors;
        }

        fieldValue.fields = [
            fieldInfo.valueKey.name,
            ...fieldInfo.fields.map(field => field.name)
        ];
    } else if (fieldInfo.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.DATE_RANGE_CONTROL) {
        fieldValue.value = {
            [fieldInfo.startDateField.name]: uiEntity[fieldInfo.startDateField.name].value,
            [fieldInfo.endDateField.name]: uiEntity[fieldInfo.endDateField.name].value,
            ...getFieldsValues(fieldInfo.fields, uiEntity, props.getFieldInfo, fieldInfo.table, getLinkedData, props.autoComplete)
        };

        const errors = [
            ...(uiEntity[fieldInfo.startDateField.name].errors || []),
            ...(uiEntity[fieldInfo.endDateField.name].errors || []),
            ...getFieldsErrors(fieldInfo.fields, uiEntity, fieldInfo)
        ];

        if (errors.length > 0) {
            fieldValue.errors = errors;
        }

        fieldValue.fields = [
            fieldInfo.startDateField.name,
            fieldInfo.endDateField.name,
            ...fieldInfo.fields.map(field => field.name)
        ];
    } else if (fieldInfo.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL) {
        fieldValue.value = {
            type: fieldInfo.type,
            ...getFieldsValues(fieldInfo.fields, uiEntity, props.getFieldInfo, fieldInfo.table, getLinkedData, props.autoComplete)
        };

        const errors = [
            ...getFieldsErrors(fieldInfo.fields, uiEntity, fieldInfo)
        ];

        if (errors.length > 0) {
            fieldValue.errors = errors;
        }

        fieldValue.fields = [
            ...fieldInfo.fields.map(field => field.name)
        ];
    } else if (fieldInfo.dataType == 'DateTime') {
        fieldValue.value = fieldValue.value
            ? parseToUtcDate(fieldValue.value)
            : null;
    } else if (fieldInfo.dataType == 'Float') {
        fieldValue.value = !isNaN(fieldValue.value) && fieldValue.value !== null
            ? +fieldValue.value
            : null;
    } else if (fieldInfo.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL) {
        const { autoComplete } = props;
        const { name, actualFieldName, table, type } = fieldInfo;

        const items = (fieldValue.value || []).map(id => props.getMultiValueLinkedFieldListItem(actualFieldName, table, id));
        const enteredText = autoComplete && autoComplete[name]
            ? autoComplete[name].enteredText
            : null;

        fieldValue = {
            value: {
                items,
                enteredText,
                actualFieldName,
                table,
                type
            }
        };

        const errors = uiEntity[name].errors || [];

        if (errors.length > 0) {
            fieldValue.errors = errors;
        }
    } else if (fieldInfo.dataType == 'ID' || isCustomLookupOrPlanningDataField(fieldInfo)) {
        fieldValue = {
            ...fieldValue,
            value: getLinkedFieldFormValue(fieldInfo.name, fieldValue.value, uiEntity, props.autoComplete, getLinkedData)
        };
    } else if (fieldInfo.dataType == 'String') {
        fieldValue = {
            ...fieldValue,
            value: fieldValue.value || null
        };
    }

    return fieldValue;
};

export const mapPropsToFields = (props) => {
    let result = {};
    const { getFieldsConfig: getSkillFieldsConfig, getUISkill, entityId } = props;

    props.sections.forEach((section) => {
        const { fields = [], skills = {} } = section;
        const sectionIds = Object.keys(skills);

        if ((section.subSections || []).length > 1) {
            result = { ...result, ...mapPropsToFields({ ...props, sections: section.subSections }) };
        }

        if ((section.subSectionItems || []).length > 1) {
            result = { ...result, ...mapPropsToFields({ ...props, sections: section.subSectionItems }) };
        }

        if (fields.length > 0) {
            fields.forEach((field) => {
                if (isDependantListControl(field)) {
                    field.fields.forEach(nestedField => {
                        result[nestedField.name] = getFormFields(nestedField, props);
                    });
                } else {
                    result[field.name] = getFormFields(field, props);
                }
            });
        } else if (sectionIds.length > 0 && props.operation === ENTITY_WINDOW_OPERATIONS.EDIT) {
            sectionIds.forEach((id) => {
                (section.skills[id].skills || []).forEach((skill => {
                    result = {
                        ...result,
                        ...mapPropsToSkillFields({ ...props, fieldsConfig: getSkillFieldsConfig(skill.id), uiSkill: getUISkill(entityId, skill.id) }, skill.id)
                    };
                }));
            });
        } else if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.REQUIREMENTS_SECTION_TYPE) {
            const { criteriaFieldsState } = props;
            if (criteriaFieldsState) {
                Object.keys(criteriaFieldsState).forEach(criteriaSection => {
                    criteriaFieldsState[criteriaSection].forEach(criteriaField => {
                        result[criteriaField.fieldName] = getCriteriaFormFields(criteriaField, props);
                    });
                });
            }
        }

        if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.CRITERIA_BUDGET_SECTION_TYPE) {
            result = {
                ...result,
                ...mapPropsToCriteriaBudgetFields(props)
            };
        }
    });

    return result;
};

const getFormFields = (field, props) => {
    let result = {};

    result = Form.createFormField({
        ...getFormField(
            props,
            {
                ...props.getFieldInfo(field.table, field.name),
                ...field
            }
        )
    });

    return result;
};

const getCriteriaFormFields = (criteriaField = {}) => {
    let result = {};

    const criteriaFieldErrors = criteriaField.errors || [];
    result = Form.createFormField(
        { value: {
            items: criteriaField.value,
            isCriteriaRole : true,
            errors : criteriaFieldErrors
        } }
    );

    return result;
};

const fieldValueChanged = (uiEntity, fields, fieldKey, fieldValueKey) => {
    return uiEntity[fieldValueKey].value !== fields[fieldKey].value[fieldValueKey];
};

const onFieldsChange = (props, fields, entityId) => {
    // Prevents invoking AntD internal forced onFieldChange calls
    if (Object.keys(fields).length <= MAX_NUMBER_FORM_FIELDS_CHANGE) {
        Object.keys(fields).forEach((field) => {
            const fieldInfo = props.getFieldInfo(props.tableName, field);

            if (fieldInfo && Object.keys(fieldInfo).length) {
                let actualValue = fields[field].value;

                if (fieldInfo.dataType === 'ID' && actualValue === null) {
                    actualValue = props.getEmptyOption(props.tableName, field);
                } else if (fieldInfo.dataType === 'DateTime' && actualValue) {
                    switch (fieldInfo.name) {
                        case ROLEMARKETPLACE_FIELDS.PUBLISHEDON:
                            actualValue = getStartOfDay(actualValue);
                            break;
                        case 'booking_start': {
                            actualValue = startOfDay(actualValue);
                            break;
                        }
                        case 'booking_end': {
                            actualValue = endOfDay(actualValue);
                            break;
                        }
                    }

                    actualValue = formatToUtcISOString(actualValue);
                }

                if (props.onFieldError && !fields[field].validating) {
                    if (field == 'job_start' || field == 'job_end') {
                        props.onFieldError({
                            ['job_start']: {
                                errors: []
                            },
                            ['job_end']: {
                                errors: []
                            }
                        }, entityId);
                    }
                    props.onFieldError({
                        [field]: {
                            errors: fields[field].errors || []
                        }
                    }, entityId);
                    props.onEntityFieldChange(fieldInfo, actualValue, entityId);
                }

            }
            // Should be revised and value.type sounds like the same field can have two different models for his value which should not be possible
            else if (fields[field].value.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTI_VALUE_LINKED_FIELD_CONTROL) {
                const { items = [] } = fields[field].value;
                const actualValue = items.map(item => item.id);

                if (props.onFieldError && !fields[field].validating) {
                    props.onFieldError({
                        [field]: {
                            errors: fields[field].errors || []
                        }
                    }, entityId);
                    props.onEntityFieldChange(fields[field], actualValue, entityId);
                }

            } else if (fields[field].value.isCriteriaRole) {
                if (props.onCriteriaFieldError && !fields[field].validating) {
                    props.onCriteriaFieldError({
                        [field]: {
                            errors: fields[field].errors || []
                        }
                    }, entityId);
                }
            }
            // Should be revised and value.type sounds like the same field can have two different models for his value which should not be possible
            else if (fields[field].value.type === ENTITY_WINDOW_CUSTOM_CONTROL_TYPES.MULTIFIELD_SINGLE_ROW_CONTROL) {
                if (props.onFieldError && !fields[field].validating) {
                    props.onFieldError({
                        [field]: {
                            errors: fields[field].errors || []
                        }
                    }, entityId);

                    fields[field].fields
                        .filter(nestedFieldName => props.uiEntity[nestedFieldName].value !== fields[field].value[nestedFieldName]) // if valueChanged
                        .forEach(nestedFieldName => {
                            props.onEntityFieldChange(props.getFieldInfo(props.tableName, nestedFieldName), fields[field].value[nestedFieldName], entityId);
                        });
                }
            } else {
                if (!fields[field].validating) {
                    const { errors = [] } = fields[field];
                    const grouppedErrors = groupBy(errors, 'fieldName');

                    let fieldsErrors = [...(fields[field].fields || [])];

                    if (fields[field] && fields[field].value && isObject(fields[field].value)) {
                        Object.keys(fields[field].value).forEach(field => {
                            if (!fieldsErrors.includes(field)) {
                                fieldsErrors.push(field);
                            }
                        });
                    }

                    props.onFieldError(
                        fieldsErrors.reduce((accumulator, fieldKey) => {
                            return {
                                ...accumulator,
                                [fieldKey]: {
                                    errors: grouppedErrors[fieldKey] || []
                                }
                            };
                        }, {}),
                        entityId
                    );

                    Object
                        .keys(fields[field].value || {})
                        .filter(fieldValueKey => fieldValueChanged(props.uiEntity, fields, field, fieldValueKey))
                        .forEach(fieldKey => {
                            props.onEntityFieldChange(props.getFieldInfo(props.tableName, fieldKey), fields[field].value[fieldKey], entityId);
                        });
                }
            }
        });
    }
};

const onAllFieldsChange = (props, fields) => {
    if (Object.keys(fields).length <= MAX_NUMBER_FORM_FIELDS_CHANGE) {
        Object.keys(fields).forEach((field) => {
            if (!fields[field].validating && fields[field].fields) {
                props.onEntityFieldChange(fields[field], fields[field].value);
            }
        });
    }
    onFieldsChange(props, fields);

};

export const debouncedFieldsChange = debounce(
    onFieldsChange,
    50,
    {
        leading: true,
        trailing: true
    }
);

export const debouncedAllFieldsChange = debounce(
    onAllFieldsChange,
    50,
    {
        leading: true,
        trailing: true
    }
);

const EntityDetailsTabContent = (props) => {
    const windowProps = {
        ...props,
        onEntityFieldChange: props.onEntityFieldChange,
        onSkillFieldChange: props.onSkillFieldChange,
        onFieldsChange: debouncedFieldsChange,
        getFieldControl,
        mapPropsToFields,
        className: 'entityDetailsWindow',
        ...getEntityDetailsWindowComponents()
    };

    return (
        <BaseEntityDetailsWindow
            {...windowProps}
        />
    );
};

const EntityBulkEditTabContent = (props) => {
    const windowProps = {
        ...props,
        onEntityFieldChange: props.onEntityFieldChange,
        onBulkFieldAdd: props.onBulkFieldAdd,
        onSkillFieldChange: props.onSkillFieldChange,
        onFieldsChange: debouncedAllFieldsChange,
        getFieldControl,
        mapPropsToFields,
        className: 'entityBulkEditWindow',
        ...getBulkEditTabComponents()
    };

    return (
        <BaseEntityDetailsWindow
            {...windowProps}
        />
    );
};

const EntityRolesListTabContent = (props) => {
    const windowProps = {
        ...props,
        onEntityFieldChange: props.onEntityFieldChange,
        onSkillFieldChange: props.onSkillFieldChange,
        onFieldsChange: debouncedAllFieldsChange,
        getFieldControl,
        mapPropsToFields
    };

    return <ConnectedEntityRoleListWindow {...windowProps} />;
};

const EntityRoleTemplatesListTabContent = (props) => {
    const windowProps = {
        ...props,
        onEntityFieldChange: props.onEntityFieldChange,
        onSkillFieldChange: props.onSkillFieldChange,
        onFieldsChange: debouncedAllFieldsChange,
        getFieldControl,
        mapPropsToFields
    };

    return <ConnectedEntityRoleTemplatesListWindow {...windowProps} />;
};

const HistoryTabContent = (props) => {
    const { moduleName, tableName, entityId } = props;

    return (
        <ConnectedEntityHistoryWindow
            moduleName={moduleName}
            tableName={tableName}
            entityId={entityId}
        />
    );
};

export const getDetailsTab = (props) => {
    const { detailsTabLabel } = props.staticLabels;

    return {
        key: ENTITY_WINDOW_TAB_KEYS.DETAILS,
        name: detailsTabLabel || 'Details',
        tabContent: createComponent(EntityDetailsTabContent, props)
    };
};

export const getBulkEditTab = (props) => {
    const { editAllTabLabel } = props.staticLabels;

    return {
        key: ENTITY_WINDOW_TAB_KEYS.EDIT_ALL,
        name: editAllTabLabel || 'Edit All',
        tabContent: createComponent(EntityBulkEditTabContent, props)
    };
};

export const getRoleListTab = (props) => {
    const { roleListLabel } = props.staticLabels;

    return {
        key: ENTITY_WINDOW_TAB_KEYS.ROLE_LIST,
        name: roleListLabel || 'Role list',
        tabContent: createComponent(EntityRolesListTabContent, props)
    };
};

export const getRoleTemplatesListTab = (props) => {
    return {
        key: ENTITY_WINDOW_TAB_KEYS.ROLE_TEMPLATES_LIST,
        name: '',
        tabContent: createComponent(EntityRoleTemplatesListTabContent, props)
    };
};

export const getHistoryTab = (props) => {
    const { historyTabLabel } = props.staticLabels;

    return {
        key: ENTITY_WINDOW_TAB_KEYS.HISTORY,
        name: historyTabLabel || 'History',
        tabContent: createComponent(HistoryTabContent, props)
    };
};