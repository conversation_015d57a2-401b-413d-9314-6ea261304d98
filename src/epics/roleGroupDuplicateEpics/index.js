import { map, mergeMap, switchMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { getRoleGroupDuplicateSelectionQuery } from '../../utils/roleGroupDuplicateUtils';
import { API_KEYS, ERROR_STATUS, SUCCESS_STATUS } from '../../constants/apiConsts';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getRequestSelection } from '../../utils/autoCompleteUtils';
import { autocompleteSearch } from '../../actions/autocompleteActions';
import { ROLE_GROUP_DUPLICATE_DIALOG_ALIAS } from '../../constants/roleGroupDuplicateConsts';
import { getLinkedTableData, getTableDatasLoadedActions } from '../../utils/linkedDataUtils';
import { ROLEREQUESTGROUP_FIELDS } from '../../constants/fieldConsts';
import { combineEpics } from 'redux-observable';
import { ROLEGROUP_DUPLICATE_DIALOG_ACTIONS, roleGroupDuplicateCloseDialog, roleGroupDuplicateDialogLoadAdditionaTableData, roleGroupDuplicateDialogSetInitialData, roleGroupDuplicateDialogUpdateEntityErrorAction, roleGroupDuplicateDialogUpdateSuccessAction, roleGroupDuplicateError, roleGroupDuplicateSingleCreateErrorPrompt } from '../../reducers/roleGroupDuplicateReducer/actions';
import { getFieldTableName, isLookupField } from '../../utils/tableStructureUtils';
import { loadMoreTableDataSuccess } from '../../actions/tableDataActions';
import { isEmpty } from 'lodash';
import { getCurrentPageAliasSelector } from '../../selectors/navigationSelectors';
import { pushUrl } from '../../actions/navigateActions';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE } from '../../constants';
import { ROLEGROUPLISTDETAILSPAGE } from '../../pages/pages';
import { resetPageParams } from '../../actions/pageStateActions';
import { getDuplicateDialogDataSelector, getDuplicateInitialFormDataSelector } from '../../selectors/commonDuplicateSelectors';
import { openRoleGroupDetailsForDifferentRoleGroup } from '../../actions/roleGroupDetailsActions';
import { LIST_PAGE_ALIAS } from '../../constants/listPageConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const getLoadTableDataActions = (tableName, fieldName, response, getFieldInfo) => {
    return [
        getAutocompleteSearchAction(tableName, fieldName, getFieldInfo),
        ...getMultipleLoadTableDataSuccessActions(response, tableName, getFieldInfo)
    ];
};

const getAutocompleteSearchAction = (tableName, fieldName, getFieldInfo) => {
    const fieldInfo = getFieldInfo(tableName, fieldName);
    const selection = getRequestSelection(tableName, fieldInfo);

    return autocompleteSearch(ROLE_GROUP_DUPLICATE_DIALOG_ALIAS, null, tableName, fieldInfo, selection);
};

const getMultipleLoadTableDataSuccessActions = (response, tableName, getFieldInfo) => {
    const linkedTableDatas = getLinkedTableData(response, tableName, getFieldInfo);
    const linkedTableDatasLoadedActions = getTableDatasLoadedActions(ROLE_GROUP_DUPLICATE_DIALOG_ALIAS, linkedTableDatas);

    return linkedTableDatasLoadedActions;
};

const loadEntityDataEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.OPEN)
        .pipe(
            switchMap(action => {
                const { tableName, entityId } = action.payload;
                const state = state$.value;
                const selectionQuery = getRoleGroupDuplicateSelectionQuery(state, tableName, entityId);

                return apis[API_KEYS.TABLE_DATA].getTableData$(tableName, selectionQuery).pipe(map(response => [action, response]));
            })
        )
        .pipe(
            switchMap(([action, response]) => {
                let resultActions = [];

                if (!Array.isArray(response) && response.status === ERROR_STATUS) {
                    resultActions = [roleGroupDuplicateDialogUpdateEntityErrorAction()];
                } else {
                    const getFieldInfo = getFieldInfoSelector(state$.value);
                    const { tableName, entityId, entity: roleGroupEntity } = action.payload;

                    const entity = {
                        ...response[0],
                        [ROLEREQUESTGROUP_FIELDS.ROLES_START]: roleGroupEntity[ROLEREQUESTGROUP_FIELDS.ROLES_START]
                    };

                    resultActions.push(
                        ...getLoadTableDataActions(tableName, ROLEREQUESTGROUP_FIELDS.GUID, response, getFieldInfo),
                        roleGroupDuplicateDialogUpdateSuccessAction({
                            entity,
                            tableName,
                            entityId
                        }),
                        roleGroupDuplicateDialogLoadAdditionaTableData(tableName)
                    );
                }

                return from(resultActions);
            })
        );
};

const roleGroupDuplicateDialogSetInitialDataEpic$ = (action$, state$) => {
    const actionType = [`${ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.UPDATE_ALL_ENTITIES}_${SUCCESS_STATUS}`];

    return action$
        .ofType(...actionType)
        .pipe(
            switchMap(() => {
                const initialFieldData = getDuplicateInitialFormDataSelector(state$.value, ROLE_GROUP_DUPLICATE_DIALOG_ALIAS) || {};

                return of(roleGroupDuplicateDialogSetInitialData(initialFieldData));
            })
        );
};

const roleGroupDuplicateDialogFieldDataChangeInterceptor$ = (action$, state$) => {
    const actionType = [`${ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.OPTION_FIELD_CHANGE}`];

    return action$
        .ofType(...actionType)
        .pipe(
            switchMap(action => {
                const { payload = {} } = action;
                const { tableName } = getDuplicateDialogDataSelector(state$.value, ROLE_GROUP_DUPLICATE_DIALOG_ALIAS);
                const getFieldInfo = getFieldInfoSelector(state$.value);
                const chain = [];
                const changedFields = Object.keys(payload);

                changedFields.forEach(field => {
                    const fieldInfo = getFieldInfo(tableName, field);

                    if (isLookupField(tableName, fieldInfo)) {
                        const linkedTableName = getFieldTableName(fieldInfo, tableName);
                        const { value } = payload[field] || {};

                        if (!isEmpty(value)) {
                            chain.push(loadMoreTableDataSuccess(
                                ROLE_GROUP_DUPLICATE_DIALOG_ALIAS,
                                { tableDataGuid: linkedTableName, tableNames: [linkedTableName] },
                                [{
                                    [`${linkedTableName}_guid`]: value.id,
                                    [`${linkedTableName}_description`]: value.value
                                }]
                            ));
                        }
                    }
                });

                return from(chain);
            })
        );
};

export const navigateToRoleGroupDetailsPage = (rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag) => {
    const params = {
        rolegroupSurrogateId
    };

    const newParams = {
        subPageOption: {
            subPageNavLink: ROLEGROUPLISTDETAILSPAGE.navigationLink,
            subPagesParams: params
        }
    };

    return pushUrl(newParams, ROLE_GROUP_DETAILS_PAGE, listPageAndBulkUpdateFeatureFlag ? LIST_PAGE_ALIAS : JOBS_PAGE_ALIAS);
};

const roleGroupDulicateSuccessActionsPageMap = {
    [ROLE_GROUP_DETAILS_PAGE]: (rolegroupSurrogateId) => ([
        openRoleGroupDetailsForDifferentRoleGroup(rolegroupSurrogateId)
    ]),
    default: (rolegroupSurrogateId, pageAliasForReset, listPageAndBulkUpdateFeatureFlag) => ([
        navigateToRoleGroupDetailsPage(rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag),
        resetPageParams(pageAliasForReset)
    ])
};

const getRolegroupDuplicateSuccessActions = (pageAlias, rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag) => {
    const mapActions = roleGroupDulicateSuccessActionsPageMap[pageAlias] || roleGroupDulicateSuccessActionsPageMap.default;

    return [
        roleGroupDuplicateCloseDialog(),
        ...mapActions(rolegroupSurrogateId, pageAlias, listPageAndBulkUpdateFeatureFlag)
    ];
};

const roleGroupDuplicateDialogInitiateInterceptor$ = (action$, state$, { apis }) => {

    return action$
        .ofType(ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.INITIATE)
        .pipe(
            switchMap(action =>
                apis[API_KEYS.ROLE_REQUEST_API_KEY].duplicateRoleGroup$(action.payload).pipe(
                    map(response => ({ response, action }))
                )
            ),
            mergeMap(({ response, action }) => {
                const { status, rolegroupSurrogateId } = response;
                const pageAlias = getCurrentPageAliasSelector(state$.value);
                const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

                if (status == ERROR_STATUS) {
                    return of(
                        roleGroupDuplicateError(),
                        roleGroupDuplicateSingleCreateErrorPrompt(
                            pageAlias,
                            action.payload,
                            {}
                        )
                    );
                }

                const isInvalidSurrogateId = !rolegroupSurrogateId && rolegroupSurrogateId != 0;

                if (isInvalidSurrogateId) {
                    return of(roleGroupDuplicateError());
                }

                return of(...getRolegroupDuplicateSuccessActions(pageAlias, rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag));
            })
        );
};

export default combineEpics(
    loadEntityDataEpic$,
    roleGroupDuplicateDialogSetInitialDataEpic$,
    roleGroupDuplicateDialogFieldDataChangeInterceptor$,
    roleGroupDuplicateDialogInitiateInterceptor$
);