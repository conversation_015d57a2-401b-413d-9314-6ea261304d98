import { combineEpics } from 'redux-observable';
import { ROLE_GROUP_LIST_PAGE_ACTIONS, ROLE_GROUP_DETAILS_PAGE_ACTIONS, ROLE_GROUP_DP } from '../actions/actionTypes';
import { updatePageParams, resetPageParams } from '../actions/pageStateActions';
import { getRolegroupListPageParams, getJobEntityFieldValueSelector, getRoleGroupSurrogateId } from '../../src/selectors/dataGridPageSelectors';
import { replaceUrl, pushUrl } from '../actions/navigateActions';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_PAGE_TABLE_DATA_ALIAS } from '../constants/jobsPageConsts';
import { switchMap, map, concatMap, filter } from 'rxjs/operators';
import * as actionTypes from '../actions/actionTypes';
import { deleteRoleGroupSuccess, openRoleGroupCreationModal, populateDPRoles } from '../actions/roleGroupListActions';
import { forkJoin, from, concat, merge, of, empty, Subject, EMPTY } from 'rxjs';
import { CRUD_OPERATIONS, FEATURE_FLAGS, TABLE_NAMES } from '../constants/globalConsts';
import { getFieldInfo, getFieldInfoSelector, getTableStructure, tempGetTableSystemFields } from '../selectors/tableStructureSelectors';
import {
    getCustomLookupSelectionFields,
    getFlatTableData,
    getLinkedSelectionFields,
    getLinkedTableData,
    getTableDatasLoadedActions
} from '../utils/linkedDataUtils';
import {
    JOB_BOOKINGS_CALC_FIELDS,
    JOB_DESCRIPTION,
    JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS,
    ROLEREQUESTGROUP_FIELDS,
    ROLEREQUEST_FIELDS
} from '../constants/fieldConsts';
import { ERROR_PAGES, ROLEGROUPLISTDETAILSPAGE, ROLEGROUPLISTPAGE } from '../pages/pages';
import { createDeleteTableDataEpic, createInsertTableDataEpic, saveRoleGroupRoles$ } from './tableDataEpics';
import { loadRoleGroupListData, setRoleGroupListPageIds, createRoleGroupSuccess, deleteRoleGroup, getRequestedRolesData, setActionableRequestedRolesData } from '../actions/roleGroupListActions';
import { getMergedSelectionFields } from '../selectors/tableFieldsSelectors';
import { getPageState } from '../selectors/pagesSelectors';
import { getSelectionFields } from './dataGridPageDataEpics';
import { SYNTHETIC_BOOKING_NAME, SYNTHETIC_UNGROUPED_ROLE_KEY, TOTAL_ACTIONABLE_REQUEST_COLUMN_NAME, TOTAL_ROLES_COLUMN_NAME } from '../constants/rolegroupListPageConsts';
import { promptAction } from '../actions/promptActions';
import { ERROR_STATUS, PLANNER_PAGE_ALIAS, ROLE_INBOX_PAGE_ALIAS, ROLE_REQUEST_FORM, SUCCESS_STATUS } from '../constants';
import { batchCRUDErrorPrompt } from '../actions/plannerDataActions';
import { entityWindowSubmitFail, entityWindowRoleGroupSubmitUpdate, loadRoleGroupDetails, refreshRoleGroup, updateRoleGroups, refreshRoleGroupTotals } from '../actions/roleGroupDetailsActions';
import { getErrorData, getRolegroupEditErrorPromptAction, getRolegroupBatchSaveActions, getRequestedRoleSelectionConfig, getIsRoleEditable } from '../utils/rolegroupDetailsPageUtils';
import { getAddedRolesSelector } from '../selectors/roleGroupDetailsPageSelectors';
import { replaceBrowserHistoryUrl } from '../history';
import { getCurrentPageAliasSelector, getPageStateParamsSelector } from '../selectors/navigationSelectors';
import { entityWindowClose, entityWindowFieldChanged, entityWindowOpen, entityWindowSetFormErrorMessages } from '../actions/entityWindowActions';
import { setDetailsPaneCollapsed, setDetailsPaneVisibility } from '../actions/detailsPaneActions';
import { ENTITY_WINDOW_MODULES, ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { loadUserEntityAccess } from '../actions/userEntityAccessActions';
import { API_KEYS } from '../constants/apiConsts';
import { clearSuggestedResourcesList } from '../actions/suggestedResourcesActions';
import { getBuiltRolegroupsDpRolesList } from '../utils/rolegroupListPageUtils';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { formValidationMapAction } from './entityWindowEpics';
import { createEntityWindowTableDataChangeInterceptorEpic } from './epicGenerators/entityWindowInterceptors';
import { getSelectionConfig } from './roleGroupDetailsPageEpics';
import { getData } from '../utils/commonUtils';
import { getPageTableDatasSelector } from '../selectors/tableDataSelectors';
import { buildCommonEntityAliasesPlaceholdersSelector } from '../utils/translationUtils';
import { LIST_PAGE_ALIAS } from '../constants/listPageConsts';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';

const getRoleGroupSelectionConfig = (rolegroupSurrogateId) => {
    const selectionConfig = getSelectionConfig(rolegroupSurrogateId);

    return {
        ...selectionConfig,
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: 'rolerequestgroup_guid',
                    operator: 'Equals',
                    value: rolegroupSurrogateId
                }
            ]
        }
    };
};

const { JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, ROLE_GROUP_MODAL } = ENTITY_WINDOW_MODULES;
const apiKey = 'tableData';

const doGetRolegroupDataApiCall$ = (apis, tableName, selection) => {
    return apis[apiKey].getTableData$(tableName, selection);
};

const mapFieldToFieldObject = (fieldName) => typeof(fieldName) === 'object' ? fieldName : { fieldName };
const getRoleGroupListSelectionConfig = (jobSurrogateId) => {
    return {
        fields: [
            {
                fieldName: 'job_description',
                fieldAlias: 'job_description'

            },
            {
                fieldName: 'job_surrogate_id',
                fieldAlias: 'job_surrogate_id'
            },
            {
                fieldName: 'job_guid',
                fieldAlias: 'job_guid'
            }
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: 'job_surrogate_id',
                    operator: 'Equals',
                    value: parseInt(jobSurrogateId)
                }
            ]
        }
    };
};

const entityWindowRoleGroupInsertSuccessInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    ROLE_GROUP_MODAL,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS,
    ROLE_GROUP_MODAL,
    [{ type: `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}` }]
);

const entityWindowRoleGroupInsertErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    ROLE_GROUP_MODAL,
    actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_ERROR,
    ROLE_GROUP_MODAL,
    [
        entityWindowSetFormErrorMessages(ROLE_GROUP_MODAL),
        entityWindowClose(ROLE_GROUP_MODAL)
    ]
);

const entityWindowRoleGroupUpdateSuccessInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    ROLE_GROUP_MODAL,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ROLE_GROUP_MODAL,
    [{ type: `${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS}` }]
);

const entityWindowRoleGroupUpdateErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    ROLE_GROUP_MODAL,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ROLE_GROUP_MODAL,
    [
        entityWindowClose(ROLE_GROUP_MODAL)
    ]
);

const openEditRoleGroupEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.EDIT_ROLE_GROUP)
        .pipe(
            switchMap((action) => {
                const { roleGroupGuid } = action.payload;
                const dataCollections = getPageTableDatasSelector(state$.value);
                const roleEntity = getData(dataCollections, TABLE_NAMES.ROLEREQUESTGROUP, roleGroupGuid);

                return [
                    entityWindowOpen(
                        ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL,
                        TABLE_NAMES.ROLEREQUESTGROUP,
                        TABLE_NAMES.ROLEREQUESTGROUP,
                        ENTITY_WINDOW_OPERATIONS.EDIT,
                        roleEntity,
                        roleGroupGuid
                    )
                ];
            })
        );
};

const editRoleGroupSuccessEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(`${actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS}_${ROLE_GROUP_MODAL}`)
        .pipe(
            switchMap(() => {
                const state = state$.value;
                const roleGroupListPageparams = getPageState(state, ROLE_GROUP_LIST_PAGE);
                const roleGroupDetailsPageParams = getPageState(state, ROLE_GROUP_LIST_PAGE);
                const pageAlias = getCurrentPageAliasSelector(state);
                const actions = [];

                if (pageAlias === ROLE_GROUP_DETAILS_PAGE) {
                    actions.push(
                        entityWindowClose(ROLE_GROUP_MODAL),
                        updatePageParams(ROLE_GROUP_DETAILS_PAGE, roleGroupDetailsPageParams),
                        refreshRoleGroup()
                    );
                } else if (pageAlias === ROLE_GROUP_LIST_PAGE) {
                    actions.push(
                        entityWindowClose(ROLE_GROUP_MODAL),
                        loadRoleGroupListData(roleGroupListPageparams)
                    );
                } else if (pageAlias === ROLE_INBOX_PAGE_ALIAS || pageAlias === PLANNER_PAGE_ALIAS) {
                    actions.push(
                        entityWindowClose(ROLE_GROUP_MODAL)
                    );
                }

                return actions;
            })
        );
};

const navigateToRoleGroupDetailsPage$ = (action$, state$, { apis }) => {
    return action$
        .ofType(`${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${ROLE_GROUP_MODAL}`)
        .pipe(
            switchMap(
                (action) => {
                    const { response: rolegroupSurrogateId } = action.payload;
                    const selection = getRoleGroupSelectionConfig(rolegroupSurrogateId);

                    return doGetRolegroupDataApiCall$(apis, TABLE_NAMES.ROLEREQUESTGROUP, selection);
                }
            ),
            switchMap(([result]) => {
                if (result == undefined || (result && result.length) == 0 || result.status === ERROR_STATUS) {
                    replaceBrowserHistoryUrl(ERROR_PAGES.NOT_FOUND.navigationLink);

                    return EMPTY();
                }

                const rolegroupSurrogateId = result[ROLEREQUESTGROUP_FIELDS.SURROGATE_ID];
                const state = state$.value;
                const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

                return of(...getRolegroupCreateSuccessActions(rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag));
            })
        );
};

const getRolegroupCreateSuccessActions = (rolegroupSurrogateId, listPageAndBulkUpdateFeatureFlag) => {
    const { navigationLink } = ROLEGROUPLISTDETAILSPAGE;

    const newParams = {
        subPageOption: {
            subPageNavLink: navigationLink,
            subPagesParams: {
                rolegroupSurrogateId
            },
            addNewEntity: false
        }
    };

    return [
        entityWindowClose(ROLE_GROUP_MODAL),
        pushUrl(newParams, ROLE_GROUP_DETAILS_PAGE, listPageAndBulkUpdateFeatureFlag ? LIST_PAGE_ALIAS : JOBS_PAGE_ALIAS),
        resetPageParams(ROLE_GROUP_DETAILS_PAGE),
        resetPageParams(ROLE_GROUP_LIST_PAGE)
    ];
};

export const getAppUrlAction = (location, alias) => {
    if ((location.pathname || '').indexOf(alias) > -1) {
        return replaceUrl;
    } else {
        return pushUrl;
    }
};

export const loadRoleGroupList$ = (action$, state$, { apis }) => {
    return action$
        .ofType(ROLE_GROUP_LIST_PAGE_ACTIONS.LOAD.DATA)
        .pipe(
            switchMap((action) => {
                const statePageParams = getRolegroupListPageParams(state$.value);
                const actionPageParams = action.payload.pageParams;
                const { jobSurrogateId } = Object.keys(statePageParams).length > 0 ? statePageParams : actionPageParams;
                const selection = getRoleGroupListSelectionConfig(jobSurrogateId);

                return doGetRolegroupDataApiCall$(apis, TABLE_NAMES.JOB, selection);
            },
            (action, result) => {
                return [result[0], action];
            }),
            switchMap(([result, action]) => {

                if (result == undefined || (result && result.length) == 0 || result.status === ERROR_STATUS) {
                    replaceBrowserHistoryUrl(ERROR_PAGES.NOT_FOUND.navigationLink);

                    return empty();
                }

                const jobDesct = result['job_description'];
                const jobSurrogateId = result['job_surrogate_id'];
                const jobId = result['job_guid'];

                const params = {
                    jobName: jobDesct,
                    jobSurrogateId: jobSurrogateId,
                    jobId
                };
                const { navigationLink } = ROLEGROUPLISTPAGE;

                const newParams = {
                    subPageOption:{
                        subPageNavLink: navigationLink,
                        subPagesParams: params
                    }
                };
                const urlAction = getAppUrlAction(window.location, navigationLink);

                let chain = [
                    of(updatePageParams(ROLE_GROUP_LIST_PAGE, params)),
                    of(urlAction(newParams, ROLE_GROUP_LIST_PAGE))
                ];

                return concat(
                    ...chain
                );
            })
        );
};


export const buildRolegroupDpRoles$ = (action$, state$, { apis }) => {
    return action$
        .ofType(ROLE_GROUP_DP.ROLE_GROUP_BUILD_ROLES)
        .pipe(
            switchMap((action) => {
                const { roles, entityId } = action.payload;
                const builtRoles = getBuiltRolegroupsDpRolesList(roles);

                return of(populateDPRoles(builtRoles, entityId));
            })
        );
};

const getJobGroupRequestsResultHandle = (response, state) => {
    return {
        type :`${actionTypes.LOAD_MORE_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`,
        payload: {
            response: response,
            tableNames: [TABLE_NAMES.ROLEREQUESTGROUP],
            tableDataGuid: TABLE_NAMES.ROLEREQUESTGROUP
        }
    };
};

const jobEntityRequestResultHandle = (response, state) => {
    const jobEntity = response[0];
    const mockBookingName = SYNTHETIC_BOOKING_NAME;

    const mockBookings = [
        {
            [`${mockBookingName}_guid`]: 'planned',
            [`${mockBookingName}_description`]: 'Planned',
            [`${mockBookingName}_totalcost`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST],
            [`${mockBookingName}_totalprofit`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_PROFIT],
            [`${mockBookingName}_totalrevenue`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_REVENUE],
            [`${mockBookingName}_roles_start`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_BOOKINGS_START],
            [`${mockBookingName}_roles_end`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.PLANNED_BOOKINGS_END]
        },
        {
            [`${mockBookingName}_guid`]: 'unconfirmed',
            [`${mockBookingName}_description`]: 'Unconfirmed',
            [`${mockBookingName}_totalcost`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_COST],
            [`${mockBookingName}_totalprofit`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_PROFIT],
            [`${mockBookingName}_totalrevenue`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_REVENUE],
            [`${mockBookingName}_roles_start`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_BOOKINGS_START],
            [`${mockBookingName}_roles_end`]: jobEntity[JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_BOOKINGS_END]
        },
        {
            [`${mockBookingName}_guid`]: SYNTHETIC_UNGROUPED_ROLE_KEY,
            [`${mockBookingName}_description`]: 'Ungrouped roles',
            [TOTAL_ACTIONABLE_REQUEST_COLUMN_NAME]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_ACTION_REQUESTS],
            [TOTAL_ROLES_COLUMN_NAME]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_COUNT],
            [`${mockBookingName}_totalcost`]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_COST],
            [`${mockBookingName}_totalprofit`]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_PROFIT],
            [`${mockBookingName}_totalrevenue`]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_REVENUE],
            [`${mockBookingName}_roles_start`]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_BOOKINGS_START],
            [`${mockBookingName}_roles_end`]: jobEntity[JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_BOOKINGS_END]
        }
    ];

    return {
        type :`${actionTypes.LOAD_MORE_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`,
        payload: {
            response: mockBookings,
            tableNames: [mockBookingName],
            tableDataGuid: mockBookingName
        }
    };
};

const getJobRoleGroupRequest = (apis, displayFieldsSelection, jobId, sortOrders) => {
    const systemFieldInfos = tempGetTableSystemFields(TABLE_NAMES.ROLEREQUESTGROUP);
    const systemFieldsSelection = getSelectionFields(Object.keys(systemFieldInfos || {}));
    const additionalFields = [
        ...systemFieldsSelection,
        ...getSelectionFields([ROLEREQUESTGROUP_FIELDS.TOTALACTIONABLEREQUESTS, ROLEREQUESTGROUP_FIELDS.TOTALROLES])
    ];
    const roleGroupSelection = {
        fields: getMergedSelectionFields(displayFieldsSelection, additionalFields),
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: ROLEREQUESTGROUP_FIELDS.JOB_GUID,
                    operator: 'Equals',
                    value: jobId
                }
            ]
        },
        order: {
            orderFields: sortOrders
        }
    };

    return apis['tableData'].getTableData$(TABLE_NAMES.ROLEREQUESTGROUP, roleGroupSelection);
};

const getSyntheticJobStateRequest = (apis, jobId) => {
    const jobBookingCalcFields = [
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_PROFIT,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_REVENUE,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_TOTAL_COST,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_BOOKINGS_START,
        JOB_BOOKINGS_CALC_FIELDS.PLANNED_BOOKINGS_END,

        JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_COST,
        JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_PROFIT,
        JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_TOTAL_REVENUE,
        JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_BOOKINGS_START,
        JOB_BOOKINGS_CALC_FIELDS.UNCONFIRMED_BOOKINGS_END,

        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_COUNT,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_ACTION_REQUESTS,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_PROFIT,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_REVENUE,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_TOTAL_COST,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_BOOKINGS_START,
        JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS.UNGROUPED_BOOKINGS_END
    ];

    const jobSelection = {
        fields: jobBookingCalcFields.map(mapFieldToFieldObject),
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: 'job_guid',
                    operator: 'Equals',
                    value: jobId
                }
            ]
        }
    };

    return apis['tableData'].getTableData$('job', jobSelection);
};

const loadRoleGroupDataEpic$ = (action$, state$, { apis }) => {
    const initialLoadAction = `UPDATE_PAGE_PARAMS_${ROLE_GROUP_LIST_PAGE}`;
    const columnsChangedAction = `DATA_GRID_COLUMNS_CHANGE_${ROLE_GROUP_LIST_PAGE}`;
    const sortOrderChangedAction = `${actionTypes.DATA_GRID_SORT_CHANGED}_${ROLE_GROUP_LIST_PAGE}`;

    const interceptOpenEpic$ = action$.ofType(initialLoadAction)
        .pipe(
            map(res => {
                const pageParams = res.payload.params || {};
                const { subPageOption = {} } = pageParams;
                const { subPagesParams = {} } = subPageOption;
                const state = state$.value;
                const { displayFields: fieldsToLoad } = state.rolegroupListPage;

                return {
                    fieldsToLoad,
                    subPagesParams
                };
            })
        );

    const interceptColumnsChangedEpic$ = action$.ofType(sortOrderChangedAction)
        .pipe(
            map(action => {
                const state = state$.value;
                const { displayFields: fieldsToLoad } = state.rolegroupListPage;

                return {
                    fieldsToLoad
                };
            })
        );

    const interceptSortChangedEpic$ = action$.ofType(columnsChangedAction)
        .pipe(
            map(action => {
                const state = state$.value;
                const { fieldOptions } = state.rolegroupListPage;
                const fieldsToLoad = Object.keys(fieldOptions).filter(fieldName => !fieldOptions[fieldName].loaded);

                return {
                    fieldsToLoad
                };
            })
        );

    const adapterEpic = merge(
        interceptOpenEpic$,
        interceptColumnsChangedEpic$,
        interceptSortChangedEpic$
    );

    return adapterEpic
        .pipe(
            switchMap(({ fieldsToLoad, subPagesParams = {} }) => {
                const state = state$.value;
                const jobGuid = getRolegroupListPageParams(state).jobId || subPagesParams.jobId;
                const { defaultSortOrder, secondarySortOrder = {}, selection } = state.rolegroupListPage;
                const { order = {} } = selection;
                const { orderFields = [] } = order;
                const sortOrders = orderFields.length ? orderFields : [defaultSortOrder, secondarySortOrder];

                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                const fields = fieldsToLoad.map(mapFieldToFieldObject);

                const selectionFields = [
                    ...fields,
                    ...getLinkedSelectionFields(TABLE_NAMES.ROLEREQUESTGROUP, fields, getFieldInfoWrapped),
                    ...getCustomLookupSelectionFields(TABLE_NAMES.ROLEREQUESTGROUP, fields, getFieldInfoWrapped)
                ];

                const jobGroupRequest = getJobRoleGroupRequest(apis, selectionFields, jobGuid, sortOrders);

                const jobEntityRequest = getSyntheticJobStateRequest(apis, jobGuid);

                return forkJoin([jobGroupRequest, jobEntityRequest]);
            }),
            switchMap(([jobGroupRequestResult, jobEntityRequestResult]) => {
                const state = state$.value;
                const actionsChain = [];

                const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), TABLE_NAMES.ROLEREQUESTGROUP, fieldName);
                const flatData = getFlatTableData(jobGroupRequestResult,TABLE_NAMES.ROLEREQUESTGROUP, getFieldInfoWrapped);
                actionsChain.push(getJobGroupRequestsResultHandle(flatData, state));

                const roleGroupsGuids = flatData.map(rolerequestgroup => rolerequestgroup.rolerequestgroup_guid);
                actionsChain.push(jobEntityRequestResultHandle(jobEntityRequestResult, state));
                actionsChain.push(setRoleGroupListPageIds(roleGroupsGuids));
                actionsChain.push(loadUserEntityAccess(roleGroupsGuids, TABLE_NAMES.ROLEREQUESTGROUP));
                actionsChain.push(setDetailsPaneVisibility(false, ROLE_GROUP_LIST_PAGE));

                const actionRequiredRoleGroupsGuids = flatData.reduce((acc, rolerequestgroup) => {
                    if (rolerequestgroup.rolerequestgroup_totalactionablerequests > 0) {
                        acc.push(rolerequestgroup.rolerequestgroup_guid);
                    }

                    return acc;
                }, []);

                if (actionRequiredRoleGroupsGuids.length) {
                    actionsChain.push(getRequestedRolesData(actionRequiredRoleGroupsGuids));
                }

                const linkedTableDatas = getLinkedTableData(jobGroupRequestResult, TABLE_NAMES.ROLEREQUESTGROUP, getFieldInfoWrapped);
                const linkedTableDatasLoadedActions = getTableDatasLoadedActions(ROLE_GROUP_PAGE_TABLE_DATA_ALIAS, linkedTableDatas);
                linkedTableDatasLoadedActions.forEach(action => actionsChain.push(action));

                return from(actionsChain);
            })
        );
};

export const viewRoleGroupListPageRequestEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.VIEW_ROLE_GROUP_LIST_PAGE_REQUEST)
        .pipe(
            switchMap(({ payload: { entityId, tableName } }) => {
                const surrogateId = getJobEntityFieldValueSelector(state$.value)(entityId, [`${tableName}_surrogate_id`]);
                const description = getJobEntityFieldValueSelector(state$.value)(entityId, [`${tableName}_description`]);
                const { navigationLink } = ROLEGROUPLISTPAGE;

                const params = {
                    jobName: description,
                    jobSurrogateId: surrogateId
                };

                const newParams = {
                    subPageOption: {
                        subPageNavLink: navigationLink,
                        subPagesParams: params
                    }
                };

                return of(pushUrl(newParams));
            })
        );
};

export const refreshRoleGroupDataEpic$ = (action$, state$) => {
    const refreshActions = [
        `${actionTypes.INSERT_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`,
        `${actionTypes.DELETE_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`
    ];

    return action$
        .ofType(...refreshActions)
        .pipe(
            filter(action => action.payload.pageAlias === ROLE_GROUP_LIST_PAGE),
            switchMap(({ payload, type }) => {
                const params = getPageState(state$.value, ROLE_GROUP_LIST_PAGE);
                let resultAction = [];
                const { pageAlias } = payload;

                if (pageAlias === ROLE_GROUP_LIST_PAGE && type === `${actionTypes.DELETE_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`) {
                    resultAction.push(setDetailsPaneVisibility(false, ROLE_GROUP_LIST_PAGE));
                    resultAction.push(setDetailsPaneCollapsed(ROLE_GROUP_LIST_PAGE, true, ROLE_GROUP_LIST_PAGE));
                    resultAction.push(entityWindowClose(JOBS_PAGE_ROLE_GROUP_DETAILS_PANE));
                }

                resultAction.push(loadRoleGroupListData(params));

                return from(resultAction);
            })
        );
};

export const externalRefreshRoleGroupDataEpic$ = (action$) => {
    const refreshActions = [
        `${actionTypes.INSERT_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`,
        `${actionTypes.DELETE_TABLE_DATA_SUCCESSFUL}_${ROLE_GROUP_PAGE_TABLE_DATA_ALIAS}`
    ];

    return action$
        .ofType(...refreshActions)
        .pipe(
            filter(action => [JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE].includes(action.payload.pageAlias)),
            switchMap(({ payload }) => {
                let resultAction = [];
                resultAction.push(resetPageParams(ROLE_GROUP_LIST_PAGE));
                const { surrogateId, jobDescription } = payload;
                const { navigationLink } = ROLEGROUPLISTPAGE;
                const params = {
                    jobName: jobDescription,
                    jobSurrogateId: surrogateId
                };
                const newParams = {
                    subPageOption: {
                        subPageNavLink: navigationLink,
                        subPagesParams: params
                    }
                };

                resultAction.push(pushUrl(newParams));

                return from(resultAction);
            })
        );
};

export const roleGroupCreationModalOpenRequestEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ROLE_GROUP_CREATION_MODAL.OPEN_REQUEST)
        .pipe(
            switchMap(({ payload: { entityId, tableName } }) => {
                const state = state$.value;
                const { page } = state.navigation;
                const entityName = getJobEntityFieldValueSelector(state)(entityId, `${tableName}_description`);
                const surrogateId = getJobEntityFieldValueSelector(state)(entityId, [`${tableName}_surrogate_id`]);

                return of(openRoleGroupCreationModal(page, entityId, entityName, surrogateId));
            })
        );
};

const deleteRoleGroupEpic$ = (action$, state$, { apis }) => {
    const deleteRoleGroupFromRoleGroupListPage$ = action$.ofType(ROLE_GROUP_LIST_PAGE_ACTIONS.DELETE)
        .pipe(
            switchMap((action) => {
                let { roleGroupId } = action.payload;

                return of(promptAction(deleteRoleGroup(roleGroupId, ROLE_GROUP_LIST_PAGE), ROLE_GROUP_LIST_PAGE));
            })
        );

    return deleteRoleGroupFromRoleGroupListPage$;
};

const updateRoleGroupEpic$ = (action$, state$, { apis }) => {
    const dispatchDirectlySubject$ = new Subject();
    const validateEW$ = action$.ofType(ROLE_GROUP_DETAILS_PAGE_ACTIONS.UPDATE)
        .pipe(
            switchMap(
                (action) => {
                    const { moduleName, entityIds } = action.payload;
                    const window = state$.value.entityWindow.window[moduleName];
                    const validateIds = entityIds || window.batchIds;

                    const patchData = validateIds.reduce((accumulator, id) => {
                        const activeEntity = window.windows[id];
                        const tableData = activeEntity.entity || activeEntity;
                        const shouldValidate = getIsRoleEditable(state$.value, tableData[ROLEREQUEST_FIELDS.STATUS_GUID], id);

                        if (shouldValidate) {
                            accumulator.push({ tableDataEntryGuid: id, tableData });
                        }

                        return accumulator;
                    }, []);

                    const followUpAction = {
                        type: action.type,
                        payload: {
                            ...action.payload,
                            patchData,
                            entityPayloadAlias: 'patchData',
                            tableName: TABLE_NAMES.ROLEREQUEST,
                            moduleName: ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM,
                            isBatch: true
                        }
                    };

                    return patchData.length > 0 ? formValidationMapAction(state$, followUpAction) : of([]);
                },
                (action, result) => [result, action]
            ),
            map(([validateErrors, action]) => {
                let errorActions = [];

                if (validateErrors && Array.isArray(validateErrors)) {
                    errorActions = validateErrors.filter(a => a != null);
                }

                let result = {
                    hasErrors: false,
                    action
                };

                if (errorActions.length != 0) {
                    result = {
                        hasErrors: true,
                        errorActions
                    };
                }

                return result;
            })
        );

    const makeRequest$ = validateEW$
        .pipe(
            filter(result => result.hasErrors === false),
            map(result => result.action),
            switchMap((action) => {
                const state = state$.value;
                const { moduleName } = action.payload;
                const { activeEntity } = state.entityWindow.window[moduleName];
                const { rolerequestgroupGuid, changedRolegroupName } = getPageStateParamsSelector(state)(ROLE_GROUP_DETAILS_PAGE);

                const batchUpdateActions = getRolegroupBatchSaveActions(state$);

                dispatchDirectlySubject$
                    .next(entityWindowRoleGroupSubmitUpdate(moduleName, activeEntity));

                return saveRoleGroupRoles$(
                    apis[API_KEYS.ROLE_REQUEST_API_KEY],
                    apis[API_KEYS.TABLE_DATA],
                    {
                        changedParentEntityName: changedRolegroupName,
                        parentGuid: rolerequestgroupGuid,
                        batchUpdateActions,
                        tableName: TABLE_NAMES.ROLEREQUEST,
                        parentTableName: TABLE_NAMES.ROLEREQUESTGROUP
                    }
                );
            },
            (action, data) => { return { action, data }; }),
            switchMap(result => {
                const state = state$.value;
                const { action, data } = result;
                const { moduleName, location } = action.payload;
                const { failedEntities = [], context = {} } = (data || {});
                const { activeEntity, batchIds } = state.entityWindow.window[moduleName];

                let resultActions = [];

                if (failedEntities.length == 0) {
                    const { changedRolegroupName, rolegroupName } = getPageStateParamsSelector(state)(ROLE_GROUP_DETAILS_PAGE);
                    const activeEntityIndex = batchIds.indexOf(activeEntity);

                    resultActions = [
                        loadRoleGroupDetails(false, activeEntityIndex, { location: location }),
                        updatePageParams(ROLE_GROUP_DETAILS_PAGE, { rolegroupName: changedRolegroupName || rolegroupName }),
                        refreshRoleGroup(),
                        clearSuggestedResourcesList(activeEntity),
                        refreshRoleGroupTotals()
                    ];
                } else {
                    const pageAlias = getCurrentPageAliasSelector(state);
                    const errorData = getErrorData(state, failedEntities, context);

                    resultActions = [
                        getRolegroupEditErrorPromptAction(result, pageAlias, errorData),
                        entityWindowSubmitFail(moduleName, activeEntity)
                    ];
                }

                return from(resultActions);
            })
        );

    const dispatchErrorActions$ = validateEW$
        .pipe(
            filter(result => result.hasErrors === true),
            switchMap(result => from([...result.errorActions]))
        );

    return merge(
        makeRequest$,
        dispatchErrorActions$,
        dispatchDirectlySubject$
    );
};

const submitUpdateForRoleGroupEpic$ = (action$, state$) => {
    return action$.ofType(ROLE_GROUP_DETAILS_PAGE_ACTIONS.SUBMIT_UPDATE)
        .pipe(
            switchMap(action => {
                const { moduleName } = action.payload;

                return from([
                    updateRoleGroups(moduleName)
                ]);
            })
        );
};

const updateRoleGroupInterceptor$ = (action$, state$) => {
    return action$
        .ofType(`${actionTypes.BATCH_CRUD_ERROR}_${CRUD_OPERATIONS.UPDATE}_rolegrouplist`)
        .pipe(
            concatMap(({ type, payload }) => {
                const { response } = payload;
                const actions = [];
                const successfulOperations = [];
                const failedOperations = [];

                response.forEach((res) => {
                    if (res.type === SUCCESS_STATUS) {
                        successfulOperations.push(res);
                    } else {
                        failedOperations.push(res);
                    }
                });

                const failedOperationsCount = failedOperations.length;
                const attemptedOperationsCount = response.length;
                const succeededOperationsCount = attemptedOperationsCount - failedOperationsCount;

                const addedEntitiesTableData = getAddedRolesSelector(state$.value.entityWindow.window[ROLE_REQUEST_FORM]);
                let errorsCollection = [];

                if (failedOperationsCount > 0) {
                    let hasCreationErrors = false;
                    failedOperations.forEach(res => {
                        const { result = {}, status } = res;
                        if (!res.id && !hasCreationErrors) {
                            addedEntitiesTableData.forEach((tableData) => {
                                errorsCollection.push({
                                    id: tableData[`${TABLE_NAMES.ROLEREQUEST}_guid`],
                                    error: {
                                        hasError: true,
                                        message: result.message ? result.message : null
                                    }
                                });
                            });
                            hasCreationErrors = true;
                        } else if (res.id) {
                            errorsCollection.push({
                                id: res.id,
                                error: {
                                    hasError: true,
                                    message: result.message ? result.message : null
                                }
                            });
                        }
                    });

                    const modalContext = {
                        requestOperation: CRUD_OPERATIONS.UPDATE,
                        tableName: TABLE_NAMES.ROLEREQUEST,
                        errorsCollection,
                        failedOperationsCount,
                        attemptedOperationsCount,
                        succeededOperationsCount
                    };
                    const currentPageAlias = state$.value.navigation.subPage || state$.value.navigation.page;

                    actions.push(batchCRUDErrorPrompt(currentPageAlias, { ...payload }, modalContext));
                }

                return from(actions);
            })
        );
};

export const getRequestedRoleDataEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.GET_REQUESTED_ROLE_DATA)
        .pipe(
            switchMap((action) => {
                const { roleGroupIds } = action.payload;
                const selection = getRequestedRoleSelectionConfig(roleGroupIds);

                return doGetRolegroupDataApiCall$(apis, TABLE_NAMES.ROLEREQUEST, selection);
            },
            (action, result) => {
                return [result, action];
            }),
            switchMap(([result, action]) => {
                const currentUserGuid = getApplicationUserId(state$.value);
                let chain = [
                    of(setActionableRequestedRolesData(result, currentUserGuid))
                ];

                return concat(
                    ...chain
                );
            })
        );
};

const buildRoleGroupDescriptionValue = (state, jobName) => {
    const { rolerequestgroupSingularLowerAlias } = buildCommonEntityAliasesPlaceholdersSelector(state);

    return `${jobName} ${rolerequestgroupSingularLowerAlias}`;
};

const openCreateRoleGroupEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.OPEN_CREATE_ROLE_GROUP)
        .pipe(
            switchMap((action) => {
                const state = state$.value;
                const { tableName, jobId, entity } = action.payload;
                const { [JOB_DESCRIPTION]: jobDescription } = entity;

                const actions = [];
                const surrogateId = getRoleGroupSurrogateId(state);
                const fieldInfo = getFieldInfoSelector(state)(TABLE_NAMES.ROLEREQUESTGROUP, ROLEREQUESTGROUP_FIELDS.JOB_GUID);
                const rolegroupDescription = jobDescription ? buildRoleGroupDescriptionValue(state, jobDescription) : null;

                actions.push(
                    entityWindowOpen(
                        ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL,
                        TABLE_NAMES.ROLEREQUESTGROUP,
                        TABLE_NAMES.ROLEREQUESTGROUP,
                        ENTITY_WINDOW_OPERATIONS.CREATE,
                        {
                            [ROLEREQUESTGROUP_FIELDS.DESCRIPTION]: rolegroupDescription
                        },
                        null,
                        false
                    )
                );

                if (tableName) {
                    actions.push(entityWindowFieldChanged(
                        ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL,
                        fieldInfo,
                        {
                            value: jobDescription,
                            id: jobId
                        },
                        surrogateId,
                        tableName
                    ));
                }

                return actions;
            })
        );
};

export default function () {
    return combineEpics(
        loadRoleGroupList$,
        loadRoleGroupDataEpic$,
        buildRolegroupDpRoles$,
        viewRoleGroupListPageRequestEpic$,
        refreshRoleGroupDataEpic$,
        externalRefreshRoleGroupDataEpic$,
        roleGroupCreationModalOpenRequestEpic$,
        deleteRoleGroupEpic$,
        updateRoleGroupEpic$,
        submitUpdateForRoleGroupEpic$,
        createInsertTableDataEpic(ROLE_GROUP_PAGE_TABLE_DATA_ALIAS, createRoleGroupSuccess)(),
        createDeleteTableDataEpic(ROLE_GROUP_PAGE_TABLE_DATA_ALIAS, deleteRoleGroupSuccess)(),
        updateRoleGroupInterceptor$,
        getRequestedRoleDataEpic$,
        entityWindowRoleGroupInsertSuccessInterceptor(),
        entityWindowRoleGroupInsertErrorInterceptor(),
        navigateToRoleGroupDetailsPage$,
        openCreateRoleGroupEpic$,
        openEditRoleGroupEpic$,
        entityWindowRoleGroupUpdateSuccessInterceptor(),
        entityWindowRoleGroupUpdateErrorInterceptor(),
        editRoleGroupSuccessEpic$
    );
}
