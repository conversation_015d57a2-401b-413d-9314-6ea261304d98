import { filter, flatMap, switchMap } from 'rxjs/operators';
import * as actionTypes from '../../actions/actionTypes';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { JOBS_PAGE_MODAL_ALIAS } from '../../constants/jobsPageConsts';
import { ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, PLANNER_MASTER_REC_ALIAS, PLANNER_SUB_REC_ALIAS } from '../../constants/plannerConsts';
import { PROFILE_PAGE_ALIAS, TALENT_PROFILE_ALIAS } from '../../constants/talentProfileConsts';
import { getEntityWindowCustomSettings } from '../../selectors/entityWindowSelectors';
import { entityWindowClose, entityWindowFieldChanged, entityWindowFieldChangedContextually, entityWindowSetFieldErrors } from '../../actions/entityWindowActions';
import { combineEpics } from 'redux-observable';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getData, isDetailsPane } from '../../utils/commonUtils';
import { talentProfileFieldChangedContextually, talentProfileSetFieldErrors } from '../../actions/talentProfileActions';
import { rollForwardDialogOptionDataChange } from '../../actions/rollForwardActions';
import { ROLL_FORWARD_DIALOG_ALIAS } from '../../constants/rollForwardConst';
import { EDIT_ALL_ENTITY_ID } from '../../constants/entityWindowConsts';
import { entityWindowFieldChangedForAllEntities } from '../../actions/entityWindowActions';
import { getEntityWindowByModuleNameSelector } from '../../selectors/entityWindowSelectors';
import { JOB_DUPLICATE_DIALOG_ALIAS } from '../../constants/jobDuplicateConsts';
import { jobDuplicateDialogOptionDataChange } from '../../actions/jobDuplicateActions';
import { getPageTableDatasSelector } from '../../selectors/tableDataSelectors';

const GLOBAL_CREATE_ACTIONS = {
    POPULATE_NEW_ENTITY: 'POPULATE_NEW_ENTITY'
};

function globalCreatePopulateNewEntityAction(sourceModal, sourceTableName, sourceField, fieldId, fieldValue, sourceEntityId) {
    return {
        type: `${GLOBAL_CREATE_ACTIONS.POPULATE_NEW_ENTITY}_${sourceModal}`,
        payload: {
            sourceModal,
            sourceField,
            fieldId,
            fieldValue,
            sourceEntityId,
            sourceTableName
        }
    };
}

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_MODAL_SIMPLIFIED,
    PLANNER_PAGE_DETAILS_PANE,
    TABLE_VIEW_MODAL,
    TABLE_VIEW_MODAL_SIMPLIFIED,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_REQUEST_FORM,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    JOBS_PAGE_BATCH_MODAL,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    PLANNER_PAGE_MOVE_TO_PROMPT,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    NOTIFICATION_PAGE_MODAL
} = ENTITY_WINDOW_MODULES;

const globalCreateEntityWindowCloseTypes = [
    `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${JOBS_PAGE_MODAL_ALIAS}`,
    `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`,
    `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PLANNER_MASTER_REC_ALIAS}`,
    `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PLANNER_SUB_REC_ALIAS}`,
    `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${PROFILE_PAGE_ALIAS}`
];

const globalCreateEntityWindowCloseEpic = () => (action$, state$) => {
    return action$.ofType(...globalCreateEntityWindowCloseTypes).pipe(
        filter(({ payload }) => payload.moduleName === ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL),
        flatMap(({ payload }) => {
            const state = state$.value;
            const { moduleName, response, tableName, tableData } = payload;
            const { sourceModal, sourceField, sourceTableName, sourceEntityId } = getEntityWindowCustomSettings(state.entityWindow, moduleName);
            const fieldValue = tableData[`${tableName}_description`];

            return [
                globalCreatePopulateNewEntityAction(sourceModal, sourceTableName, sourceField, response, fieldValue, sourceEntityId),
                entityWindowClose(ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL)
            ];
        })
    );
};

const globalCreateEntityWindowHandleCloseEpic = () => (action$, state$) => {
    return action$.ofType(`${actionTypes.ENTITY_WINDOW.CLOSE_GLOBAL_CREATE_MODAL}_${ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL}`).pipe(
        switchMap(({ payload }) => {
            const { moduleName } = payload;
            const state = state$.value;

            const sourceEwProps = getEntityWindowCustomSettings(state.entityWindow, moduleName);
            const { sourceModal, sourceField, sourceTableName, sourceEntityId, sourceLinkTable, sourceFieldValueId } = sourceEwProps;
            const dataCollections = getPageTableDatasSelector(state);
            const tableData = getData(dataCollections, sourceLinkTable, sourceFieldValueId);
            const sourceDisplayValue = (tableData || {})[`${sourceLinkTable}_description`];

            return [
                globalCreatePopulateNewEntityAction(sourceModal, sourceTableName, sourceField, sourceFieldValueId, sourceDisplayValue, sourceEntityId),
                entityWindowClose(ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL)
            ];
        })
    );
};

const populateNewEntityInterceptor$ = (moduleName, handler) => (action$, state$) => {
    return action$
        .ofType(`${GLOBAL_CREATE_ACTIONS.POPULATE_NEW_ENTITY}_${moduleName}`)
        .pipe(flatMap(({ payload }) => {
            const state = state$.value;
            const { sourceModal, sourceField, fieldId, fieldValue, sourceEntityId, sourceTableName } = payload;
            const fieldInfo = getFieldInfoSelector(state)(sourceTableName, sourceField);
            const { batchIds = [] } = getEntityWindowByModuleNameSelector(state)(moduleName);

            return handler({
                fieldInfo,
                moduleName: sourceModal,
                tableName: sourceTableName,
                entityId: sourceEntityId,
                fieldValue: {
                    value: fieldValue,
                    id: fieldId
                },
                batchIds
            });
        }));
};

const entityWindowFieldChangedHandler = ({ tableName, moduleName, entityId, fieldInfo, fieldValue, batchIds }) => {
    const actions = [entityWindowSetFieldErrors(moduleName, {
        [fieldInfo.name]: { errors: [] }
    })];

    if (isDetailsPane(moduleName)) {
        actions.push(entityWindowFieldChangedContextually(moduleName, fieldInfo, fieldValue, entityId, tableName));
    } else if (entityId === EDIT_ALL_ENTITY_ID) {
        actions.push(entityWindowFieldChangedForAllEntities(moduleName, fieldInfo, fieldValue, batchIds));
    } else {
        actions.push(entityWindowFieldChanged(moduleName, fieldInfo, fieldValue, entityId, tableName));
    }

    return actions;
};

const talentProfileFieldChangedHandler = ({ fieldInfo, fieldValue }) => {
    return [
        talentProfileSetFieldErrors({ [fieldInfo.name]: { errors: [] } }),
        talentProfileFieldChangedContextually(fieldInfo, fieldValue)
    ];
};

const rollForwardDialogOptionDataChangeHandler = ({ fieldInfo: { name }, fieldValue }) => {
    const payload = {
        [name]: {
            value: fieldValue
        },
        name
    };

    return [rollForwardDialogOptionDataChange(payload)];
};

const duplicateJobDialogOptionDataChangeHandler = ({ fieldInfo: { name }, fieldValue }) => {
    const payload = { [name]: { value: fieldValue }, name };

    return [jobDuplicateDialogOptionDataChange(payload)];
};

export default function () {
    return combineEpics(
        populateNewEntityInterceptor$(PLANNER_PAGE_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(PLANNER_PAGE_MODAL_SIMPLIFIED, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(PLANNER_PAGE_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(TABLE_VIEW_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(TABLE_VIEW_MODAL_SIMPLIFIED, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(RESOURCES_PAGE_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(RESOURCES_PAGE_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_INBOX_PAGE_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_INBOX_PAGE_MODAL_SIMPLIFIED, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_RESOURCE_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_REQUEST_FORM, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_INBOX_PAGE_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(PLANNER_PAGE_BATCH_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(PLANNER_PAGE_BATCH_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_BATCHED_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_BATCH_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(RESOURCES_PAGE_BATCHED_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(RESOURCES_PAGE_BATCH_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_INBOX_PAGE_BATCH_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(PLANNER_PAGE_MOVE_TO_PROMPT, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(ROLE_DETAILS_PAGE_MOVE_TO_PROMPT, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(NOTIFICATION_PAGE_MODAL, entityWindowFieldChangedHandler),
        populateNewEntityInterceptor$(TALENT_PROFILE_ALIAS, talentProfileFieldChangedHandler),
        populateNewEntityInterceptor$(ROLL_FORWARD_DIALOG_ALIAS, rollForwardDialogOptionDataChangeHandler),
        populateNewEntityInterceptor$(JOB_DUPLICATE_DIALOG_ALIAS, duplicateJobDialogOptionDataChangeHandler),

        globalCreateEntityWindowCloseEpic(),
        globalCreateEntityWindowHandleCloseEpic()
    );
}

