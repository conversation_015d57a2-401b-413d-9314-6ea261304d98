import { of, empty, from } from 'rxjs';
import { switchMap, mergeMap, map, filter } from 'rxjs/operators';
import { getFieldTableName } from '../utils/tableStructureUtils';
import * as actionTypes from '../actions/actionTypes';
import { loadPagedAccessDataSuccess, loadPagedResultsDataSuccess, loadAdditionalColumnsSuccess, setPagedDataLoading, pagedDataRegisterKeepAlive, batchPatchPagedTableDataSuccess } from '../actions/pagedDataActions';
import { dataGridPageChangeSuccess, dataGridLoadData, dataGridColumnsChangeSuccess, dataGridPageChange, reloadJobsPageDataGridAction } from '../actions/dataGridActions';
import { cloneDeep } from 'lodash';
import { getFieldInfo, getTableStructure, tempGetTableSystemFields } from '../selectors/tableStructureSelectors';
import { DATA_GRID_PAGED_DATA_SUFFIX, DATA_GRID_TABLE_DATAS_SUFFIX, DATA_GRID_FILTERS_SUFFIX, DATA_GRID_INITIAL_PAGE_NUMBER, DEFAULT_MAX_RECORDS } from '../constants/dataGridConsts';
import { completeChain } from './chainUtils';
import { omit } from '../utils/commonUtils';
import { batchActions } from 'redux-batched-actions';
import { getLinkedTableData, getFlatTableData, getTableDatasLoadedActions, getLinkedSelectionFields, getCustomLookupSelectionFields, getDoubleLinkedSelectionFields } from '../utils/linkedDataUtils';
import { ERROR_STATUS, ENTITY_WINDOW_MODULES } from '../constants';
import { getMergedSelectionFields } from '../selectors/tableFieldsSelectors';
import { getIsCustomField, getSkillFieldsSelection } from '../utils/fieldUtils';
import { entityWindowClose } from '../actions/entityWindowActions';
import { ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS } from '../constants/plannerConsts';
import { pushUrl, replaceUrl } from '../actions/navigateActions';
import { FEATURE_FLAGS, JOIN_TYPES, OPERATORS, TABLE_NAMES, URL_PARAMS } from '../constants/globalConsts';
import { getEntityGuids, getEntityLinkedGuids } from '../utils/plannerDataUtils';
import { loadUserEntityAccess } from '../actions/userEntityAccessActions';
import { selectEdits } from '../actions/tableDataActions';
import { commonDataBatchInterceptorEpic } from '../epics/plannerDataEpics';
import { CRUD_OPERATIONS } from '../constants/globalConsts';
import { JOBS_PAGE_ALIAS, FILTER_FIELD_NAMES } from '../constants';
import { setDetailsPaneVisibility, setDetailsPaneCollapsed } from '../actions/detailsPaneActions';
import { JOBS_PAGE_MODAL_ALIAS, JOB_PAGE_PAGED_DATA } from '../constants/jobsPageConsts';
import { createEntityWindowTableDataChangeInterceptorEpic } from './epicGenerators/entityWindowInterceptors';
import { getCurrentPageAliasSelector } from '../selectors/navigationSelectors';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { RESOURCE_USERSTATUS, ROLEREQUESTGROUP_FIELDS, ROLEREQUEST_FIELDS, RESOURCE_FIELDS, ROLEMARKETPLACE_FIELDS, JOB_TOTAL_RAGHEALTH_GUID, JOB_TOTAL_RAGHEALTH_DESCRIPTION, JOB_JOBSTATUS_GUID, RESOURCE_CMERED, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMEYELLOW, RESOURCE_BOOKING_CONFLICT_DATES, RESOURCE_BOOKING_POTENTIAL_CONFLICT_DATES } from '../constants/fieldConsts';
import { loadAvatars } from '../actions/avatarActions';
import { AVATAR_SIZES } from '../constants/avatarConsts';
import { PEOPLE_FINDER_DIALOG_ALIAS } from '../constants/peopleFinderConst';
import { MARKETPLACE_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { getPopulateExpandedCriteriasAction } from '../utils/requirementsUtils';
import { loadCriteriaValuesForMultipleRoles } from '../actions/criteriaActions';
import { API_KEYS } from '../constants/apiConsts';
import { OPERATION_LOG_DIALOG_ALIAS, OPERATION_STATE_NAME, OPERTION_LOG_SORT_NAME_FIELD_MAP } from '../constants/operationLogDialogConsts';
import { TABLE_SYSTEM_FIELDS } from '../constants/tablesConsts';
import { getLicenseValuesByKeySelector } from '../selectors/commonSelectors';
import { LICENSE_KEYS_ADMIN_SETTINGS } from '../constants/globalConsts';
import { MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT } from '../constants/paginationConsts';
import { PLANNER_PAGE_ALIAS } from '../constants/plannerConsts';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

const { licensePlannerPageMaxInfiniteScrollRows } = LICENSE_KEYS_ADMIN_SETTINGS;

const {
    PAGE,
    PAGE_SIZE
} = URL_PARAMS;

const buildPageParams = (page, pageSize) => {
    return {
        [PAGE]: page,
        [PAGE_SIZE]: pageSize
    };
};

const getDataGridPageEndChainErrorAction = (state$) => () => {
    let result = null;

    const state = state$.value;
    const currentPageAlias = getCurrentPageAliasSelector(state);
    const dataGridPages = [JOBS_PAGE_ALIAS, ROLE_INBOX_PAGE_ALIAS];

    if (dataGridPages.includes(currentPageAlias)) {
        const { pagedData = {}, tableName } = state[currentPageAlias];
        const { loading } = pagedData[tableName];

        if (!loading) {
            result = dataGridLoadData(currentPageAlias);
        }
    }

    return result;
};

export const getSelectionFields = (fields) => {
    const fieldName = 'fieldName';

    return fields.map(field => {
        if (!field[fieldName]) {
            return { [fieldName]: field };
        }

        return field;
    });
};

export const dataGridPageExists = (pageNumber, rowCount, pageSize) => {
    return pageNumber > 0 && pageNumber <= Math.ceil(rowCount / pageSize);
};

const getAppUrlAction = (actionType, alias) => {
    switch (actionType) {
        case `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`:
        case `${actionTypes.PAGED_DATA_PAGE_SIZE_CHANGED}_${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`:
            return pushUrl;
        default:
            return replaceUrl;
    }
};

export const getLoadFilterAccessData$ = (table, ids, fields, apis) => {
    const selection = {
        fields: fields.map((fieldName) => {
            return typeof (fieldName) === 'object' ? fieldName : { fieldName };
        }),
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: `${table}_guid`,
                    operator: 'Contains',
                    value: ids
                }
            ]
        }
    };

    return apis['tableData']
        .getTableData$(table, selection)
        .pipe(
            switchMap((response) => {
                let data = [];

                if (response.status !== ERROR_STATUS)
                    data = response;

                return of({ table, data });
            })
        );
};

export const getSelectionOrder = (initOrder, primaryTableName, getFieldInfo) => {
    return {
        ...initOrder,
        orderFields: initOrder.orderFields.map((item) => {
            const fieldName = item.field;
            const fieldInfo = getFieldInfo(primaryTableName, fieldName);
            const fieldTableName = getFieldTableName(fieldInfo, primaryTableName);
            const isJoinedNonLinkedOrderField = !fieldInfo && item.tableName !== primaryTableName;

            let result = item;

            if (fieldName === JOB_TOTAL_RAGHEALTH_GUID) {
                result = {
                    ...item,
                    field: JOB_TOTAL_RAGHEALTH_DESCRIPTION
                };
            } else if (!isJoinedNonLinkedOrderField && primaryTableName !== fieldTableName && !getIsCustomField(fieldInfo)) {
                result = {
                    ...item,
                    field: `${fieldName}.${fieldTableName}_description`
                };
            } else if (primaryTableName === OPERATION_STATE_NAME) {
                const mappedFieldName = OPERTION_LOG_SORT_NAME_FIELD_MAP[item.field];
                result = {
                    ...item,
                    field: mappedFieldName
                };
            }

            return result;
        })
    };
};

const getAdditionalSelectionFields = (pageAlias, state) => {
    const currentMainPage = getCurrentPageAliasSelector(state);

    let additionalFields = [];

    if (pageAlias === ROLE_INBOX_PAGE_ALIAS) {
        additionalFields = [
            { fieldName: ROLEREQUEST_FIELDS.DESCRIPTION },
            { fieldName: ROLEREQUEST_FIELDS.FTE },
            { fieldName: `${ROLEREQUEST_FIELDS.RESOURCE_GUID}.${RESOURCE_USERSTATUS}` },
            { fieldName: `${ROLEREQUEST_FIELDS.ROLE_GROUP_GUID}.${ROLEREQUESTGROUP_FIELDS.SURROGATE_ID}` },
            { fieldName: ROLEREQUEST_FIELDS.FIXEDTIME },
            { fieldName: ROLEREQUEST_FIELDS.HOURS_PER_DAY },
            { fieldName: ROLEREQUEST_FIELDS.RESOURCE_DEMAND },
            // { fieldName: ROLEREQUEST_FIELDS.POTENTIAL_CONFLICTS },
            { fieldName: ROLEREQUEST_FIELDS.FILLED_PERCENTAGE },
            { fieldName: ROLEREQUEST_FIELDS.CAN_PUBLISH },
            { fieldName: ROLEREQUEST_FIELDS.ROLEREQUEST_CALLER_APPLY_DATE },
            { fieldName: ROLEREQUEST_FIELDS.ROLEREQUEST_IS_CALLER_APPLICANT },
            { fieldName: ROLEREQUEST_FIELDS.PUBLICATION_GUID },
            { fieldName: `${ROLEREQUEST_FIELDS.JOB_GUID}.${JOB_JOBSTATUS_GUID}` }
        ];
    }

    if (pageAlias === PEOPLE_FINDER_DIALOG_ALIAS) {
        additionalFields = [
            { fieldName: FILTER_FIELD_NAMES.RESOURCE_SURROGATE_ID },
            { fieldName: RESOURCE_CMERED },
            { fieldName: RESOURCE_CMEBLUE },
            { fieldName: RESOURCE_CMEGREEN },
            { fieldName: RESOURCE_CMEYELLOW },
            { fieldName: RESOURCE_FIELDS.SKILL_EXPIRY_STATUS }
        ];

        if (currentMainPage === PLANNER_PAGE_ALIAS) {
            additionalFields.push({ fieldName: RESOURCE_FIELDS.AVAILABLE_TIME });
        }
    }

    if (pageAlias === MARKETPLACE_PAGE_ALIAS) {
        additionalFields = [
            { fieldName: 'rolerequest_rolerequeststatus_guid.rolerequeststatus_description', fieldAlias: ROLEREQUEST_FIELDS.STATUS_DESCRIPTION },
            { fieldName: 'rolerequest_job_guid.job_description', fieldAlias: ROLEREQUEST_FIELDS.JOB_DESCRIPTION },
            { fieldName: 'rolemarketplace_rolecategory_guid.rolecategory_description', fieldAlias: ROLEREQUEST_FIELDS.CATEGORY, tableName: TABLE_NAMES.ROLEMARKETPLACE },
            { fieldName: 'rolemarketplace_publishedon', fieldAlias: ROLEREQUEST_FIELDS.PUBLISHEDON, tableName: TABLE_NAMES.ROLEMARKETPLACE },
            { fieldName: 'rolemarketplace_criteriamatch', fieldAlias: ROLEREQUEST_FIELDS.CRITERIA_MATCH, tableName: TABLE_NAMES.ROLEMARKETPLACE },
            { fieldName: ROLEREQUEST_FIELDS.ROLEREQUEST_CALLER_APPLY_DATE, fieldAlias: ROLEREQUEST_FIELDS.ROLEREQUEST_CALLER_APPLY_DATE, tableName: TABLE_NAMES.ROLEREQUEST },
            { fieldName: ROLEREQUEST_FIELDS.ROLEREQUEST_IS_CALLER_APPLICANT, fieldAlias: ROLEREQUEST_FIELDS.ROLEREQUEST_IS_CALLER_APPLICANT, tableName: TABLE_NAMES.ROLEREQUEST },
            { fieldName: ROLEREQUEST_FIELDS.INFO, fieldAlias: ROLEREQUEST_FIELDS.INFO, tableName: TABLE_NAMES.ROLEREQUEST },
            { fieldName: ROLEREQUEST_FIELDS.PENDING_DEMAND, fieldAlias: ROLEREQUEST_FIELDS.PENDING_DEMAND, tableName: TABLE_NAMES.ROLEREQUEST }
        ];
    }

    return additionalFields;
};

const getLoadUserEntityAccessActions = (pageAlias, tableName, data) => {
    const actions = [loadUserEntityAccess(getEntityGuids(data, tableName), tableName)];

    if (pageAlias === ROLE_INBOX_PAGE_ALIAS) {
        actions.push(loadUserEntityAccess(getEntityLinkedGuids(data, tableName, TABLE_NAMES.RESOURCE), TABLE_NAMES.RESOURCE));
        actions.push(loadUserEntityAccess(getEntityLinkedGuids(data, tableName, TABLE_NAMES.JOB), TABLE_NAMES.JOB));
        actions.push(loadUserEntityAccess(getEntityLinkedGuids(data, tableName, TABLE_NAMES.ROLEREQUESTGROUP), TABLE_NAMES.ROLEREQUESTGROUP));
    }

    return actions;
};

const getLoadAvatarActions = (pageAlias, tableName, data = []) => {
    const actions = [];

    switch (pageAlias) {
        case ROLE_INBOX_PAGE_ALIAS: {
            const resourceGuids = data.map(entity => entity[`${tableName}_${TABLE_NAMES.RESOURCE}_guid`]);
            actions.push(loadAvatars(resourceGuids, AVATAR_SIZES.TINY));
            break;
        }
        case PEOPLE_FINDER_DIALOG_ALIAS: {
            const resourceGuids = data.map(entity => entity[`${tableName}_guid`]);
            actions.push(loadAvatars(resourceGuids, AVATAR_SIZES.TINY));
            break;
        }
        default:
            break;
    }

    return actions;
};

export function createAddDataGridFieldsEpic(
    alias,
    loadAdditionalFieldsActionHandler,
    getDataGridPageState = (state, alias) => state[alias]
) {
    return (action$, state$, { apis }) => {
        return action$.ofType(loadAdditionalFieldsActionHandler).pipe(
            switchMap(
                () => {
                    const state = state$.value;
                    const dataPage = getDataGridPageState(state, alias);
                    const { tableName, pagedData, uiOptions } = dataPage;
                    const { pageSize } = pagedData[tableName];
                    const pageNumber = uiOptions.pageNumber;

                    return of(dataGridLoadData(
                        alias,
                        pageSize,
                        pageNumber,
                        pageNumber
                    ));
                }
            )
        );
    };
}

export function createDataGridAddFieldsEpic(
    alias,
    tableDatasSuffix,
    getTableName,
    getCollectionAlias,
    loadAdditionalFieldsActionHandler,
    getLoadAdditionalColumnsAlias,
    columnsChangeSuccessAction,
    getDataKey,
    getFieldToLoad = (state, tableName, fieldName) => fieldName,
    getFieldOptions
) {
    return (action$, state$, { apis }) => {
        const loadAdditionaFields$ = action$.ofType(loadAdditionalFieldsActionHandler).pipe(
            mergeMap(
                (action) => {
                    const state = state$.value;
                    const dataPage = state[alias];
                    const { fieldOptions, pagedData, tableName: dataPageTableName } = dataPage;

                    const tableName = getTableName(state, action) || dataPageTableName;

                    //This is a problem as the getFieldOptions call can return fields that are not for the specific table - temporary fix in getFieldToLoad
                    const requestFieldOptions = getFieldOptions
                        ? getFieldOptions(action, state$)
                        : fieldOptions;

                    const fieldsToLoad = Object.keys(requestFieldOptions)
                        .filter(fieldKey => !requestFieldOptions[fieldKey].loaded && fieldKey !== `${tableName}_guid`)
                        .concat(`${tableName}_guid`)
                        .map(fieldName => getFieldToLoad(state, tableName, fieldName))
                        //Temp fix that will remove the null fields. That means that the field was not found for the table we are requesting.
                        .filter(item => null !== item);

                    let result = of(null);

                    const shouldLoadData = fieldsToLoad.length > 1;

                    if (shouldLoadData) {
                        let recordIds = [];
                        const dataKey = getDataKey(tableName);

                        if (action.payload && ('recordIds' in action.payload)) {
                            recordIds = action.payload.recordIds;
                        } else if (dataKey) {
                            const data = dataPage[dataKey][getCollectionAlias(state, action)];

                            recordIds = Object.keys(data.byId[tableName] || data.byId);
                        } else {
                            recordIds = Object.keys(pagedData[tableName].byId);
                        }

                        const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                        const fields = getSelectionFields(fieldsToLoad);
                        const selectionFields = [
                            ...fields,
                            ...getLinkedSelectionFields(tableName, fields, getFieldInfoWrapped),
                            ...getCustomLookupSelectionFields(tableName, fields, getFieldInfoWrapped)
                        ];

                        if (0 < recordIds.length)
                            result = getLoadFilterAccessData$(
                                tableName,
                                recordIds,
                                selectionFields,
                                apis
                            );
                        else
                            result = of([]);
                    }

                    return result;
                },
                (action, result) => {
                    return [result, action];
                }
            )
        );

        const additionalTableColumnsLoaded$ = loadAdditionaFields$.pipe(
            switchMap(
                ([pagedDataAdditionalFieldsRes, action]) => {

                    if (!pagedDataAdditionalFieldsRes || (pagedDataAdditionalFieldsRes.data && 0 === pagedDataAdditionalFieldsRes.data.length) || (0 === pagedDataAdditionalFieldsRes.length))
                        return empty();

                    const state = state$.value;
                    const dataPage = state[alias];
                    const { tableName: dataPageTableName } = dataPage;
                    const tableName = getTableName(state, action) || dataPageTableName;

                    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                    const linkedTableDatas = getLinkedTableData(pagedDataAdditionalFieldsRes.data, tableName, getFieldInfoWrapped);
                    const pagedData = getFlatTableData(pagedDataAdditionalFieldsRes.data, tableName, getFieldInfoWrapped);

                    let pagedDataProjection = pagedData.reduce((accumulator, record) => {
                        accumulator[record[`${tableName}_guid`]] = record;

                        return accumulator;
                    }, {});

                    const additionalColumnsAlias = getLoadAdditionalColumnsAlias(tableName);

                    const moreColumnsAddedAction = loadAdditionalColumnsSuccess(additionalColumnsAlias, getCollectionAlias(state, action), pagedDataProjection);
                    const linkedTableDatasLoadedActions = getTableDatasLoadedActions(`${alias}_${tableDatasSuffix}`, linkedTableDatas);
                    const colsUpdateSuccess = columnsChangeSuccessAction(alias, action);

                    return of(
                        batchActions([
                            moreColumnsAddedAction,
                            ...linkedTableDatasLoadedActions
                        ]),
                        colsUpdateSuccess
                    );
                }
            )
        );

        return additionalTableColumnsLoaded$;
    };
}

export function createDataGridChangePageEpic(
    alias,
    endpointName = API_KEYS.PAGING,
    getDataGridPageState = (state, alias) => state[alias],
    defaultAppliedFilters = [],
    shouldLoadEntityAccess = true
) {
    return (action$, state$, { apis }) => {
        const loadPagedDataPage$ = action$.ofType(`${actionTypes.DATA_GRID_PAGE_CHANGE}_${alias}`).pipe(
            mergeMap(
                ({ payload }) => {
                    const state = state$.value;
                    const dataPage = getDataGridPageState(state, alias);
                    const { tableName, pagedData } = dataPage;
                    const { key, loadedPages } = pagedData[tableName];

                    const { pageNumber, pageSize } = payload;
                    const pageLoaded = loadedPages.indexOf(pageNumber - 1) > -1;

                    let result = of([]);

                    if (!pageLoaded) {
                        const from = (pageNumber - 1) * pageSize;
                        const { tableName } = dataPage;

                        const pagedAccessSelection = getBuildDataGridSelection(alias, (fields) => fields, dataPage, state, defaultAppliedFilters);

                        result = apis[endpointName].getPagedResults$(tableName, key, from, pageSize, pagedAccessSelection);
                    }

                    return result;
                },
                (action, result) => [result, action]
            )
        );

        const pageLoadSuccess$ = loadPagedDataPage$.pipe(
            switchMap(
                completeChain(getDataGridPageEndChainErrorAction(state$))(
                    ([pagedDataRes, action]) => {
                        const state = state$.value;
                        const dataPage = getDataGridPageState(state, alias);
                        const { tableName, pagedData } = dataPage;
                        const { loadedPages } = pagedData[tableName];

                        const { pageNumber, pageSize } = action.payload;
                        const pageLoaded = loadedPages.indexOf(pageNumber - 1) > -1;

                        const maximumInfiniteScrollRows =
                            (alias === PLANNER_PAGE_ALIAS) ?
                                getLicenseValuesByKeySelector(state)(licensePlannerPageMaxInfiniteScrollRows).subscribedCount :
                                MAXIMUM_INFINITE_SCROLL_ROWS_AMOUNT;

                        let actions = [];

                        if (!pageLoaded) {
                            const from = (pageNumber - 1) * pageSize;
                            const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                            const linkedTableDatas = getLinkedTableData(pagedDataRes, tableName, getFieldInfoWrapped);
                            const mappedData = getMappedData(alias, pagedDataRes);
                            const shouldFlattenData = alias !== MARKETPLACE_PAGE_ALIAS;
                            const pagedData = shouldFlattenData ? getFlatTableData(pagedDataRes, tableName, getFieldInfoWrapped) : mappedData;

                            const tableDatasLoadedActions = getTableDatasLoadedActions(`${alias}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, linkedTableDatas);

                            actions.push(
                                loadPagedResultsDataSuccess(`${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`, tableName, from, pagedData, tableName, maximumInfiniteScrollRows),
                                batchActions([...tableDatasLoadedActions])
                            );

                            if (shouldLoadEntityAccess) {
                                actions.push(
                                    ...getLoadUserEntityAccessActions(alias, tableName, pagedDataRes),
                                    ...getLoadAvatarActions(alias, tableName, pagedDataRes)
                                );
                            }

                            if (alias === MARKETPLACE_PAGE_ALIAS) {
                                actions.push(getPopulateExpandedCriteriasAction(pagedDataRes, state, pagedDataRes.map(dataEntry => dataEntry.id)));
                                actions.push(loadCriteriaValuesForMultipleRoles(pagedDataRes.map(dataEntry => dataEntry.id)));
                            }

                        }

                        if (alias !== OPERATION_LOG_DIALOG_ALIAS) {
                            actions.push(pushUrl(buildPageParams(pageNumber, pageSize), alias));
                        }

                        actions.push(
                            dataGridPageChangeSuccess(alias, pageNumber)
                        );

                        return from(actions);
                    }
                )
            )
        );

        return pageLoadSuccess$;
    };
}

const getRelevantFilterSelection = (filterSelection, hasSubSelection = false) => {
    const selection = [];
    const subSelection = [];

    Object.keys(filterSelection).forEach(tableNameKey => {

        filterSelection[tableNameKey].forEach((filter, index) => {

            if (filter.field === FILTER_FIELD_NAMES.SKILL) {
                filter = getSkillFieldsSelection(filter);
            }

            if (filter.hasOwnProperty('filterValues')) { // check for multi value type filter
                const multiFilterSel = cloneDeep(filterSelection[tableNameKey][index]);
                filter = { ...omit(multiFilterSel, ['filterValues']) };
            }

            if (filter.hasOwnProperty('subFilter') && hasSubSelection) {
                subSelection.push({
                    ...filter.subFilter,
                    value: filter.value
                });
                filter = { ...omit(filter, ['subFilter']) };
            } else if (hasSubSelection) {
                subSelection.push(filter);
            }

            selection.push(filter);
        });
    });

    return {
        selection,
        subSelection
    };
};

const getMergedFilterSelection = (baseFilterSelection, filterSelection, hasSubSelection) => {
    const { selection: baseSelection, subSelection: baseSubSelectionLines } = getRelevantFilterSelection(baseFilterSelection, hasSubSelection);
    const { selection, subSelection } = getRelevantFilterSelection(filterSelection, hasSubSelection);

    return {
        selection: baseSelection.concat(selection),
        baseSubSelection: baseSubSelectionLines.concat(subSelection)
    };
};

const getHasSubSelection = (baseFilterSelection, filterSelection) => {
    let hasSubSelection = false;

    Object.keys(baseFilterSelection).forEach(key => {
        hasSubSelection = baseFilterSelection[key].some(f => f.hasOwnProperty('subFilter'));
    });

    !hasSubSelection && Object.keys(filterSelection).forEach(key => {
        hasSubSelection = filterSelection[key].some(f => f.hasOwnProperty('subFilter'));
    });

    return hasSubSelection;
};

const getDataPageFilterLines = (dataPage) => {
    const { tableName, filters } = dataPage;
    const { selection: filterSelection = {}, baseFilter = {} } = filters[tableName];
    const { selection: baseFilterSelection = {} } = baseFilter;
    const hasSubSelection = getHasSubSelection(baseFilterSelection, filterSelection);

    return getMergedFilterSelection(baseFilterSelection, filterSelection, hasSubSelection);
};

const buildFilterQuery = (selectionLines) => {
    return {
        filter: {
            filterGroupOperator: 'And',
            filterLines: selectionLines
        }
    };
};

const buildSubFiltersFilterQuery = (selectionLines, baseSubSelectionLines, mainTableName) => {
    const tables = [];
    const uniqueTableNames = [];

    baseSubSelectionLines = baseSubSelectionLines.map(element => {
        const { tableName } = element;
        if (tableName && tableName !== mainTableName) {
            element = {
                ...element,
                tableName: `${tableName}_subTable`
            };

            if (uniqueTableNames.indexOf(tableName) === -1) {
                uniqueTableNames.push(tableName);
            }
        }

        return element;
    });

    uniqueTableNames.forEach(tableName => {
        tables.push({
            name: `${tableName}`,
            alias: `${tableName}_subTable`,
            joinType: 'Outer',
            joinField: `${tableName}_${mainTableName}_guid`,
            joinToField: `${mainTableName}_guid`
        });
    });

    return {
        distinct: true,
        tables: tables,
        filter: {
            filterGroupOperator: 'Or',
            filterLines: [],
            subFilters: [
                {
                    filterGroupOperator: 'And',
                    filterLines: selectionLines
                },
                {
                    filterGroupOperator: 'And',
                    filterLines: baseSubSelectionLines
                }
            ]
        }
    };
};

const getFilterQuery = (dataPage, roleCalcImprovementsFeatureEnabled, defaultAppliedFilters = []) => {
    const { selection = [], baseSubSelection = [] } = getDataPageFilterLines(dataPage);
    const { tableName } = dataPage;

    const selectionLines = defaultAppliedFilters.length > 0
        ? [...selection, ...defaultAppliedFilters]
        : selection;

    if (roleCalcImprovementsFeatureEnabled) {
        if (tableName === TABLE_NAMES.ROLEREQUEST && selectionLines.length > 0) {
            const filteredSectionLines = selectionLines.filter(sectionLine => sectionLine.field !== FILTER_FIELD_NAMES.ROLE_STATUS);
            const roleRequestStatusGuid = selectionLines.find(sectionLine => sectionLine.field === FILTER_FIELD_NAMES.ROLE_STATUS) || null;

            if (roleRequestStatusGuid) {
                filteredSectionLines.push({
                    field: FILTER_FIELD_NAMES.ROLE_HASSTATUS,
                    operator: OPERATORS.DB_OPERATORS.EQUALS,
                    value: true,
                    parameters: {
                        statuses: roleRequestStatusGuid.value
                    }
                });
            }

            return buildFilterQuery(filteredSectionLines);
        }
    }

    const filteredSelection = selectionLines.reduce((accumulator, selectionObject) => {
        const { field, operator, value, tableName = '', parameters = {} } = selectionObject;

        const updatedSelectionObject = {
            field,
            operator,
            value,
            parameters,
            ...(tableName ? { tableName } : {})
        };

        accumulator.push(updatedSelectionObject);

        return accumulator;
    }, []);

    return baseSubSelection.length > 0
        ? buildSubFiltersFilterQuery(filteredSelection, baseSubSelection, tableName)
        : buildFilterQuery(filteredSelection);
};

const getJoinTablesQuery = (alias) => {
    return alias === MARKETPLACE_PAGE_ALIAS
        ? [{
            name: TABLE_NAMES.ROLEMARKETPLACE,
            joinType: JOIN_TYPES.INNER_JOIN,
            joinField: ROLEMARKETPLACE_FIELDS.ROLEREQUEST_GUID,
            joinToField: ROLEREQUEST_FIELDS.GUID
        }]
        : [];
};

const getMappedData = (alias, data) => {
    return alias === MARKETPLACE_PAGE_ALIAS
        ? data.map(dataEntry => dataEntry.body)
        : data;
};

export function createDataGridLoadPagedDataInterceptorEpic(alias) {
    return (action$, state$) => {
        const actionsOfInterest = [
            `${actionTypes.DATA_GRID_SORT_CHANGED}_${alias}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.CLEAR_ALL_FILTERS}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`
        ];

        return action$.ofType(...actionsOfInterest).pipe(
            switchMap(
                () => {
                    const state = state$.value;
                    const dataPage = state[alias];
                    const { tableName } = dataPage;

                    return of(
                        setPagedDataLoading(
                            `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                            tableName
                        )
                    );
                }
            )
        );
    };
}

const getBuildDataGridSelection = (alias, getCalcFieldParameters = (fields) => fields, dataPage, state, defaultAppliedFilters, maxRecords = DEFAULT_MAX_RECORDS, initialSelectionFields, roleCalcImprovementsFeatureEnabled) => {
    const { tableName, selection, displayFields } = dataPage;
    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    // const systemFieldInfos = getFieldInfos(tableStructure, tableName, getIsSystemField);
    // Switch with above line once System fields infos are updated on server
    const systemFieldInfos = tempGetTableSystemFields(tableName, initialSelectionFields);

    if (listPageAndBulkUpdateFeatureFlag) {
        // Exclude these system fields from /paged endpoint payload for resources view data grid.
        // This code will remain even after the fetaure flag is removed because including these systemFields in the payload for resource filter, returns an error.
        if (tableName === TABLE_NAMES.RESOURCE) {
            delete systemFieldInfos[RESOURCE_BOOKING_CONFLICT_DATES]
            delete systemFieldInfos[RESOURCE_BOOKING_POTENTIAL_CONFLICT_DATES]
        }
    }

    const systemFieldsSelection = getSelectionFields(Object.keys(systemFieldInfos || {}));

    const displayFieldsToLoad = displayFields.filter(field => {
        const fieldInfo = getFieldInfoWrapped(tableName, field);
        let loadField = false;

        if (fieldInfo != null && Object.keys(fieldInfo).length > 0 && !fieldInfo.excludeFromTableDisplaySettings) {
            loadField = true;
        }

        return loadField;
    });

    const displayFieldsSelection = getSelectionFields(displayFieldsToLoad);

    const mergedFields = getMergedSelectionFields(displayFieldsSelection, systemFieldsSelection);
    const fields = getCalcFieldParameters(mergedFields, getFieldInfoWrapped, tableName, state);
    const additionalFields = getAdditionalSelectionFields(alias, state);
    const additionalFieldsWithCalcParameters = getCalcFieldParameters(additionalFields, getFieldInfoWrapped, tableName, state);

    const selectionFields = [
        ...fields,
        ...getLinkedSelectionFields(tableName, fields, getFieldInfoWrapped),
        ...getCustomLookupSelectionFields(tableName, fields, getFieldInfoWrapped),
        ...additionalFieldsWithCalcParameters,
        ...getDoubleLinkedSelectionFields(tableName)
    ];

    let pagedAccessSelection = {
        ...selection,
        fields: selectionFields,
        order: getSelectionOrder(selection.order, tableName, getFieldInfoWrapped)
    };

    const filterQuery = getFilterQuery(dataPage, roleCalcImprovementsFeatureEnabled, defaultAppliedFilters);
    const joinTables = getJoinTablesQuery(alias);

    pagedAccessSelection = {
        ...pagedAccessSelection,
        ...filterQuery
    };

    if (joinTables.length) {
        pagedAccessSelection = {
            ...pagedAccessSelection,
            tables: joinTables
        };
    }

    if (maxRecords) {
        pagedAccessSelection = {
            ...pagedAccessSelection,
            maxRecords
        };
    }

    return pagedAccessSelection;
};

export function createDataGridLoadDataEpic(
    alias,
    getCalcFieldParameters = (fields) => fields,
    shouldFlattenData = true,
    endpointName = API_KEYS.PAGING,
    getMappedData = (data) => data,
    getAdditionalSuccessActions = () => [],
    defaultAppliedFilters = [],
    maxRecords = DEFAULT_MAX_RECORDS,
    tableSystemFields = TABLE_SYSTEM_FIELDS
) {
    return (action$, state$, { apis }) => {
        const actionsOfInterest = [
            `${actionTypes.DATA_GRID_LOAD_DATA}_${alias}`,
            `${actionTypes.DATA_GRID_SORT_CHANGED}_${alias}`,
            `${actionTypes.PAGED_DATA_PAGE_SIZE_CHANGED}_${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.EXTERNAL_FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.CLEAR_EXTERNAL_FILTER}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.CLEAR_ALL_FILTERS}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.BASE_FILTER_SET}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.RESET_BASE_FILTER}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.REFRESH_FILTERED_DATA}_${alias}`
        ];

        const loadPagedData$ = action$.ofType(...actionsOfInterest).pipe(
            switchMap(
                ({ payload }) => {
                    const state = state$.value;
                    const dataPage = state[alias];
                    const { tableName, pagedData } = dataPage;
                    const { pageSize } = pagedData[tableName];
                    const roleCalcImprovementsFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.ROLE_CALC_IMPROVEMENTS)(state$.value);

                    const pagedAccessSelection = getBuildDataGridSelection(alias, getCalcFieldParameters, dataPage, state, defaultAppliedFilters, maxRecords, tableSystemFields, roleCalcImprovementsFeatureEnabled);
                    const pageSizeParam = payload.pageSize ? payload.pageSize : pageSize;

                    return apis[endpointName].getPagedAccess$(pageSizeParam, pagedAccessSelection, tableName);
                },
                (action, result) => [result, action]
            )
        );

        const dataGridPageDataLoaded$ = loadPagedData$.pipe(
            switchMap(
                ([pagedDataRes, action]) => {
                    const { targetPage: dataGridPageNumber, pageSize: dataGridPageSize, redirectOnLoad = true } = action.payload;
                    const state = state$.value;
                    const dataPage = state[alias];
                    const { tableName } = dataPage;

                    const { data, rowCount } = pagedDataRes;
                    const mappedData = getMappedData(data);
                    const size = dataGridPageSize || dataPage.pagedData[tableName].pageSize;

                    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                    const linkedTableDatas = getLinkedTableData(mappedData, tableName, getFieldInfoWrapped);
                    const pagedData = shouldFlattenData ? getFlatTableData(mappedData, tableName, getFieldInfoWrapped) : mappedData;
                    const dataGridPageExisting = dataGridPageExists(dataGridPageNumber, rowCount, size);

                    const navigatedDataGridPageNumber = dataGridPageExisting ? dataGridPageNumber : DATA_GRID_INITIAL_PAGE_NUMBER;
                    const processLoading = pagedData.length == dataGridPageSize && dataGridPageExisting && dataGridPageNumber > 1;
                    const urlAction = getAppUrlAction(action.type, alias);
                    const tableDatasLoadedActions = getTableDatasLoadedActions(`${alias}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, linkedTableDatas);

                    const actions = [
                        ...getLoadUserEntityAccessActions(alias, tableName, mappedData),
                        ...getLoadAvatarActions(alias, tableName, mappedData),
                        loadPagedAccessDataSuccess(`${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`, tableName, { ...pagedDataRes, data: pagedData }, tableName, size, processLoading),
                        pagedDataRegisterKeepAlive(`${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`, tableName, pagedDataRes),
                        dataGridColumnsChangeSuccess(alias),
                        batchActions([...tableDatasLoadedActions]),
                        ...getAdditionalSuccessActions(state, data)
                    ];

                    if (processLoading) {
                        actions.push(dataGridPageChange(alias, dataGridPageNumber, size));
                    }

                    const isOperationLogDialogTableDataSort = action.type === `${actionTypes.DATA_GRID_SORT_CHANGED}_${OPERATION_LOG_DIALOG_ALIAS}`;

                    if (!isOperationLogDialogTableDataSort && redirectOnLoad) {
                        actions.push(urlAction(buildPageParams(navigatedDataGridPageNumber, size), alias));
                    }

                    return from(actions);
                }
            )
        );

        return dataGridPageDataLoaded$;
    };
}

export function createDataGridDetailsPaneCloseEpic(alias, detailsPaneAlias) {
    return (action$, state$) => {
        const actionsOfInterest = [
            `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.CLEAR_ALL_FILTERS}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
            `${actionTypes.DATA_GRID_SORT_CHANGED}_${alias}`,
            `${actionTypes.DATA_GRID_PAGE_CHANGE}_${alias}`,
            `${actionTypes.FILTERS_ACTIONS.BASE_FILTER_SET}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.RESET_BASE_FILTER}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.DATA_GRID_PAGE_CLOSE_DETAILS_PANE}_${alias}`,
            `${actionTypes.DELETE_TABLE_DATA}_${JOB_PAGE_PAGED_DATA}`,
            `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${TABLE_NAMES.JOB}`,
            `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${TABLE_NAMES.ROLEREQUEST}`
        ];

        return action$.ofType(...actionsOfInterest)
            .pipe(
                filter(({ payload = {} }) => payload.moduleName !== ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL),
                switchMap(() => {
                    let actions = [
                        setDetailsPaneCollapsed(detailsPaneAlias, true, detailsPaneAlias),
                        setDetailsPaneVisibility(false, detailsPaneAlias)
                    ];

                    const pageAlias = getCurrentPageAliasSelector(state$.value);

                    if (pageAlias === JOBS_PAGE_ALIAS) {
                        actions.push(
                            entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE)
                        );
                    } else if (pageAlias === ROLE_INBOX_PAGE_ALIAS) {
                        actions.push(
                            entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL),
                            entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL),
                            entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL)
                        );
                    } else if (pageAlias === RESOURCES_PAGE_ALIAS) {
                        actions.push(
                            entityWindowClose(ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL),
                            entityWindowClose(ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE),
                            entityWindowClose(ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL),
                            entityWindowClose(ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE),
                        );
                    }

                    return from(actions);
                })
            );
    };
}

export function createDataGridClearSelectionEpic(alias, tableName, actionsToOmit = []) {
    return (action$) => {
        const actionsOfInterest = [
            `${actionTypes.FILTERS_ACTIONS.FILTER_APPLY}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.FILTER_CLEAR}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.CLEAR_ALL_FILTERS}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.DATA_GRID_PAGE_CHANGE}_${alias}`,
            `${actionTypes.DATA_GRID_SORT_CHANGED}_${alias}`,
            `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_${tableName}`,
            `${actionTypes.FILTERS_ACTIONS.BASE_FILTER_SET}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.RESET_BASE_FILTER}_${alias}_${DATA_GRID_FILTERS_SUFFIX}`,
            `${actionTypes.FILTERS_ACTIONS.REFRESH_FILTERED_DATA}_${alias}`
        ];

        return action$.ofType(...actionsOfInterest)
            .pipe(
                filter((action) => actionsToOmit.indexOf(action.type) === -1),
                map(() => {
                    return selectEdits(
                        `${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`,
                        {
                            dataGuid: tableName,
                            editableGuids: []
                        }
                    );
                })
            );
    };
}

export const createJobsPagePagedTableDataBatchUpdateInterceptorEpic = (alias) => (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.BATCH_DIGEST_PATCH_PAGED_TABLE_DATA_SUCCESSFUL}_${alias}`,
        `${actionTypes.BATCH_CRUD_ERROR}_${CRUD_OPERATIONS.UPDATE}`
    ];

    const epicProps = {
        actionsOfInterest,
        successHandler: batchPatchPagedTableDataSuccess,
        requestOperation: CRUD_OPERATIONS.UPDATE
    };

    const commonGroupedDataBatchEpic = commonDataBatchInterceptorEpic(`${JOBS_PAGE_ALIAS}_${DATA_GRID_PAGED_DATA_SUFFIX}`, epicProps);

    return commonGroupedDataBatchEpic(action$, state$);
};


export const entityWindowContextualEditJobPageErrorInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    JOBS_PAGE_MODAL_ALIAS,
    actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
    [
        {
            type: `${actionTypes.ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_ERROR}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE}`,
            payload: {}
        }
    ]
)();

export const entityWindowClientDeleteJobPageErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
)();

export const entityWindowClientPatchJobPageErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
)();

export const entityWindowJobPatchJobPageErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    JOBS_PAGE_MODAL_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
)();

export const entityWindowJobDeleteJobPageErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    JOBS_PAGE_MODAL_ALIAS,
    actionTypes.ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL)
    ]
)();

export const jobsPageDeleteJobSuccessInterceptorEpic = (action$) => {
    return action$
        .ofType(`${actionTypes.DIGEST_DELETE_TABLE_DATA_SUCCESSFUL}_${JOB_PAGE_PAGED_DATA}`)
        .pipe(
            map(() => reloadJobsPageDataGridAction())
        );
};

export const jobsPagePatchUpdateJobSuccessInterceptorEpic = (action$) => {
    return action$
        .ofType(`${actionTypes.BATCH_PATCH_PAGED_TABLE_DATA_SUCCESSFUL}_${JOB_PAGE_PAGED_DATA}`)
        .pipe(
            map(() => reloadJobsPageDataGridAction())
        );
};