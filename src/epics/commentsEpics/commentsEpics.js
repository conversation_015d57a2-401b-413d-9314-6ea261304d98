import { switchMap, mergeMap, filter } from 'rxjs/operators';
import { combineEpics } from 'redux-observable';
import * as actionTypes from '../../actions/actionTypes';
import { of, concat, empty } from 'rxjs';
import { uniq } from 'lodash';
import { pagedDataRegisterKeepAlive, loadPagedAccessData, pagedDataUnregisterKeepAlive } from '../../actions/pagedDataActions';
import { addPagedDataModel, pagedDataModelsLoadedSuccess } from '../../actions/workspaceActions';
import { createAPICallEpic, getApiCallEpicConfig } from '../epicGenerators/apiCallEpicGenerator';
import { insertTableDataRequest$, patchTableDataRequest$, deleteTableDataRequest$ } from '../tableDataEpics';
import * as pagedDataEpics from '../pagedDataEpics';
import { digestReloadCommentsPagedData, updateComment, editCommentSuccess, reloadPagedCommentsData, updatePagedCommentsData, discardChanges } from '../../actions/commentsActions';
import { DEFAULT_COMMENTS_PAGE_SIZE } from '../../constants/commentsConsts';
import { getPagedCommentsGuid, getCommentsTableName, getPagedCommentsAlias } from '../../utils/commentsUtils';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { getAvatars, resourceAvatarLoaded } from '../../selectors/avatarSelectors';
import { loadAvatars } from '../../actions/avatarActions';
import { AVATAR_SIZES } from '../../constants/avatarConsts';
import { API_KEYS, ERROR_STATUS } from '../../constants/apiConsts';
import { getApplicationUserId } from '../../selectors/applicationUserSelectors';
import { entityWindowHasSection, getEntityWindowTableName, getEntityWindowAllComments, getEntityWindowPagedComments } from '../../selectors/entityWindowSelectors';
import { ENTITY_WINDOW_SECTION_TYPES } from '../../constants/entityWindowConsts';
import { getEntityPagedComments } from '../../selectors/commentsSelectors';

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_DETAILS_PANE,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    NOTIFICATION_PAGE_MODAL,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    JOBS_PAGE_BATCH_MODAL,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_BATCH_MODAL,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    MARKETPLACE_PAGE_MODAL,
    MARKETPLACE_DETAILS_PANE
} = ENTITY_WINDOW_MODULES;

const getRequestSelection = (commentsTableName, entityId, entityTableName) => {
    return {
        fields: [
            {
                fieldName: `${commentsTableName}_guid`
            },
            {
                fieldName: `${commentsTableName}_text`
            },
            {
                fieldName: `${commentsTableName}_${entityTableName}_guid`
            },
            {
                fieldName: `${commentsTableName}_createdby_resource_guid`
            },
            {
                fieldName: `${commentsTableName}_createdby_resource_guid.resource_description`
            },
            {
                fieldName: `${commentsTableName}_updatedon`
            },
            {
                fieldName: `${commentsTableName}_createdon`
            }
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: `${commentsTableName}_${entityTableName}_guid`,
                    operator: 'Contains',
                    value: [entityId]
                }
            ]
        },
        order: {
            orderFields: [
                {
                    order: 'Descending',
                    field: `${commentsTableName}_createdon`
                }
            ]
        }
    };
};

const loadPagedCommentsEpic = (action$ /*state$, { apis }*/) => {

    return action$
        .ofType(actionTypes.COMMENTS.LOAD_PAGED)
        .pipe(
            switchMap(({ payload }) => {
                const { moduleName, entityTableName, entityId, pageSize = DEFAULT_COMMENTS_PAGE_SIZE } = payload;
                const commentsTableName = getCommentsTableName(entityTableName);
                const pagedAccessSelection = getRequestSelection(commentsTableName, entityId, entityTableName);

                const pagedDataGuid = getPagedCommentsGuid(entityTableName, entityId);
                const pagedDataAlias = getPagedCommentsAlias(moduleName, entityTableName);

                let actionsChain = [
                    of(addPagedDataModel(pagedDataAlias, pagedDataGuid, commentsTableName, pageSize)),
                    of(pagedDataModelsLoadedSuccess(pagedDataAlias)),
                    of(loadPagedAccessData(pagedDataAlias, commentsTableName, pagedAccessSelection, pagedDataGuid, pageSize))
                ];

                return concat(...actionsChain);
            })
        );
};

const digestLoadCommentsAvatarsEpic = (alias) => {
    return (action$, state$, { apis }) => {
        const actionsOfInterest = [
            `${actionTypes.LOAD_PAGED_ACCESS_DATA_SUCCESSFUL}_${alias}`,
            `${actionTypes.LOAD_PAGED_RESULTS_DATA_SUCCESSFUL}_${alias}`
        ];

        return action$
            .ofType(...actionsOfInterest)
            .pipe(
                mergeMap(
                    ({ payload }) => {
                        const { tableName, result } = payload;
                        const commentsData = result.data || result;
                        const avatars = getAvatars(state$.value);

                        const loadedCommentsAuthorsIds = commentsData.map(comment => comment[`${tableName}_createdby_resource_guid`]);
                        const resourceIds = uniq([
                            ...loadedCommentsAuthorsIds,
                            getApplicationUserId(state$.value)
                        ]);

                        const avatarRequestsIDs = resourceIds
                            .filter(id => resourceAvatarLoaded(avatars, id, AVATAR_SIZES.TINY.label));

                        return of(loadAvatars(avatarRequestsIDs, AVATAR_SIZES.TINY));
                    }
                )
            );
    };
};

const registerPagedCommentsKeepAliveEpic = (alias) => {
    return (action$ /* state$, { apis } */) => {

        return action$
            .ofType(`${actionTypes.LOAD_PAGED_ACCESS_DATA_SUCCESSFUL}_${alias}`)
            .pipe(
                switchMap(({ payload }) => {
                    const { tableName: commentsTableName, result } = payload;

                    return of(pagedDataRegisterKeepAlive(alias, commentsTableName, result));
                })
            );
    };
};

const getModulesWithPagedCommentsGuid = (comments, pagedCommentsGuid) => {
    const modules = [];

    Object.keys(comments).map(module => {
        const modulePagedCommentsGuids = Object.keys(getEntityWindowPagedComments(comments, module));

        if (modulePagedCommentsGuids.includes(pagedCommentsGuid)) {
            modules.push(module);
        }
    });

    return modules;
};

const getHighestCommentsLoadedCount = (comments, modules, pagedDataGuidToReload) => {
    let result = 0;

    modules.map(module => {
        const modulePagedComments = getEntityWindowPagedComments(comments, module);
        const { loadedPages, pageSize } = getEntityPagedComments(modulePagedComments, pagedDataGuidToReload);
        const hasDataLoaded = loadedPages.length > 0;
        const uiLastPageNumber = hasDataLoaded ? loadedPages.length : 1;
        const uiMaxDataRowsCount = uiLastPageNumber * pageSize;

        result = Math.max(result, uiMaxDataRowsCount);
    });

    return result;
};

const digestReloadPagedCommentsEpic = (action$, state$ /*{ apis }*/) => {
    //on CREATE_SUCCESS, DELETE_SUCCESS
    return action$
        .ofType(actionTypes.COMMENTS.DIGEST_RELOAD)
        .pipe(
            switchMap(({ payload }) => {
                const { entityTableName, entityId } = payload;
                const pagedDataGuidToReload = getPagedCommentsGuid(entityTableName, entityId);
                const commentsState = getEntityWindowAllComments(state$.value);

                const modulesToUpdate = getModulesWithPagedCommentsGuid(commentsState, pagedDataGuidToReload);
                const highestCommentsLoadedCount = getHighestCommentsLoadedCount(commentsState, modulesToUpdate, pagedDataGuidToReload);

                return of(reloadPagedCommentsData(
                    getCommentsTableName(entityTableName),
                    pagedDataGuidToReload,
                    modulesToUpdate,
                    entityId,
                    entityTableName,
                    highestCommentsLoadedCount
                ));
            })
        );
};

const updatePagedCommentsDataEpic = (action$, state$, { apis }) => {

    const loadNewPagedData$ = action$
        .ofType(actionTypes.COMMENTS.RELOAD_PAGED)
        .pipe(
            mergeMap(
                ({ payload }) => {
                    const { tableName, pageSize, entityId, entityTableName } = payload;
                    const pagedAccessSelection = getRequestSelection(tableName, entityId, entityTableName);

                    return apis[API_KEYS.PAGING].getPagedAccess$(pageSize, pagedAccessSelection, tableName);
                },
                (action, result) => [result, action]
            )
        );

    return loadNewPagedData$.pipe(
        filter(([response]) => response.status !== ERROR_STATUS),
        mergeMap(([response, action]) => {
            const { pagedDataGuid, modulesToUpdate } = action.payload;
            const { data = [], rowCount, key } = response;

            const actionsChain = [];

            modulesToUpdate.map(moduleName => {
                actionsChain.push(
                    of(updatePagedCommentsData(getPagedCommentsAlias(moduleName), pagedDataGuid, data, rowCount, key))
                );
            });

            return actionsChain.length > 0 ? concat(...actionsChain) : empty();
        })
    );
};

const updateCommentEpic = (action$, state$, { apis }) => {

    const loadComment$ = action$
        .ofType(actionTypes.COMMENTS.EDIT_SUCCESS)
        .pipe(
            switchMap(({ payload }) => {
                const { entityTableName, commentId } = payload;
                const commentsTableName = getCommentsTableName(entityTableName);

                return apis[API_KEYS.TABLE_DATA].getTableDataRow$(commentsTableName, commentId);
            },
            (action, result) => [result, action])
        );

    const updateComment$ = loadComment$.pipe(
        filter(([response]) => response.status !== ERROR_STATUS),
        switchMap(([commentData, action]) => {
            const { entityId, entityTableName, commentId } = action.payload;
            const commentsTableName = getCommentsTableName(entityTableName);
            const targetPagedDataGuid = getPagedCommentsGuid(entityTableName, entityId);
            const commentsState = getEntityWindowAllComments(state$.value);
            const modulesToUpdate = getModulesWithPagedCommentsGuid(commentsState, targetPagedDataGuid);
            const actionsChain = [];

            modulesToUpdate.map(module => {
                actionsChain.push(
                    of(updateComment(getPagedCommentsAlias(module), commentId, targetPagedDataGuid, commentData, commentsTableName))
                );
            });

            return actionsChain.length > 0 ? concat(...actionsChain) : empty();
        })
    );

    return updateComment$;
};

const disposeCommentsEpic = (moduleName) => {
    const alias = '';

    return (action$, state$ /*{ apis } */) => {
        return action$
            .ofType(
                `${actionTypes.ENTITY_WINDOW.CLOSE}_${moduleName}`,
                `${actionTypes.SET_DETAILS_PANE_SELECTED_TAB}_${alias}`
            )
            .pipe(
                filter(() => {
                    const { value: { entityWindow } } = state$;
                    const tableName = getEntityWindowTableName(entityWindow, moduleName);

                    if (tableName === undefined) {
                        return false;
                    }

                    return entityWindowHasSection(state$.value, tableName, ENTITY_WINDOW_SECTION_TYPES.COMMENTS_SECTION_TYPE, moduleName);
                }),
                switchMap(() => {
                    const commentsAlias = getPagedCommentsAlias(moduleName);

                    return of(
                        discardChanges(commentsAlias),
                        pagedDataUnregisterKeepAlive(commentsAlias)
                    );
                })
            );
    };
};

const insertCommentEpic = createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.COMMENTS.CREATE,
        API_KEYS.TABLE_DATA,
        insertTableDataRequest$,
        digestReloadCommentsPagedData
        // errorActionHandler
    )
)();

const patchCommentEpic = createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.COMMENTS.EDIT_SUBMIT,
        API_KEYS.TABLE_DATA,
        patchTableDataRequest$,
        editCommentSuccess
        // errorActionHandler
    )
)();

const deleteCommentEpic = createAPICallEpic(
    null,
    getApiCallEpicConfig(
        actionTypes.COMMENTS.DELETE,
        API_KEYS.TABLE_DATA,
        deleteTableDataRequest$,
        digestReloadCommentsPagedData
        //errorActionHandler
    )
)();

const plannerModalCommentsAlias = getPagedCommentsAlias(PLANNER_PAGE_MODAL);
const plannerBatchModalCommentsAlias = getPagedCommentsAlias(PLANNER_PAGE_BATCH_MODAL);
const plannerDetailsPaneCommentsAlias = getPagedCommentsAlias(PLANNER_PAGE_DETAILS_PANE);
const roleInboxModalCommentsAlias = getPagedCommentsAlias(ROLE_INBOX_PAGE_MODAL);
const roleInboxDetailsPaneCommentsAlias = getPagedCommentsAlias(ROLE_INBOX_PAGE_DETAILS_PANE);
const roleInboxBatchModalCommentsAlias = getPagedCommentsAlias(ROLE_INBOX_PAGE_BATCH_MODAL);
const roleInboxBatchDetailsPaneCommentsAlias = getPagedCommentsAlias(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE);
const jobsModalCommentsAlias = getPagedCommentsAlias(JOBS_PAGE_MODAL);
const jobsDetailsPaneCommentsAlias = getPagedCommentsAlias(JOBS_PAGE_DETAILS_PANE);
const jobsBatchModalCommentsAlias = getPagedCommentsAlias(JOBS_PAGE_BATCH_MODAL);
const jobsBatchDetailsPaneCommentsAlias = getPagedCommentsAlias(JOBS_PAGE_BATCHED_DETAILS_PANE);
const resourcesModalCommentsAlias = getPagedCommentsAlias(RESOURCES_PAGE_MODAL);
const resourcesDetailsPaneCommentsAlias = getPagedCommentsAlias(RESOURCES_PAGE_DETAILS_PANE);
const resourcesBatchModalCommentsAlias = getPagedCommentsAlias(RESOURCES_PAGE_BATCH_MODAL);
const resourcesBatchDetailsPaneCommentsAlias = getPagedCommentsAlias(RESOURCES_PAGE_BATCHED_DETAILS_PANE);
const rolesBoardModalCommentsAlias = getPagedCommentsAlias(MARKETPLACE_PAGE_MODAL);
const rolesBoardDetailsPaneCommentsAlias = getPagedCommentsAlias(MARKETPLACE_DETAILS_PANE);
const plannerBatchDetialsPaneCommentsAlias = getPagedCommentsAlias(PLANNER_PAGE_BATCH_DETAILS_PANE);
const notificationsModalCommentsAlias = getPagedCommentsAlias(NOTIFICATION_PAGE_MODAL);

export default combineEpics(
    loadPagedCommentsEpic,
    insertCommentEpic,
    patchCommentEpic,
    updateCommentEpic,
    digestReloadPagedCommentsEpic,
    updatePagedCommentsDataEpic,
    deleteCommentEpic,
    pagedDataEpics.createLoadPagedAccessDataEpic(plannerModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(notificationsModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(plannerBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(plannerDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(plannerBatchDetialsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(roleInboxModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(roleInboxDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(roleInboxBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(roleInboxBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(jobsModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(jobsDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(jobsBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(jobsBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(resourcesModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(resourcesDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(resourcesBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(resourcesBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(rolesBoardModalCommentsAlias),
    pagedDataEpics.createLoadPagedAccessDataEpic(rolesBoardDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(plannerModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(notificationsModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(plannerBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(plannerDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(plannerBatchDetialsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(roleInboxModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(roleInboxDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(roleInboxBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(roleInboxBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(jobsModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(jobsDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(jobsBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(jobsBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(resourcesModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(resourcesDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(resourcesBatchModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(resourcesBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(rolesBoardModalCommentsAlias),
    pagedDataEpics.createLoadPagedResultsDataEpic(rolesBoardDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(plannerModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(notificationsModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(plannerBatchModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(plannerDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(plannerBatchDetialsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(roleInboxModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(roleInboxDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(roleInboxBatchModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(roleInboxBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(jobsModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(jobsDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(jobsBatchModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(jobsBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(resourcesModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(resourcesDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(resourcesBatchModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(resourcesBatchDetailsPaneCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(rolesBoardModalCommentsAlias),
    pagedDataEpics.createPagedDataKeepAliveEpic(rolesBoardDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(plannerModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(notificationsModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(plannerBatchModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(plannerDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(roleInboxModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(roleInboxDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(roleInboxBatchModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(roleInboxBatchDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(jobsModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(jobsDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(jobsBatchModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(jobsBatchDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(resourcesModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(resourcesDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(resourcesBatchModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(resourcesBatchDetailsPaneCommentsAlias),
    registerPagedCommentsKeepAliveEpic(rolesBoardModalCommentsAlias),
    registerPagedCommentsKeepAliveEpic(rolesBoardDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(plannerModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(notificationsModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(plannerBatchModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(plannerDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(plannerBatchDetialsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(roleInboxModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(roleInboxDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(roleInboxBatchModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(roleInboxBatchDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(jobsModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(jobsDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(jobsBatchModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(jobsBatchDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(resourcesModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(resourcesDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(resourcesBatchModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(resourcesBatchDetailsPaneCommentsAlias),
    digestLoadCommentsAvatarsEpic(rolesBoardModalCommentsAlias),
    digestLoadCommentsAvatarsEpic(rolesBoardDetailsPaneCommentsAlias),
    disposeCommentsEpic(PLANNER_PAGE_MODAL),
    disposeCommentsEpic(NOTIFICATION_PAGE_MODAL),
    disposeCommentsEpic(PLANNER_PAGE_BATCH_MODAL),
    disposeCommentsEpic(PLANNER_PAGE_DETAILS_PANE),
    disposeCommentsEpic(PLANNER_PAGE_BATCH_DETAILS_PANE),
    disposeCommentsEpic(ROLE_INBOX_PAGE_MODAL),
    disposeCommentsEpic(ROLE_INBOX_PAGE_DETAILS_PANE),
    disposeCommentsEpic(ROLE_INBOX_PAGE_BATCH_MODAL),
    disposeCommentsEpic(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
    disposeCommentsEpic(JOBS_PAGE_MODAL),
    disposeCommentsEpic(JOBS_PAGE_DETAILS_PANE),
    disposeCommentsEpic(JOBS_PAGE_BATCH_MODAL),
    disposeCommentsEpic(JOBS_PAGE_BATCHED_DETAILS_PANE),
    disposeCommentsEpic(RESOURCES_PAGE_MODAL),
    disposeCommentsEpic(RESOURCES_PAGE_DETAILS_PANE),
    disposeCommentsEpic(RESOURCES_PAGE_BATCH_MODAL),
    disposeCommentsEpic(RESOURCES_PAGE_BATCHED_DETAILS_PANE),
    disposeCommentsEpic(MARKETPLACE_PAGE_MODAL),
    disposeCommentsEpic(MARKETPLACE_DETAILS_PANE)
);