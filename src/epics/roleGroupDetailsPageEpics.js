import _ from "lodash";
import { combineEpics } from "redux-observable";
import { empty, concat, of, from } from "rxjs";
import { map, mergeMap, switchMap, filter } from "rxjs/operators";
import { LOAD_USER_ENTITY_ACCESS_SUCCESS, ROLE_GROUP_DETAILS_PAGE_ACTIONS, ENTITY_WINDOW, REFRESH_ROLE_GROUP, PAGE_ACTIONS, ASSIGN_RESOURCE_TO_CRITERIA, LOAD_WORKFLOW_ENTITY_ACCESS_SUCCESS, REDIRECT_TO_ROLEGROUP_DETAILS_PAGE, DELETE_ROLE_GROUP, DELETE_ROLE_GROUP_REQUEST } from "../actions/actionTypes";
import { addNewEntityRequest, entityWindowOpenForMultiple, entityWindowUpdateMultipleEntities, entityWindowClose, entityWindowOpen, changeReadOnlyFieldsVisibility, setBatchWindowActiveEntity, entityWindowUpdateMessages, discardBatchEntityChangesAction } from "../actions/entityWindowActions";
import { resetPageParams, updatePageParams } from "../actions/pageStateActions";
import { promptAction } from "../actions/promptActions";
import { loadRoleGroupDetails, updateFieldValue, cleanRoleGroupDetails, updateEntityField, deleteRoleGroup, updateRoleGroups } from "../actions/roleGroupDetailsActions";
import { deleteTableDataError, loadMoreTableDataSuccess } from "../actions/tableDataActions";
import { ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS, ROLE_REQUEST_FORM, TABLE_NAMES } from "../constants";
import { ERROR_STATUS } from "../constants/apiConsts";
import { EMPTY_STATE, ENTITY_WINDOW_OPERATIONS, ENTITY_WINDOW_MODULES, ENTITY_WINDOW_SECTION_KEYS } from "../constants/entityWindowConsts";
import { ROLEREQUESTGROUP_FIELDS, ROLEREQUEST_FIELDS } from "../constants/fieldConsts";
import { CRITERIA_SECTIONS, ROLE_ENTITY_TYPES, ROLE_ITEM_STATUS_KEYS } from "../constants/rolesConsts";
import { getRoleGroupSurrogateId } from "../selectors/dataGridPageSelectors";
import { getRoleRequestGroupGuidSelector, getRoleRequestGroupJobGuid, getRoleRequestStatusGuidSelector } from "../selectors/roleGroupDetailsPageSelectors";
import { getIsJobDpItemVisibleCollapse } from "../selectors/jobsPageRoleGroupSelectors";
import { getFieldInfoSelector } from "../selectors/tableStructureSelectors";
import {getActiveRoleRequestEntitySelector} from '../selectors/roleGroupDetailsPageSelectors';
import {createEntityWindowTableDataChangeInterceptorEpic} from './epicGenerators/entityWindowInterceptors'
import {patchPagedDataSuccess} from '../actions/pagedDataActions';
import {JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE_DP_TABLE_DATA, ROLE_GROUP_DETAILS_PAGE_TAB_KEYS, JOBS_PAGE_DP_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_PAGE_TABLE_DATA_ALIAS} from '../constants/jobsPageConsts';
import { DATA_GRID_TABLE_DATAS_SUFFIX} from '../constants/dataGridConsts';
import {setDetailsPaneSelectedTab, setDetailsPaneVisibility} from '../actions/detailsPaneActions';
import { createAPICallEpic, getApiCallEpicConfig } from "./epicGenerators/apiCallEpicGenerator";
import { deleteTableDataRequest$ } from "./tableDataEpics";
import { validateCriteriaRoleBudgetSection, validateMultipleEntities } from "../actions/entityWindowDrivenActions";
import { getPageState } from '../selectors/pagesSelectors';
import { createRoleGroupSuccess } from "../actions/roleGroupListActions";
import { getApplicationAccessSelector, getRoleTypeSelector } from '../selectors/userEntityAccessSelectors';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { getPopulateNewRoleCriteriaAction } from "../actions/criteriaActions";
import { getNewEntityId, getNewEntityFields } from "../utils/entityStructureUtils";
import {
    getActiveEntityID,
    getCurrentDetailsPaneForPage,
    createGetActiveWindowSelector,
    getEntityWindowOperation,
    getEntityWindowEntity,
    getEntityWindowTableName,
    getRolerequestgroupSurrogateIdSelector
} from "../selectors/entityWindowSelectors";
import { getCurrentPageAliasSelector } from "../selectors/navigationSelectors";
import { getTableDatasAlias } from "../utils/tableStructureUtils";
import { getSingleSuggestedResourceDataSelector } from "../selectors/suggestedResourcesSelectors";
import { getRolegroupDetailsDpTabToSelect } from "../utils/rolegroupDetailsPageUtils";
import { getPageRoleRequestStatusDescriptionSelector } from "../selectors/roleRequestStatusSelectors";
import { getIsMultipleAssigneesEnabled } from "../selectors/functionalityConfigurationSelectors";
import { loadUserEntityAccess } from "../actions/userEntityAccessActions";
import { createMovePendingTimeAllocationEpic, createRemovePendingTimeAllocationEpic, rolerequestMoveToEpic$ } from "./rolerequestEpics";
import { managePendingTimeAllocationErrorAction } from "../actions/rolerequestActions";
import { getIsCriteriaRole } from "../utils/roleRequestsUtils";
import { roleRequestResourceField } from "../state/entityWindow/fieldsConfig";
import { getHasHiddenRequirementsSelector } from "../selectors/requirementsSelectors";
import { getCriteriaRoleAssignedResourcesSelector, getIsRoleSuggestibleSelector } from "../selectors/roleRequestsSelector";
import { getEntityHasWorkflowAccessSelector } from "../selectors/workflowEntityAccessSelectors";
import { ROLEREQUEST_WORKFLOW_ACCESS_TYPES } from "../constants/tablesConsts";
import { NULL_GUID, PLANNER_PAGE_ALIAS } from "../constants/plannerConsts";
import { getEntityWindowMessagesSelector } from "../selectors/entityWindowMessagesSelectors";
import { navigateToRoleGroupDetailsPage } from "./roleGroupDuplicateEpics";
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_DP_ALIAS } from "../constants/roleInboxPageConsts";
import { reloadRoleInboxPageDataGridAction } from "../actions/dataGridActions";
import { reloadPlannerData } from "../actions/plannerDataActions";
import { replaceUrl, PAGE_ACTIONS as PAGE_NAVIGATE_ACTIONS } from "../actions/navigateActions";
import { ROLEGROUPLISTDETAILSPAGE } from "../pages/pages";
import { FEATURE_FLAGS, PLANNER_TABLE_DATA_NAME, TABLE_DATA_NAME } from "../constants/globalConsts";
import { getFeatureFlagSelector } from "../selectors/featureManagementSelectors";

const tableDataApiKey = 'tableData';

export const doGetRolegroupDataApiCall$ = (apis, tableName, selection) => {
    return apis[tableDataApiKey].getTableData$(tableName, selection);
};

const jobsTableNameAlias = `${JOBS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;

export const getSelectionConfig = (rolegroupSurrogateId) => {
    const additionalRoleGroupFields = [ROLEREQUESTGROUP_FIELDS.ROLES_START,ROLEREQUESTGROUP_FIELDS.ROLES_END, ROLEREQUESTGROUP_FIELDS.TOTALACTIONABLEREQUESTS, ROLEREQUESTGROUP_FIELDS.TOTALROLES];

    return {
        fields: [
            {
                fieldName: 'rolerequestgroup_guid',
                fieldAlias: 'rolerequestgroup_guid'
            },
            {
                fieldName: 'rolerequestgroup_description',
                fieldAlias: 'rolerequestgroup_description'
            },
            {
                fieldName: 'rolerequestgroup_job_guid.job_description',
                fieldAlias: 'job_description'

            },
            {
                fieldName: 'rolerequestgroup_job_guid.job_surrogate_id',
                fieldAlias: 'job_surrogate_id'
            },
            {
                fieldName: 'rolerequestgroup_surrogate_id',
                fieldAlias: 'rolerequestgroup_surrogate_id'
            },
            {
                fieldName: 'rolerequestgroup_job_guid',
                fieldAlias: 'rolerequestgroup_job_guid'
            },
            ...additionalRoleGroupFields.map(fieldName => ({fieldName}))
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: 'rolerequestgroup_surrogate_id',
                    operator: 'Equals',
                    value: parseInt(rolegroupSurrogateId)
                }
            ]
        }
    };
};

const deleteRoleGroupEpic$ = (action$, state$, { apis }) => {
    return action$.ofType(DELETE_ROLE_GROUP_REQUEST)
        .pipe(
            switchMap((action) => {
                const { payload } = action;
                const state = state$.value;
                const { entityId } = payload;
                const pageAlias = getCurrentPageAliasSelector(state);
                const rolerequestGroupGuid = pageAlias === ROLE_GROUP_DETAILS_PAGE
                    ? getRoleRequestGroupGuidSelector(state)
                    : entityId;

                return of(promptAction(deleteRoleGroup(rolerequestGroupGuid, pageAlias), pageAlias));
            })
        );
};

const roleDeleteDataHandler = (alias, payload, state) => {
    return {
        jobSurrogateId: (((state.rolegroupDetailsPage || {}).pageState || {}).params || {}).jobSurrogateId,
        jobName: (((state.rolegroupDetailsPage || {}).pageState || {}).params || {}).jobName
    }
}

const roleGroupDeletesuccessHandler = (alias, payload, processedData) => {
    const { pageAlias } = payload;

    return roleGroupDeleteSuccessHandlerMap[pageAlias](processedData);
};

const roleGroupDeleteSuccessHandlerMap = {
    [ROLE_INBOX_PAGE_ALIAS]: () => [setDetailsPaneVisibility(false, ROLE_INBOX_PAGE_DP_ALIAS), reloadRoleInboxPageDataGridAction()],
    [PLANNER_PAGE_ALIAS]: () => [reloadPlannerData()],
    [ROLE_GROUP_DETAILS_PAGE]: (processedData) => {
        const metaData = {
            surrogateId: processedData.jobSurrogateId,
            jobDescription: processedData.jobName,
            pageAlias: ROLE_GROUP_DETAILS_PAGE
        };

        return [createRoleGroupSuccess(ROLE_GROUP_PAGE_TABLE_DATA_ALIAS, metaData)];
    }
};

export const createDeleteRoleGroupEpic = (alias, successActionHandler = roleGroupDeletesuccessHandler, errorActionHandler = deleteTableDataError) => createAPICallEpic(
    null,
    getApiCallEpicConfig(
        DELETE_ROLE_GROUP,
        'tableData',
        deleteTableDataRequest$,
        successActionHandler,
        errorActionHandler,
        undefined,
        undefined,
        roleDeleteDataHandler
    )
);

export const refreshDPOnFieldChange$ = (action$, state$, {apis}) => {
    return action$
        .ofType(ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.REFRESH_RESOURCE_DP,
            `${ENTITY_WINDOW.SET_ACTIVE_ENTITY}_${[ROLE_REQUEST_FORM]}`,
            `${ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_OVERLAPPING_BOOKING}_${ROLE_REQUEST_FORM}`,
            `${ENTITY_WINDOW.ADD_NEW_ENTITY}_${[ROLE_REQUEST_FORM]}`).pipe(
            switchMap(() => {
                let resourceId = null;
                let chain = [];
                const activeEntity = getActiveRoleRequestEntitySelector(state$.value);
                const {rolerequest_resource_guid = {}} = activeEntity;
                resourceId = rolerequest_resource_guid.value;
                chain = getRoleGroupDetailsPageRefreshDPActions(resourceId, state$.value);

                return concat(...chain);
            })
        );
};

const entityWindowSubmitUpdateResourceInterceptor = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.RESOURCE,
    ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL),
        patchPagedDataSuccess(ROLE_GROUP_DETAILS_PAGE_DP_TABLE_DATA, {tableDataGuid: TABLE_NAMES.RESOURCE}, true)
    ]
);

const entityWindowResourceDPUpdateInlineDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.RESOURCE,
    ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
    [
        patchPagedDataSuccess(ROLE_GROUP_DETAILS_PAGE_DP_TABLE_DATA, {tableDataGuid: TABLE_NAMES.RESOURCE}, true),
        {
            type: `${ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE}`,
            payload: {
                moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
            }
        }
    ]
);

const entityWindowResourceDPUpdateInlineDetailsPaneErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.RESOURCE,
    ENTITY_WINDOW.CONTEXTUAL_EDIT_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
    [
        {
            type: `${ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_ERROR}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE}`,
            payload: {}
        }
    ]
);

const entityWindowRoleGroupDPUpdateInlineDetailsPaneErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUESTGROUP,
    ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    [
        {
            type: `${ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_ERROR}_${ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE}`,
            payload: {}
        }
    ]
);

const entityWindowRoleGroupMoveToInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT),
        updateRoleGroups(ROLE_REQUEST_FORM)
    ]
);

export const entityWindowRoleGroupMoveErrorToInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT)
    ]
);

const entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUESTGROUP,
    ENTITY_WINDOW.CONTEXTUAL_EDIT_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    [
        patchPagedDataSuccess(ROLE_GROUP_DETAILS_PAGE_DP_TABLE_DATA, { tableDataGuid: TABLE_NAMES.ROLEREQUESTGROUP }, true),
        {
            type: `${ENTITY_WINDOW.CONTEXTUAL_EDIT_APPLY_SUCCESS}_${ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE}`,
            payload: {
                moduleName: ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE
            }
        }
    ]
);

export const setRoleOnLoadRoleGroupDetailsPage$ = (action$, state$, {apis}) => {
    return action$
        .ofType(PAGE_ACTIONS.OPEN.rolegroupDetailsPage)
        .pipe(
            switchMap(() => {
                let chain = [];
                const state = state$.value;
                const { windows = {} } = state.entityWindow.window[ROLE_REQUEST_FORM];
                const roleRequests = Object.keys(windows) || [];
                const { roleRequestId = '' } = getPageState(state, ROLE_GROUP_DETAILS_PAGE);
                if (roleRequestId != '' && roleRequests.includes(roleRequestId)) {
                    const indexValue = roleRequests.indexOf(roleRequestId);
                    const index = (indexValue > -1) ? indexValue : 0;
                    chain.push(setBatchWindowActiveEntity(ROLE_REQUEST_FORM, index, TABLE_NAMES.ROLEREQUEST));
                }

                return chain;
            })
        );
};

export const loadRoleGroupDetailsPageDP$ = (alias, pageTableNameAlias) => {
    return (action$, state$, {apis}) => {
        const {OPEN_FOR_MULTIPLE /*, EDIT */} = ENTITY_WINDOW;

        return action$
            .ofType(
                `${OPEN_FOR_MULTIPLE}_${alias}`
            ).pipe(
                filter(({payload: {moduleName}}) => moduleName === ROLE_REQUEST_FORM),
                switchMap(({payload: {entityIds, activeEntity, entities}}) => {
                    let chain = [];
                    let resourceId = null;
                    if (entityIds.length) {
                        const filteredEntity = entities.find(entity => entity.entityId === activeEntity);
                        resourceId = filteredEntity && filteredEntity.entity.rolerequest_resource_guid;
                    }
                    chain = getRoleGroupDetailsPageRefreshDPActions(resourceId, state$.value);

                    return concat(...chain);
                })
            );
    };
};

export const getRoleGroupDetailsPageRefreshDPActions = (resourceId = null, state) => {
    let chain = [];
    const shouldJobDPItemClose = getIsJobDpItemVisibleCollapse(state.jobsPage, JOBS_PAGE_DP_ALIAS);
    const shouldRoleGroupDPItemClose = getIsJobDpItemVisibleCollapse(state.rolegroupListPage, ROLE_GROUP_LIST_PAGE);
    const { tabs = {}, selectedTabKey } = getCurrentDetailsPaneForPage(state) || {};
    const isRoleByCriteria = getRoleTypeSelector(state) == ROLE_ENTITY_TYPES.ROLE_BY_CRITERIA;
    const tabToSelect = getRolegroupDetailsDpTabToSelect(tabs, selectedTabKey, resourceId, isRoleByCriteria);
    const { RESOURCE_KEY } = ROLE_GROUP_DETAILS_PAGE_TAB_KEYS;

    if (resourceId) {

        if (tabToSelect) {
            chain.push(of(entityWindowOpen(
                ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE,
                TABLE_NAMES.RESOURCE,
                TABLE_NAMES.RESOURCE,
                ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                {},
                resourceId
            )));

            chain.push(of(setDetailsPaneSelectedTab(ROLE_GROUP_DETAILS_PAGE, tabToSelect, ROLE_GROUP_DETAILS_PAGE)));
        }

        if(shouldJobDPItemClose){
            chain.push(of(entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE)));
        }

        if(shouldRoleGroupDPItemClose){
            chain.push(of(entityWindowClose(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE)));
        }
    } else {
        const {rolegroupDetailsPage} = state;
        const {pageState: {params = {}} = {}} = rolegroupDetailsPage;
        const {roleGroupDetailsPane = {}} = rolegroupDetailsPage;
        const activeDetailPane = roleGroupDetailsPane[ROLE_GROUP_DETAILS_PAGE] || {};

        chain.push(of(
            setDetailsPaneSelectedTab(
                ROLE_GROUP_DETAILS_PAGE,
                tabToSelect,
                ROLE_GROUP_DETAILS_PAGE
            )
        ));

        if (activeDetailPane.selectedTabKey === RESOURCE_KEY)
            chain.push(of(entityWindowOpen(
                ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
                TABLE_NAMES.ROLEREQUESTGROUP,
                TABLE_NAMES.ROLEREQUESTGROUP,
                ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT,
                {},
                params.rolerequestgroupGuid
            )));
    }
    return chain;
};

export const assignResourceToCriteria$ = (action$, state$, { apis }) => {
    return action$
        .ofType(
            `${ASSIGN_RESOURCE_TO_CRITERIA}_${ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE}`,
            `${ASSIGN_RESOURCE_TO_CRITERIA}_${ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM}`
        )
        .pipe(
            switchMap((action) => {
                const state = state$.value;
                const { entityId = null, tableName } = action.payload;
                const resourceTableName = TABLE_NAMES.RESOURCE;
                const fieldName = `${tableName}_${resourceTableName}_guid`;
                const criteriaRoleGuid = getActiveEntityID(state, ROLE_REQUEST_FORM);
                const getActiveWindowSelector = createGetActiveWindowSelector();

                const { entity: activeEntity } = getActiveWindowSelector({ entityWindow: state.entityWindow.window[ROLE_REQUEST_FORM] });
                const roleStatusDescription = getPageRoleRequestStatusDescriptionSelector(state)(activeEntity[ROLEREQUEST_FIELDS.STATUS_GUID]);
                const operation = roleStatusDescription === ROLE_ITEM_STATUS_KEYS.DRAFT ? ENTITY_WINDOW_OPERATIONS.EDIT : ENTITY_WINDOW_OPERATIONS.READ;

                const chain = [updateEntityField(tableName, criteriaRoleGuid, fieldName, entityId, operation)];

                if (entityId) {
                    const currentPage = getCurrentPageAliasSelector(state);
                    const tableDatasAlias = getTableDatasAlias(currentPage);
                    const resourceData = getSingleSuggestedResourceDataSelector(state)(entityId);

                    chain.push(loadMoreTableDataSuccess(
                        tableDatasAlias,
                        { tableNames: [resourceTableName], tableDataGuid: resourceTableName },
                        [resourceData]
                    ));
                }

                return from(chain);
            })
        );
};

export const updateEntityField$ = (action$, state$, { apis }) => {
    return action$
        .ofType(ENTITY_WINDOW.UPDATE_ENTITY_FIELD)
        .pipe(
            switchMap((action) => {
                const { tableName, entityId, fieldName, newValue } = action.payload;
                const fieldData = { [fieldName]: newValue };

                return apis[tableDataApiKey].patchTableData$(tableName, entityId, fieldData);
            },
            (action, response) => [action, response]),
            switchMap(([action, response]) => {
                if (response && response.status === ERROR_STATUS) {
                    return empty();
                }

                const { fieldName, newValue, operation, entityId, tableName } = action.payload;
                const chain = [updateFieldValue(ROLE_REQUEST_FORM, newValue, fieldName, operation)];

                if (fieldName === ROLEREQUEST_FIELDS.STATUS_GUID) {
                    chain.push(loadUserEntityAccess([entityId], tableName));
                }

                return from(chain);
            })
        );
};

export const showBudgetSectionMessagesEpic$ = (action$, state$) => {
    return action$
        .ofType(LOAD_WORKFLOW_ENTITY_ACCESS_SUCCESS)
        .pipe(
            filter(() => {
                const state = state$.value;
                const pageAlias = getCurrentPageAliasSelector(state);

                return pageAlias === ROLE_GROUP_DETAILS_PAGE;
            }),
            switchMap(({ payload }) => {
                return from(updateBudgetSectionMessages(state$.value, payload, ROLE_REQUEST_FORM));
            })
        );
};

const getTableDataNameByPageAlias = (pageAlias) => {
    return pageAlias === PLANNER_PAGE_ALIAS ? PLANNER_TABLE_DATA_NAME : TABLE_DATA_NAME;
};

export const redirectToRolegroupDetailsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(REDIRECT_TO_ROLEGROUP_DETAILS_PAGE)
        .pipe(
            switchMap((action$) => {
                const { entityId } = action$.payload;
                const pageAlias = getCurrentPageAliasSelector(state$.value);
                const pageTableDataAlias = getTableDataNameByPageAlias(pageAlias);
                const rolerequestgroupSurrogateId = getRolerequestgroupSurrogateIdSelector(state$.value, pageAlias, pageTableDataAlias)(entityId);
                const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

                return [navigateToRoleGroupDetailsPage(rolerequestgroupSurrogateId, listPageAndBulkUpdateFeatureFlag)];
            })
        );
};

export const updateBudgetSectionMessages = (state, payload, moduleName) => {
    let result = [];
    const { tableName } = payload;
    const operation = getEntityWindowOperation(state, moduleName);

    const entity = getEntityWindowEntity(state.entityWindow, moduleName);
    //We add this default NULL_GUID because the getRoleCriteriaStateSelector expects it otherwise it throws error
    //There are cases where this entityId is not yet populated by the time we go through this chunk
    const entityId = getActiveEntityID(state, moduleName) || NULL_GUID;
    const ewTableName = getEntityWindowTableName(state.entityWindow, moduleName);
    const isCriteriaRole = getIsCriteriaRole(entity);
    const currentTableName = tableName || ewTableName;

    if (isCriteriaRole) {
        const staticMessages = getEntityWindowMessagesSelector(state)(moduleName);
        const hasValidResource = entity[roleRequestResourceField.name];
        const hasHiddenRequirements = getHasHiddenRequirementsSelector(state, entityId, CRITERIA_SECTIONS.MUST_MEET);
        const hasAssignedResources = getCriteriaRoleAssignedResourcesSelector(state)(entityId, entity).length > 0;
        const isRoleRejectable = getEntityHasWorkflowAccessSelector(state)(entityId, currentTableName, ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_REJECT);
        const isRoleApplied = entity[ROLEREQUEST_FIELDS.ROLEREQUEST_CALLER_APPLY_DATE] !== null;
        const pageAlias = getCurrentPageAliasSelector(state);
        const isRoleSuggestible = getIsRoleSuggestibleSelector(state)(entity);

        result = [entityWindowUpdateMessages(
            moduleName,
            currentTableName,
            {
                operation,
                isCriteriaRole: true,
                entityId,
                staticMessages,
                hasValidResource,
                hasHiddenRequirements,
                hasAssignedResources,
                isRoleRejectable,
                isRoleApplied,
                pageAlias,
                isRoleSuggestible
            }
        )];
    }

    return result;
};

export const refreshSingleRoleGroupDataEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(REFRESH_ROLE_GROUP)
        .pipe(
            switchMap(
                action => {
                    const pageParams = getRoleGroupSurrogateId(state$.value);
                    const { rolegroupSurrogateId } = pageParams;

                    const selection = getSelectionConfig(rolegroupSurrogateId);

                    return doGetRolegroupDataApiCall$(apis, TABLE_NAMES.ROLEREQUESTGROUP, selection);
                },
                (action, result) => [result[0], action]
            ),
            switchMap(([result, action]) => {
                if (result == undefined || (result && result.length) == 0 || result.status === ERROR_STATUS) {
                    return empty();
                }

                const rolerequestgroupDesct = result[ROLEREQUESTGROUP_FIELDS.DESCRIPTION];
                const rolerequestgroupSurrogateId = result[ROLEREQUESTGROUP_FIELDS.SURROGATE_ID];
                const rolerequestgroupGuid = result[ROLEREQUESTGROUP_FIELDS.GUID];
                const rolerequestGroupStart = result[ROLEREQUESTGROUP_FIELDS.ROLES_START];
                const rolerequestGroupEnd = result[ROLEREQUESTGROUP_FIELDS.ROLES_END];
                const rolerequestGroupTotalActionableRequest = result[ROLEREQUESTGROUP_FIELDS.TOTALACTIONABLEREQUESTS];
                const rolerequestGroupTotalRoles = result[ROLEREQUESTGROUP_FIELDS.TOTALROLES];
                const jobGuid = result[ROLEREQUESTGROUP_FIELDS.JOB_GUID];
                const jobName = result['job_description'];
                const jobSurrogateId = result['job_surrogate_id'];

                const params = {
                    rolegroupName: rolerequestgroupDesct,
                    rolegroupSurrogateId: rolerequestgroupSurrogateId,
                    rolerequestgroupGuid,
                    rolerequestGroupStart,
                    rolerequestGroupEnd,
                    rolerequestGroupTotalActionableRequest,
                    rolerequestGroupTotalRoles,
                    jobName: jobName,
                    jobId: jobGuid,
                    jobSurrogateId: jobSurrogateId
                };

                return of(updatePageParams(ROLE_GROUP_DETAILS_PAGE, params));
            })
        );
};

const getOperationsMap = (state, entity, tableName, operation) => {
    let operationsMap = {default: operation};
    const getRoleRequestStatusGuid = getRoleRequestStatusGuidSelector(state);
    const draftGuid = getRoleRequestStatusGuid(ROLE_ITEM_STATUS_KEYS.DRAFT);
    entity.forEach(data => {
        const guid = data[`${tableName}_guid`];
        const requestStatus = data[`${tableName}_${TABLE_NAMES.ROLEREQUESTSTATUS}_guid`];
        const getApplicationAccessSelectorWrapped = getApplicationAccessSelector(state);
        const hasAccess = getApplicationAccessSelectorWrapped(tableName, [guid], ENTITY_ACCESS_TYPES.EDIT, null, null, null, { ignoreCustomRestrictions: true });

        operationsMap[guid] = hasAccess ? (requestStatus !== draftGuid ? ENTITY_WINDOW_OPERATIONS.READ : operation) : ENTITY_WINDOW_OPERATIONS.READ;
    });

    return operationsMap;
};

//This should be refactored, because it is very bad to couple opening of ew and validation with loading of user access.
//Response reducer should be removed as well
export const loadRoleGroupDetailsSuccess$ = (action$, state$) => {
    return action$
        .ofType(LOAD_USER_ENTITY_ACCESS_SUCCESS, ROLE_GROUP_DETAILS_PAGE_ACTIONS.LOAD.CLEAN_LOADED_RESPONSE)
        .pipe(
            map((action) => {
                const state = state$.value;
                const { payload } = action;
                const { tableName } = payload;
                const { rolegroupDetailsPage, applicationSettings } = state;
                const { userEntityAccess = {} } = applicationSettings;
                const { processedData = {} } = rolegroupDetailsPage || {};
                const { activeEntityId, processedTableDatas, entityIds, addNewEntity, additionalProps = {} } = processedData;
                let chain = [];
                const moduleName = ROLE_REQUEST_FORM;

                if (tableName == TABLE_NAMES.ROLEREQUEST && userEntityAccess[TABLE_NAMES.ROLEREQUEST] && activeEntityId != null) {
                    const operation = ENTITY_WINDOW_OPERATIONS.EDIT;
                    const operationsMap = getOperationsMap(state, processedTableDatas, tableName, operation);
                    const budgetFieldsVisibility = activeEntityId === EMPTY_STATE && !addNewEntity;

                    chain.push(
                        entityWindowOpenForMultiple(
                            moduleName,
                            tableName,
                            ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS,
                            operationsMap,
                            processedTableDatas,
                            entityIds,
                            activeEntityId
                        ),
                        entityWindowUpdateMultipleEntities(moduleName, tableName, entityIds, processedTableDatas, operationsMap),
                        changeReadOnlyFieldsVisibility(moduleName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, tableName, budgetFieldsVisibility)
                    );

                    if (addNewEntity) {
                        const draftStatusGuid = getRoleRequestStatusGuidSelector(state)(ROLE_ITEM_STATUS_KEYS.DRAFT);
                        const rolerequestGroupGuid = getRoleRequestGroupGuidSelector(state);
                        const jobGuid = getRoleRequestGroupJobGuid(state);

                        const newEntityId = getNewEntityId();
                        const getFieldInfo = getFieldInfoSelector(state);
                        const { windows = {} } = state.entityWindow.window[moduleName] || {};

                        if (additionalProps.shouldAddCriteria) {
                            chain.push(getPopulateNewRoleCriteriaAction(newEntityId));
                        }

                        const expandedAdditionalProps = {
                            ...additionalProps,
                            useMultipleAssignees: getIsMultipleAssigneesEnabled(state)
                        };

                        chain.push(
                            addNewEntityRequest(
                                moduleName,
                                tableName,
                                ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS,
                                ENTITY_WINDOW_OPERATIONS.CREATE,
                                {
                                    [ROLEREQUEST_FIELDS.STATUS_GUID]: draftStatusGuid,
                                    [ROLEREQUEST_FIELDS.ROLE_GROUP_GUID]: rolerequestGroupGuid,
                                    ...getNewEntityFields(tableName, expandedAdditionalProps, windows, getFieldInfo),
                                    [ROLEREQUEST_FIELDS.IS_TEMPLATE]: false,
                                    [ROLEREQUEST_FIELDS.JOB_GUID]: jobGuid
                                },
                                null,
                                false,
                                undefined,
                                newEntityId
                            )
                        );
                    }
                    chain.push(
                        validateMultipleEntities({
                            tableName,
                            moduleName
                        })
                    );
                    const cleanProcessedData = { activeEntityId: null, processedTableDatas: {}, entityIds: [], addNewEntity: null, additionalProps }
                    chain.push(cleanRoleGroupDetails(cleanProcessedData), validateCriteriaRoleBudgetSection({ moduleName, entityIds }));
                }
                return chain;
            }),
            mergeMap((actions) => {
                return concat(
                    ...actions.map(action => of(action))
                );
            })
        );
};

export const openRolegroupDetailsForDifferentRoleGroup$ = (action$, state$) => {
    return action$
        .ofType(ROLE_GROUP_DETAILS_PAGE_ACTIONS.OPEN_FOR_DIFFERENT_ROLE_GROUP)
        .pipe(
            mergeMap(({payload}) => {
                const { rolegroupSurrogateId } = payload;

                const params = {
                    rolegroupSurrogateId
                };

                const newParams = {
                    subPageOption: {
                        subPageNavLink: ROLEGROUPLISTDETAILSPAGE.navigationLink,
                        subPagesParams: params
                    }
                };

                return of(
                    discardBatchEntityChangesAction(ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM),
                    replaceUrl(newParams),
                    PAGE_NAVIGATE_ACTIONS.OPEN[ROLE_GROUP_DETAILS_PAGE](window.location, ROLE_GROUP_DETAILS_PAGE, false, {}),
                    resetPageParams(ROLE_GROUP_LIST_PAGE)
                );
            })
        );
};

export default combineEpics(
    refreshDPOnFieldChange$,
    entityWindowSubmitUpdateResourceInterceptor(),
    entityWindowResourceDPUpdateInlineDetailsPaneSuccessInterceptorEpic(),
    entityWindowResourceDPUpdateInlineDetailsPaneErrorInterceptorEpic(),
    entityWindowRoleGroupDPUpdateInlineDetailsPaneSuccessInterceptorEpic(),
    entityWindowRoleGroupDPUpdateInlineDetailsPaneErrorInterceptorEpic(),
    entityWindowRoleGroupMoveToInterceptorEpic(),
    entityWindowRoleGroupMoveErrorToInterceptorEpic(),
    loadRoleGroupDetailsPageDP$(ROLE_REQUEST_FORM, jobsTableNameAlias),
    createDeleteRoleGroupEpic()(),
    assignResourceToCriteria$,
    updateEntityField$,
    createMovePendingTimeAllocationEpic(ROLE_GROUP_DETAILS_PAGE, () => loadRoleGroupDetails(), managePendingTimeAllocationErrorAction),
    createRemovePendingTimeAllocationEpic(ROLE_GROUP_DETAILS_PAGE, () => loadRoleGroupDetails(), managePendingTimeAllocationErrorAction),
    refreshSingleRoleGroupDataEpic$,
    setRoleOnLoadRoleGroupDetailsPage$,
    loadRoleGroupDetailsSuccess$,
    showBudgetSectionMessagesEpic$,
    redirectToRolegroupDetailsPageEpic$,
    deleteRoleGroupEpic$,
    openRolegroupDetailsForDifferentRoleGroup$,
    rolerequestMoveToEpic$
);