﻿// Any string wrapped in ${ } or { } is a placeholder string and should not be translated literally.
export default {
    //common validation errors
    emailValidationError: 'Proporcione una dirección de correo válida.',
    //commonly used text
    fieldValue: 'Valor del campo',
    confirmation: 'Confirmación',
    invalid: 'No válido',
    current: '{index} (Actual)',
    dismissText: 'Descartar',
    mandatoryValidation: '* Obligatorio',
    //common component
    selectionListLicenseWarning: '{contactUs} para aumentar su límite.',
    selectionListLicenseError: '{contactUs} para aumentar su límite.',
    selectionListDiaryCustomMsg: '{activeCount} de {licensedCount} diarios utilizados',
    selectionListSkillsCustomMsg: '{activeCount} de {licensedCount} habilidades utilizadas',
    commonComponentConfirmationModalConsequence: 'Entiendo las consecuencias de este cambio.',
    commonComponentConfirmationModalEmptyMsg: ' ',
    noOptionsAvailable: 'No hay opciones disponibles',
    actionBarSaveButtonLabel: 'Guardar los cambios',
    actionBarConfirmChangesLabel: 'Confirmar los cambios',
    actionBarCancelButtonLabel: 'Cancelar',
    actionBarDiscardChangesLabel: 'Descartar los cambios',
    actionBarLeavePageMessage: '¿Confirma que desea salir de la página?',
    actionBarUnsavedChanges: 'Hay cambios sin guardar en esta página. ¿Desea salir?',
    actionBarFormErrorMessage: 'Este formulario tiene errores.',
    deleteModalCancelButtonLabel: 'No, conservar el perfil de seguridad',
    deleteModalSubmitButtonLabel: 'Sí, borrar el perfil de seguridad',
    securityProfileDeleteModalTitle: '¿Desea borrar el perfil de seguridad {profileName}?',
    securityProfileDeleteModalText: '¿Desea borrar permanentemente este perfil de seguridad?',
    cannotBeUndoneText: 'Esta acción no se puede deshacer.',
    addAnItemText: 'Añadir un elemento',
    selectionListSortText: 'Ordenar A-Z',
    deleteSectionConfirmation: '¿Confirma que desea borrar {sectionName}?',
    deleteSectionConfirmationTitle: 'Borrar confirmación de sección',
    CreateContextMenuLabel: 'Crear',
    RenameContextMenuLabel: 'Cambiar de nombre',
    DuplicateContextMenuLabel: 'Duplicar',
    DeleteContextMenuLabel: 'Borrar',
    DUPLICATE_USING_APIContextMenuLabel: 'DUPLICATE_USING_API',
    toasterDefaultSuccessMessage: 'Se han guardado los cambios',
    toasterDefaultErrorMessage: 'Fallo al guardar, vuelva a intentarlo',
    toasterDefaultWarningMessage: 'No se han guardado algunos cambios; actualice y vuelva a intentarlo',
    toasterDefaultUnsavedChangesMessage: 'No se han podido guardar los cambios; resuelva los errores y vuelva a intentarlo.',
    actionCannotBeUndoneMessage: 'No se puede deshacer esta acción.',
    noDataAvailableText: 'Sin datos',
    licensedCountLabel: '{activeCount} de {licensedCount} utilizados ',
    defaultLimitationInfo: 'El límite se basa en su plan de licencia actual.',
    contactUsText: 'Contáctenos',
    importText: 'Importar',
    noResultsFound: 'No se encontraron resultados',
    reorderText: 'Reordenar',
    editText: 'Editar',
    resetText: 'Restablecer',
    searchText: 'Buscar',
    nameText: 'nombre',

    //skill filter cascader component
    skillFilterResetButtonDisabledMessage: 'No es posible restablecer: No se han seleccionado habilidades.',
    skillFilterApplyButtonDisabledMessage: 'No es posible aplicar: No se han seleccionado habilidades.',
    skillFilterApplyButtonDisabledForMaxCountMessage: 'No es posible aplicar: Se permite un máximo de {maxSkillSelection} selecciones.',

    //Import data
    entityImportSelectValuePlaceholder: 'Seleccionar valor',
    ImportSuccessfulToasterMessage: 'Se ha importado correctamente',
    ImportUnsuccessfulToasterMessage: 'No se ha importado correctamente',
    operationLogChangesSaved: 'Se han guardado los cambios en el registro de',
    //userManagement Messages
    userManagementLicenseWarning: 'Está cerca del límite que permite su licencia. Ajuste los usuarios a \'Inactivo\' o {contactUs} para aumentar el límite de su plan.',
    userManagementLicenseError: 'Ha alcanzado el límite que permite su licencia. Ajuste los usuarios a \'Inactivo\' o {contactUs} para aumentar el límite de su plan.',
    userManagementLicenseErrorHeading: 'No puede tener más de {number} usuarios activos.',
    userManagementLicenseActiveUsers: 'Tiene {number} usuarios ajustados a ‘Activo’.',
    userManagementLicenseUserLimit: 'Su licencia actual admite hasta {number} usuarios activos.',
    userManagementLicenseContactUS: 'Ajuste algunos usuarios a ‘Inactivo’ o {contactUs} para aumentar su límite.',
    userManagementInactiveUserHeading: '¿Ajustar {number} usuarios a \'Inactivo\'?',
    userManagementSetInactiveUserPopup: '¿Desea ajustar {number} usuarios a \'Inactivo\'?',
    userManagementUnAssignBooks: 'No podrán iniciar sesión y se les desasignará de las reservas actuales. No será posible asignarlos a futuras reservas.',
    userManagementReportingArchive: 'Si estaban asignados a reservas pasadas, estos datos se conservarán para fines de archivado o generación de informes.',
    userManagementInactiveConfirmation: 'Ajuste {number} usuarios a \'Inactivo\' y elimínelos de las reservas actuales',
    userManagementInactivePopupPrimaryButton: 'Convertir los usuarios en Inactivos',
    userManagementInactivePopupSecondaryButton: 'Cancelar',
    //for confirmation modal popup need to change later
    userManagementDeletePopupMessage: '¿Borrar los usuarios seleccionados?',
    userManagementDeleteWarningMessage: '¿Desea eliminar permanentemente a {número} usuarios?',
    userManagementDeleteBookingsWarning: 'Se eliminarán todos sus datos del sistema. Las reservas pasadas a las que fueron asignados quedarán sin asignar y pueden afectar a cualquier informe que implique esas reservas.',
    userManagementDeleteHistoricData: 'Si desea conservar datos históricos, solo tiene que establecer este Estado de usuario como \'Inactivo\'.',
    userManagementDeleteConfirmation: 'Borrar los usuarios seleccionados y eliminarlos de cualquier reserva o informe del sistema',
    userManagementDeletePrimaryButton: 'Borrar usuarios',
    userManagementDeleteSecondaryButton: 'Conservar los usuarios seleccionados',
    userManagementDescription: 'Añadir, editar o borrar usuarios aquí. Navegue hasta sus perfiles para realizar más cambios.',
    userManagementSubTitle: 'Usuarios activos ',
    userManagementNotifyUserCountDescription: '{activeUser} de sus {totalUser} usuarios están activos. Su licencia admite hasta {licencedActiveUser} usuarios activos.',
    userManagementAddUserButtonFroCommandBar: 'Añadir un usuario',
    userManagementToNavigateImportFeatureMessage: 'Para las actualizaciones masivas, es posible que prefiera utilizar la función {Import}.',
    userManagementFormLabelsSecurityProfile: 'Perfil de seguridad',
    userManagementFormLabelsUserStatusDescription: 'Si el estado de un usuario como activo, podrá acceder a su cuenta.',
    userManagementFormLabelsName: 'Nombre',
    userManagementFormLabelsEmail: 'Correo electrónico',
    userManagementFormLabelsUserStatus: 'Usuario activo',
    userManagementFormLabelsUserLastLogin: 'Último acceso',
    userManagementValidationFirstName: 'Escriba su nombre',
    userManagementValidationLastName: 'Escriba su apellido',
    userManagementValidationEmailReqd: 'Escriba su correo electrónico',
    userManagementValidationEmailMsg: 'No ha escrito un correo electrónico válido',
    userManagementValidationSecurityProfile: 'Es obligatorio seleccionar un perfil de seguridad',
    userManagementTooltipGoToProfile: 'Ir al perfil',
    userManagementTooltipResetPass: 'Restablecer contraseña',
    userManagementTooltipResendEmail: 'Enviar correo electrónico de invitación',
    userManagementTooltipMarkDelete: 'Marcar para borrar',
    userManagementFirstNamePlaceholder: 'Nombre',
    userManagementLastNamePlaceholder: 'Apellido',
    userManagementEmailPlaceholder: 'Escribir correo electrónico',
    userManagementSecurityProfilePlaceholder: 'Seleccionar un perfil de seguridad',
    userManagementTitle: 'Gestión de usuarios',
    userManagementStatusActive: 'Activo',
    userManagementStatusInactive: 'Inactivo',
    userManagementDuplicateEmail: 'Asegúrese de que los usuarios tienen identificaciones de correo electrónico únicas.',
    userManagementUserAccess: 'Permisos insuficientes para editar estos recursos',
    //comopanyInfo Messages
    companyInfoNameLabel: 'Nombre de la empresa',
    companyInfoLogoLabel: 'Logotipo',
    companyUploadLogoLabel: 'Cargar el logotipo',
    companyInfoLogoThumbnailLabel: 'Miniatura del logotipo',
    companyUploadLogoThumbnailLabel: 'Cargar la miniatura del logotipo',
    companyInfoSupportPhoneNumberLabel: 'Número de teléfono de asistencia',
    companyInfoApplicationLanguage: 'Idioma de la aplicación',
    companyInfoSupportEmailLabel: 'Correo electrónico de asistencia',
    companyInfoInstanceOwnerLabel: 'Propietario de la instancia',
    companyInfoLogoControlMessage: 'El logotipo debe tener un tamaño de 80 px X 24 px y estar en formato .png',
    companyInfoUploadControlText: 'Haga clic o arrastre el archivo a esta zona para cargarlo',
    companyInfoLogoThumbnailControlMessage: 'La miniatura del logotipo debe tener un tamaño de 24 px X 24 px y estar en formato .png',
    companyInfoPhoneNumberErrorMessage: 'Indique un número de teléfono válido',
    companyInfoEmailErrorMessage: 'Indique una dirección de correo electrónico válida',
    companyInfoLogoErrorMessage: 'Cargue un logotipo válido',
    companyInfoLogoThumbnailErrorMessage: 'Cargue una miniatura de logotipo válida',
    companyInfoLookUpNoMatches: 'No hay resultados',
    companyInfoSelectValuePlaceholder: 'Seleccionar valor',
    companyInfoHelpSupportMessage: 'Para obtener asistencia, póngase en contacto',
    companyInfoVersion: 'Versión {version}',
    companyInfoTitle: 'Información de la empresa',
    companyInfoFileUploadFailed: 'Fallo al cargar el archivo {fileName}.',
    //Currency Messages
    currencyHeaderText: 'Divisa',
    currencyDefaultMessage: 'Divisa del sistema',
    currencyDescription: 'Seleccione la divisa que se va a utilizar en toda la aplicación.',
    baseCurrencyLabel: 'Divisa base',
    //diary calendar messages
    diaryCalendarHeading: 'Agenda',
    diaryCalendarSubHeading: 'Personalice el año definiendo un patrón de trabajo estándar y los días no laborables, como los días festivos y el cierre de la oficina.',
    diaryCalendarCustomDayTitle: 'Añadir un día personalizado',
    diaryCalendarCustomPeriodTitle: 'Añadir un periodo personalizado',
    diaryCalendarSelectDateRangeRequiredMsg: 'Seleccione un rango de fechas',
    diaryCalendarCustomPeriodRequiredMsg: 'Se necesita un nombre para el periodo personalizado',
    diaryCalendarWorkPatternRequiredMsg: 'Seleccione un patrón de trabajo',
    diaryCalendarSelectDateRequiredMsg: 'Seleccione una fecha',
    diaryCalendarCustomDayRequiredMsg: 'Se necesita un nombre para el día personalizado',
    diaryCalendarDayTypeRequiredMsg: 'Seleccione un tipo de día',
    diaryCalendarReplaceCustomPeriodMsg: '¿Desea reemplazar "{overlappingPeriods}" por un nuevo período personalizado?',
    diaryCalendarReplaceCustomDayMsg: '¿Desea reemplazar "{overlappingDayName}" por un nuevo día personalizado?',
    //for confirmation modal popup need to change later
    diaryCalendarSaveHistoricalDataAlert: '¿Confirma que desea guardar los cambios en los datos históricos?',
    diaryCalendarSavePastDayChangesMsg: 'Sus cambios pueden afectar a los cálculos de las reservas pasadas. Esta acción no se puede deshacer.',
    diaryCalendarStandardWorkPatternLabel: 'Patrón de trabajo estándar',
    diaryCalendarCustomDaysGridHeading: 'Días personalizados',
    diaryCalendarCustomPeriodsGridHeading: 'Periodos personalizados',
    diaryCalendarCustomDaysAddBtn: 'Añadir día personalizado',
    diaryCalendarCustomPeriodsAddBtn: 'Añadir periodo personalizado',
    diaryCalendarCustomDayNamePlaceholder: 'Nombre del día personalizado',
    diaryCalendarCustomPeriodNamePlaceholder: 'Nombre del periodo personalizado',
    diaryCalendarCustomDayTypePlaceholder: 'Tipo de día personalizado',
    diaryCalendarCustomWorkPatternPlaceholder: 'Patrón de trabajo del periodo personalizado',
    diaryCalendarCustomDayIsInUseMessage: 'Se solapa con un día personalizado existente',
    diaryCalendarCustomPeriodIsInUseMessage: 'Se solapa con un periodo personalizado existente',
    diaryCalendarCustomGridDateRequiredMessage: 'Proporcione una fecha',
    diaryCalendarCustomGridRangeRequiredMessage: 'Proporcione un rango de fechas',
    diaryCalendarCustomGridNameRequiredMessage: 'Proporcione un nombre',
    diaryCalendarCustomGridDayTypesRequiredMessage: 'Proporcione un tipo de día',
    diaryCalendarCustomGridWorkPatternsRequiredMessage: 'Proporcione un patrón de trabajo',
    diaryCalendarTotalHourSumErrorMessage: 'El total de horas debe ser inferior o igual a 24 horas',
    diaryCalendarHoursAginstDurationError: 'La hora de inicio y de fin debe incluir el número de horas asignadas',
    diaryCalendarCustomDayPlaceholder: 'Escriba el nombre del día personalizado',
    diaryCalendarDayTypePlaceholder: 'Seleccione el tipo de día',
    diaryCalendarCustomPeriodPlaceholder: 'Escriba el nombre del período personalizado',
    diaryCalendarWorkPatternPlaceholder: 'Seleccione el patrón de trabajo',
    diaryCalendarRangeDateLabel: 'Fecha de inicio y de fin',
    diaryCalendarPatternLabel: 'Patrón',
    diaryNameRequiredMessage: 'Proporcione un nombre para la agenda',
    uniqueDiaryNameMessage: 'Introduzca un nombre de agenda único',
    diaryWarningTitle: 'No se puede borrar el calendario de la agenda',
    diaryWarningMessage: 'No se puede borrar "{diaryNames}&quot de la agenda;. Esta agenda se asigna a los recursos.',
    //button labels
    confirmButtonLabel: 'Confirmar',
    cancelButtonLabel: 'Cancelar',
    saveButtonLabel: 'Guardar',
    okButtonLabel: 'Aceptar',
    //table headings
    diaryCalendarCustomDaysDateTableHeading: 'Fecha',
    //same value need to check
    diaryCalendarCustomDaysNameTableHeading: 'Nombre',
    diaryCalendarCustomPeriodsRangeTableHeading: 'Fecha de inicio y fin',
    diaryCalendarCustomPeriodsWorkPatternTableHeading: 'Patrón de trabajo',
    //work pattern
    workPatternHeading: 'Patrones de trabajo',
    workPatternCommandBarFieldName: 'Editar el nombre del patrón de trabajo',
    workPatternSubHeading: 'Se puede crear un patrón de trabajo seleccionando un día de inicio y tipos de días individuales. Añada, edite o elimine patrones de trabajo aquí.',
    //for confirmation modal popup need to change later
    workPatternSaveInUseAlert: '¿Confirma que quiere guardar los cambios para el patrón de trabajo en uso?',
    workPatternSaveInUseChangesMessage: 'Sus cambios pueden afectar al calendario de la agenda. Esta acción no se puede deshacer.',
    workPatternAddButton: 'Añadir un día',
    workPatternReqValidation: 'Seleccione un tipo de día',
    workPatternUniqueMsg: 'Escriba un nombre de patrón de trabajo único',
    workPatternReqdMsg: 'Proporcione un nombre de patrón de trabajo',
    workPatternStartDayLabel: 'Día de inicio',
    workPatternDayTableHead: 'Día',
    workPatternWarningMessage: 'Para borrarlo, elimínelo de las agendas.',
    workPatternWarningTitle: 'No puede borrar el patrón de trabajo {selection} porque está en uso.',
    //Day Types
    dayTypePageHeading: 'Tipos de día',
    dayTypeHeading: 'Tipo de día',
    dayTypeCommandBarFieldName: 'Editar el nombre del tipo de día',
    dayTypeSubHeading: 'Configure el tipo (día laborable o no laborable) de forma que refleje el horario habitual de su organización.',
    //for confirmation modal popup need to change later
    dayTypeSaveInUseAlert: '¿Confirma que desea guardar los cambios para el tipo de día en uso?',
    dayTypeSaveInUseChangesMessage: 'Sus cambios pueden afectar al calendario de la agenda. Esta acción no se puede deshacer.',
    dayTypeWorkingHoursRequiredMessage: 'Escriba el tiempo de trabajo',
    dayTypeContingencyTimeRequiredMessage: 'Escriba el tiempo de contingencia',
    dayTypeWorkingHoursCannotZeroMessage: 'El tiempo de trabajo no puede ser 00:00',
    dayTypeWorkDayLabel: 'Día laborable',
    dayTypeNonWorkDayLabel: 'Día no laborable',
    dayTypeWorkTimeLabel: 'Tiempo de trabajo',
    dayTypeContingencyTimeHours: 'Tiempo de contingencia',
    dayTypeTitleRequiredMessage: 'Proporcione el nombre del tipo de día',
    dayTypeTitleUniqueMessage: 'Escriba un tipo de día único',
    dayTypeWarningTitle: 'No puede borrar el tipo de día {selection} porque está en uso.',
    dayTypesWarningMessage: 'Para borrarlo, elimínelo del patrón de trabajo.',
    //entity import
    entityImportPageHeader: 'Importar datos',
    entityImportPageSummaryText: 'Importe los datos en su aplicación mediante dos sencillos pasos:<ol><li>Descargue la plantilla correspondiente y rellene los datos en ella asegurándose de que cumplimentar los campos obligatorios</li><li>Cargue la plantilla que ha rellenado</li></ol>Los datos que haya rellenado en la plantilla se importarán en la aplicación.',
    entityImportDownloadTemplatesHeader: 'Descargar una plantilla',
    entityImportDownloadTemplatesNumber: '1',
    entityImportUploadDataHeader: 'Cargar la plantilla que ha rellenado',
    entityImportUploadDataNumber: '2',
    entityImportJobLabel: 'Trabajos',
    entityImportClientLabel: 'Clientes',
    entityImportResourceLabel: 'Recursos',
    entityImportSkillLabel: 'Habilidades',
    entityImportUploadControlText: 'Haga clic o arrastre un archivo a esta zona para cargarlo',
    entityImportSelectUploadFileLabel: 'Seleccione un archivo para cargarlo',
    entityImportImportSuccessful: 'Se ha importado correctamente',
    entityImportImportUnsuccessful: 'No se ha importado correctamente',
    entityImportTemplateDownloadFailed: 'no se puede descargar la plantilla',
    entityImportUploadControlError: 'Seleccione el archivo para cargarlo',
    entityImportUploadDropDownError: 'Seleccione el tipo',
    entityImportUploadDropDownPlaceholder: 'Elija el tipo de datos',
    entityImportFileUploadFailed: 'fallo al cargar el archivo.',
    entityImportTypeOfData: 'Tipo de datos',
    entityImportUploadAndVerify: 'Cargar y verificar',
    entityImportCancelBtn: 'Cancelar',
    entityImportConfirmImport: 'Confirmar importación',
    entityImportFormValidateMsg: 'Haga clic para una validación previa ante de importar',
    entityImportmailSubject: 'Conservar el límite de licencia en la nube',
    entityImportUploadProcessed: '{EntriesProcessedCnt} {currentEntityType} entradas procesadas.',
    entityImportTemplateFileName: 'EntityImportTemplate',
    processFormErrorCorrectionText: 'Para finalizar la importación, corrija los siguientes errores.',
    processFormAlternateOption: 'También puede optar por eliminarlos o cancelar la actualización, actualizar Microsoft Excel y volver a cargarlo.',
    processFormRowNoFromExcelMsg: 'El número de fila correspondiente del archivo de Microsoft Excel se muestra a continuación.',
    processFormRequiredClientNameField: 'El campo Nombre del cliente es obligatorio.',
    processFormRequiredClientCodeField: 'El campo Código de cliente es obligatorio.',
    processFormRequiredJobTitleField: 'El campo del cargo es obligatorio.',
    processFormRequiredSkillNameField: 'El campo de nombre de la habilidad es obligatorio.',
    processFormRequiredSkillInfoField: 'El campo de información sobre habilidades es obligatorio.',
    processFormRequiredSkillSectionField: 'El campo de la sección de habilidades es obligatorio.',
    processFormRequiredFirstNameField: 'El campo de nombre es obligatorio.',
    processFormRequiredLastNameField: 'El campo de apellido es obligatorio.',
    processFormRequiredEmailField: 'El campo de correo electrónico es obligatorio.',
    processFormProcessed: ' procesado.',
    processFormProcessedWithError: ' procesado con errores.',
    processFormProcessedWithNoError: '  procesado sin errores.',
    processFormWithError: ' con errores',
    processFormWithNoError: ' sin errores',
    processFormLicenseUserContError: 'No se pueden importar estas entradas de {currentEntityType}.',
    processFormLicenseUserDivLine1: 'Su licencia admite hasta <b></b>{allowedActiveUsersCount} usuarios activos.',
    processFormLicenseUserDivLine2_1: 'Esta importación dará como resultado {totalRecordsProcessed} usuarios activos.',
    processFormContactUs: '{contactUs}',
    processFormLicenseUserDivLine2_2: 'para aumentar su límite.',
    processFormLicenseUserContErrorAlert: 'Se ha producido un error. No se puede procesar la importación',
    processFormContactUsText: 'Contáctenos',
    //color scheme
    colourSchemeHeader: 'Tema de color',
    colourSchemeFieldName: 'Editar el nombre del tema de color',
    colourSchemeHeaderAddButtonText: 'Añadir una regla de color',
    colourSchemeSummaryText: 'Muestre cada tipo de reserva en un color diferente seleccionando su color preferido para cada campo.',
    colourSchemeRolesSummaryText: 'Muestre los roles en un color diferente seleccionando su color preferido para cada campo.',
    colourSchemeConfirmModalText: 'Al cambiar el campo, se eliminarán todas las reglas existentes sobre el tema del color. ¿Continuar?',
    colourSchemeTableRequired: 'Seleccione la tabla',
    colourSchemeFieldRequired: 'Seleccione el campo',
    colourSchemeGridEmptyText: 'No se han creado normas de color',
    colourSchemeGridAddButtonText: 'Añadir una regla',
    colourSchemePreviewText: 'Vista previa del texto',
    colourSchemeFieldValueRequired: 'Seleccione el valor del campo de la lista',
    colourSchemeFieldValueUnique: 'El valor del campo debe ser único',
    colourSchemeLookUpNoMatches: 'No hay resultados',
    colourSchemeSelectValuePlaceholder: 'Seleccionar valor',
    colourSchemeResourceLookupPlaceholder: 'Sin asignar',
    colourSchemeColourSchemeAddButton: 'Añadir una regla de color',
    colourSchemeTable: 'Tabla',
    colourSchemeField: 'Campo',
    colourSchemePreviewTextTitle: 'Vista previa',
    colourSchemeColourCodeTextTitle: 'Color',
    colourSchemeCreateColorTheme: 'Crear tema de color',
    colourSchemeFieldDropdownPlaceholder: 'Elegir campo',
    colourSchemeTableDropdownPlaceholder: 'Ninguno',
    colorSchemeUniqueTitle: 'Introduzca un nombre de tema de color único',
    colorSchemeRequiredTitle: 'Proporcione un nombre de tema de color',
    colourThemeTabTitle_Bookings: 'Reservas',
    colourThemeTabTitle_Roles: 'Funciones',
    colourSchemeDescriptionPlaceholder: 'Descripción del color',
    //conflicts
    conflictPageHeader: 'Conflictos',
    conflictsMsgsSubHeaderLabel: 'Cuando mostrar conflictos.',
    conflictsMsgsSubLabel_line1: 'Elegir si mostrar conflictos. Seleccione el umbral igual y superior según el cual una o varias reservas asignadas a un recurso aparecerán como un conflicto.',
    conflictsMsgsResourceLoadingControlLabel: 'Cuando la carga de recursos sea, como mínimo',
    conflictsMsgsShowConflictsLabel: 'Mostrar conflictos',
    conflictsMsgsConfermationModelHeader: 'Escriba un umbral de carga válido.',
    conflictsMsgsConfermationModelSubHeader: 'El umbral de carga debe estar entre {minValue} y {maxValue}%.',
    conflictsMsgsShowResourceLoadingNote: 'Los valores de carga aceptados son de {minValue} a {maxValue}%.',
    conflictsMsgsOkBtn: 'Aceptar',
    conflictsMsgsYesText: 'Sí',
    conflictsMsgsNoText: 'No',

    //Service accounts
    serviceAccounts: {
        maximumFieldLengthValidationMessage: 'Máximo ${maximumFieldSize} caracteres',
        pageHeader: 'Gestión de cuenta de servicio',
        addButton: 'Añadir una cuenta de servicio',
        addEntity: 'Añadir una cuenta de servicio',
        saveButtonLabel: 'Guardar los cambios',
        cancelButtonLabel: 'Cancelar',
        markedForDeletionMessage: 'Marcado para borrarlo. Se borrará cuando confirme los cambios.',
        cancelDeletion: 'Cancelar borrado',
        serviceAccountManagementTitle: 'Gestión de cuenta de servicio',
        serviceAccountsSubTitle: 'Cuentas de servicio activas',
        serviceAccountDescription: '¿Desea construir algo que se integre con Retain y lo amplíe? Añadir, editar o borrar cuentas de servicio aquí.',
        serviceAccountNavigateToRetainApiDocumentationMessage: 'Puede leer más información sobre <linkText>las API en la nube de Retain</linkText> en nuestra documentación de ayuda.',
        serviceAccountManagementUsedAccountsWarning: 'Se está acercando al límite de 5 cuentas de servicio que se pueden añadir. Gestione las cuentas existentes o elimine las que no utilice.',
        serviceAccountManagementUsedAccountsError: 'Ha alcanzado el límite de 5 cuentas de servicio que se pueden añadir. Gestione las cuentas existentes o elimine las que no utilice.',
        emptyStateMessage: 'Sin resultados',
        setPassword: 'Establecer contraseña',
        password: 'Contraseña',
        name: 'Nombre',
        tenant: 'Inquilino',
        securityProfile: 'Perfil de seguridad',
        email: 'Correo electrónico',
        savePasswordTooltip: 'Guarde los cambios antes de establecer una contraseña',
        nameValidationMessage: 'Escriba el nombre',
        emailValidationMessage: 'Escriba su correo electrónico',
        typeHerePlaceholder: 'Escriba aquí',
        nameColumnTitle: 'Nombre',
        emailColumnTitle: 'Correo electrónico',
        securityProfileColumnTitle: 'Perfil de seguridad',
        actionsColumnTitle: 'Acciones',
        emailExplanation: 'Escriba un correo electrónico único que no esté en uso para una cuenta existente',
        formHasErrorsMessage: 'Este formulario tiene errores'
    },

    //workflows
    workflowsSettings: {
        roleByNameWorkflowPageHeader: 'Roles por nombre',
        roleByRequirementsWorkflowPageHeader: 'Roles por requisitos',
        rolesByNamePageDescriptionLabel: 'Los roles pueden utilizarse para solicitar recursos para una tarea. Los roles por nombre se utilizan cuando se conocen los recursos que uno desea.',
        rolesByRequirementsPageDescriptionLabel: 'Los roles pueden utilizarse para solicitar recursos para una tarea. Los roles por requisitos permiten enviar solicitudes para encontrar recursos que coincidan.',
        draftStateDescription: 'Los roles pueden guardarse como borradores si no están listos para enviarse como solicitudes.',
        requestedStateDescription: 'Roles que pueden crearse o rechazarse en el momento, tales como las reservas.',
        liveStateDescription: 'Roles que pueden reservarse.',
        rejectedStateDescription: 'Roles que han sido rechazados.',
        archivedStateDescription: 'Roles que ya no requieren ninguna acción.',
        statesLegendTitle: 'Estados de flujo de trabajo',
        actorsSectionTitle: 'Actores del flujo de trabajo',
        actorsSectionDescription: 'Defina lo que puede hacer cada uno de los actores en el flujo de trabajo.',
        requesterActorTitle: 'Solicitante',
        roleByNameRequesterActorDescription: 'Crea roles para enviar solicitudes para una tarea.',
        roleByRequirementsRequesterActorDescription: 'Crea roles para enviar solicitudes con un conjunto de requisitos.',
        whoCanCreateRolesLabel: 'Cualquier persona puede crear roles para',
        roleByRequirementsWhoCanCreateRolesLabel: 'Cualquier persona puede crear roles en',
        whoCanCreateRolesForThemselvesLabel: 'Cualquier persona puede crear sus propios roles en',
        creatorActionsInfoBannerLabel: 'Las acciones que aparecen a continuación solamente están disponibles para la persona que creó el rol.',
        deleteRolesWithAssigneesInfoBannerLabel: 'Para eliminar las funciones con múltiples asignados, el usuario debe cumplir esta condición para todos los asignados.',
        draftRolesActionsTitle: 'Borrador',
        requesterCanEditDeleteLabel: 'Editar y borrar borradores de roles',
        requesterCanSubmitLabel: 'Enviar solicitud',
        requesterCanArchiveLabel: 'Archivar',
        requestedRolesActionsTitle: 'Solicitado',
        requesterCanRestartRequestedLabel: 'Reiniciar',
        requesterCanDeleteRequestedLabel: 'Borrar roles solicitados',
        restartingActionsTitle: 'Reiniciando',
        requesterCanRestartRejectedLabel: 'Reiniciar roles rechazados',
        requesterCanRestartArchivedLabel: 'Reiniciar roles archivados',
        completedRolesActionsTitle: 'Roles completados',
        requesterCanDeleteLiveLabel: 'Borrar roles activos',
        requesterCanDeleteRejectedLabel: 'Borrar roles rechazados',
        requesterCanDeleteArchivedLabel: 'Borrar roles archivados',
        assignerActorTitle: 'Persona que asigna',
        assignerActorDescription: 'Asigna recursos a los roles solicitados en base a los requisitos.',
        assignerWhoCanRespondLabel: 'puede asignar roles',
        assignerCanAssignResourcesLabel: 'Asignar recursos a los roles',
        approverActorTitle: 'Persona que aprueba',
        approverActorDescription: 'Responde a las solicitudes.',
        appproverWhoCanRespondLabel: 'puede responder a solicitudes para',
        approverCanMakeLiveLabel: 'Activar',
        criteriaRoleCanMakeLiveLabel: 'Crear una función o asignado en vivo',
        approverCanRejectLabel: 'Rechazar',
        criteriaRoleCanRejectLabel: 'Rechazar una función o asignado',
        approverCanRestartLabel: 'Reiniciar',
        approverCanDeleteRequestedLabel: 'Borrar roles solicitados',
        approverCanDeleteLiveLabel: 'Borrar roles activos',
        approverCanDeleteRejectedLabel: 'Borrar roles rechazados',
        approverCanDeleteArchivedLabel: 'Borrar roles archivados',
        approverSelectedResourcesInvalidValue: 'Máx. ${maxLimitCount} recursos. Si se necesitan más personas que aprueben, considere utilizar \'Perfiles de seguridad seleccionados\'.',
        assignerSelectedResourcesInvalidValue: 'Máx. ${maxLimitCount} recursos. Si se necesitan más personas que asignen, considere utilizar \'Perfiles de seguridad seleccionados\'.',
        requesterSelectedResourcesInvalidValue: 'Máx. ${maxLimitCount} recursos. Si se necesitan más solicitantes, considere utilizar \'Perfiles de seguridad seleccionados\'.',
        selectedSecurityProfilesInvalidValue: 'Máximo ${maxLimitCount} perfiles de seguridad',
        addAnotherPrefix: 'Añadir un',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'se encontró con este nombre.',
        multiValueFieldErrorMessagePrefix: 'Seleccione al menos uno',
        saveButtonLabel: 'Guardar cambios',
        cancelButtonLabel: 'Cancelar',
        formHasErrorsMessage: 'El formulario tiene errores',
        noResultsFoundMessage: 'No se encontraron resultados',
        roleCreatorLabel: 'El creador de roles y',
        whoCanActAsRequesterLabel: 'pueden actuar como solicitantes de un rol.'
    },
    //report settings
    reportSettingsSubLabel: 'Establezca objetivos para su organización',
    reportSettingsErrorTitle: 'Valor objetivo incorrecto',
    reportSettingsBillabillityLabel: 'Objetivo de uso facturable global',
    reportSettingsBillabilityRule: 'Establecer un objetivo global con el que comparar los datos reales de uso facturable',
    reportSettingsJobOpportunityLabel: 'Umbral de oportunidad de trabajo',
    reportSettingsJobOpportunityRule: 'Porcentaje mínimo de oportunidad para contabilizar un trabajo como facturable',
    reportSettingsFieldTitle: 'Uso facturable',

    //Colour schemes
    deleteColourSchemeTitle: '¿Borrar "{itemTobeDeleted}" tema de color?',
    deleteColourSchemeInformationMessage: 'Cualquier plan que utilice este tema volverá al tema por defecto. ',
    deleteColourSchemeWarningMessage: 'No se puede deshacer esta acción.',
    colourSchemeCheckMessage: 'Entiendo el impacto de la eliminación de este tema de color',
    deleteColourSchemePrimaryButton: 'Borrar tema de color',
    deleteColourSchemeSecondaryButton: 'Conservar tema de color',

    colorPickerText: 'Selector de color',

    //security Profile
    securityProfilePageHeader: 'Perfiles de seguridad',
    securityProfileFieldName: 'Editar el nombre del perfil de seguridad',
    functionalAccessHeading: 'General',
    functionalAccessSubHeading: 'Controla a qué páginas y funcionalidades tiene acceso este perfil de seguridad. Por ejemplo, puede utilizar una regla de acceso funcional para bloquear el acceso a la configuración de la administración.',
    yesLabel: 'Sí',
    noLabel: 'No',
    entityAccessSubHeading: 'Controla el nivel de acceso que este perfil de seguridad tiene a {entityName}s.',
    skillEntitySubHeading: 'Controla el nivel de acceso que este perfil de seguridad tiene a {entityName}.',
    entityAccessSubHeadingRemaining: 'Active o desactive el acceso o establezca niveles de acceso para un control más pormenorizado.',
    readEntityResource: '{entityName} siempre están visibles para los usuarios, pero puede restringir la visibilidad de ciertos {entityName} por medio de los controles que aparecen más abajo. La seguridad de lectura {lineBreak} sustituirá a todas las otras reglas de seguridad.',
    readEntityJob: '{entityName} siempre están visibles para los usuarios, pero puede restringir la visibilidad de ciertos {entityName} por medio de los controles que aparecen más abajo. Los usuarios de {lineBreak} siempre podrán ver las Tareas que se les han asignado. La seguridad de lectura sustituirá a todas las demás reglas de seguridad.',
    readEntityBooking: '{entityName}s están visibles dependiendo de las condiciones de lectura ajustadas para el trabajo y el recurso.{entityName} La seguridad de la lectura reemplaza a las demás reglas de seguridad.',
    readEntityRole: '{entityName}s están visibles dependiendo de las condiciones de lectura ajustadas para el trabajo y el recurso.La seguridad de la lectura reemplaza a las demás reglas de seguridad.',
    readEntityScenario: '{entityName}s están siempre visibles para todos los usuarios.La seguridad de la lectura reemplaza a las demás reglas de seguridad.',
    readEntityRoleRequest: '{entityName}es siempre están visibles para todos los usuarios. La seguridad de lectura sustituirá a todas las demás reglas de seguridad.',
    readEntityClient: '{entityName}es siempre están visibles para todos los usuarios. La seguridad de lectura sustituirá a todas las demás reglas de seguridad.',
    createEntity: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para crear {entityName}s.',
    readEntitySkill: 'Las habilidades y certificaciones siempre están visibles, pero puede restringir cuáles de ellas se pueden añadir a un perfil por medio de los controles siguientes.',
    editEntity: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para editar {entityName}s.',
    deleteEntity: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para que no pueda borrar {entityName}s.',
    customConditionsAreaHeader: '{entityName} que...',
    liveRoleSubHeading: 'Funciones en vivo ',
    liveRoleBookingMessage: 'La creación de reservas en vivo a partir de las funciones está controlada por los permisos de reserva; consulte ',
    workflowCardSubHeading: 'Flujo de trabajo de la función',
    workflowCardMessage: 'Gestione quién puede solicitar, aprobar y tomar otras medidas en relación con las funciones mediante los ajustes del flujo de trabajo.',
    workflowTurnedOff: 'Active "Flujos de trabajo" en la pestaña General de su perfil de seguridad para acceder a esta página.',
    subRuleCreateRequest: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para que no pueda crear solicitudes.',
    subRuleRejectRequest: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para que no rechace las solicitudes.',
    subRuleAssignCriteriaRoles: 'Si se desactiva, se ocultarán las opciones de la interfaz y se bloqueará este perfil de seguridad para que no pueda asignar los recursos sugeridos a las funciones de criterio.',
    readRuleCondition: '¿Qué {entityName}s pueden ver?',
    createRuleCondition: '¿Qué {entityName}s pueden crear?',
    editRuleCondition: '¿Qué {entityName}s pueden editar?',
    deleteRuleCondition: '¿Qué {entityName}s pueden borrar?',
    securityProfileNameRequiredMsg: 'Proporcione un nombre de perfil de seguridad',
    uniqueSecurityProfileNameMsg: 'Escriba un nombre de perfil de seguridad',
    delete_failureWarningTitle: 'No se puede eliminar el perfil de seguridad',
    delete_failureWarningMessage: 'No se puede eliminar el perfil de seguridad de {profileName}. Este perfil de seguridad está en uso.',
    delete_failureButtonLabel: 'Aceptar',
    skillReadRuleCondition: '¿Qué habilidades y certificaciones puede el usuario añadir a su propio perfil?',
    fieldSecurityHeading: 'Campos de {entityName}',
    fieldSecuritySubHeading: 'Establezca con qué campos de {entityName} puede interactuar este perfil de seguridad. Por defecto, los campos son editables, pero también pueden configurarse como de solo lectura u ocultos.',
    fieldSecurityInfoForCondition: 'Para',
    fieldSecurityInfoThisFieldIsCondition: 'Este campo es',
    fieldSecurityInfoOtherwiseCondition: 'De lo contrario, el campo es',
    mandatoryNotification: ' Es un campo obligatorio. Restringir un campo obligatorio puede tener resultados inesperados.',
    editAccessNotification: 'Se ha desactivado el acceso de edición para {entityName}s. La selección de \'Editable\' no tendrá ningún efecto sobre el acceso al campo.',
    readOnlyNotification: 'Este es un campo de solo lectura del sistema y no puede hacerse editable',
    externalIdNotificationMessage: 'Este campo es editable solo a través del Portal API',
    note: 'Nota',
    important: 'Importante',
    emptyFieldSecurityViewMessage: 'Todavía no se ha añadido ningún campo',
    accessLevel: 'Nivel de acceso',
    tabMessage: 'Ficha {tabName}',
    skillCategoriesLabel: 'Categorías de habilidad ',
    departmentLabel: 'Departamentos ',
    divisionLabel: 'Division ',
    skillEntityTypeLabel: 'Tipos de habilidad ',
    serviceLineLabel: 'Líneas de servicio ',
    skillsCertificationLabel: 'Habilidades y certificacion ',
    viewbudgetEntity: 'Permitir a los usuarios ver los valores presupuestarios estimados de los roles y los valores reales una vez asignados los recursos',
    managerApprovalAlert: 'ES_Users must have a manager set in \'Reports to\' field or they will not be able to update their skills_ES',
    managerApprovalSubHeading: 'ES_For users with this security profile any changes they make to their own skills will need to be approved by their manager. Skill preferences can be changed without approval_ES',
    customConditions: {
        operators: {
            Int: {
                LessThan: 'Inferior a',
                LessThanOrEqual: 'Inferior o igual a',
                Equals: 'Igual a',
                GreaterThanOrEqual: 'Superior o igual a',
                GreaterThan: 'Superior a',
                NOT_EQUALS_OPERATOR: 'No es igual a'
            },
            DateTime: {
                LessThanOrEqual: 'Antes',
                GreaterThanOrEqual: 'Después'
            },
            ID: {
                IN_OPERATOR: 'Es uno de',
                NOT_IN_OPERATOR: 'No es uno de'
            },
            Bool: {
                Equals: 'Igual a'
            }
        },
        valueTypes: {
            relativeToToday: 'Referente a hoy',
            blank: 'Vacío',
            selectedValues: 'Valores seleccionados',
            loggedInUserValue: 'Valor de usuario que ha iniciado sesión',
            existingValue: 'Valor existente',
            customValue: 'Valor personalizado'
        },
        relativeDateValues: {
            PLUS_180: 'Hoy +180 días',
            PLUS_90: 'Hoy +90 días',
            PLUS_28: 'Hoy +28 días',
            PLUS_7: 'Hoy +7 días',
            PLUS_1: 'Mañana',
            TODAY: 'Hoy',
            MINUS_1: 'Ayer',
            MINUS_7: 'Hoy -7 días',
            MINUS_28: 'Hoy -28 días',
            MINUS_90: 'Hoy -90 días',
            MINUS_180: 'Hoy -180 días'
        },
        noConditionOperatorError: 'Seleccione el operador',
        noConditionValueTypeError: 'Seleccione el tipo de valor',
        addConditionsListButtonLabel: '+ Añadir condición AND',
        andOperatorLabel: 'AND',
        maxConditionsCountLabel: ' 3 máximo',
        addConditionRowButtonLabel: '+ Añadir condición OR',
        orOperatorLabel: 'OR',
        fieldHeaderLabel: 'Campo',
        operatorHeaderLabel: 'Operador',
        valueTypeHeaderLabel: 'Tipo de valor',
        valueHeaderLabel: 'Valor',
        noResultsFoundMessage: 'No se encontraron resultados',
        pleaseEnterFieldLabel: 'Escriba un campo',
        conditionFieldNamePlaceholder: 'Seleccione un campo...',
        yesLabel: 'Sí',
        noLabel: 'No',
        wholeNumberInputError: 'Escriba un valor válido',
        commonPredefinedConditionsLabel: 'Use una condición predefinida',
        inheritReadPredefinedConditionsLabel: 'Use solo las condiciones de lectura establecidas para el trabajo y el recurso',
        commonCustomConditionLabel: 'Cree una condición personalizada',
        inheritReadCustomConditionLabel: 'Añada un condición personalizada además de las condiciones de lectura establecidas para el trabajo y el recurso',
        addAnotherPrefix: 'Añadir',
        deleteRowLabel: 'Borrar fila',
        pleaseEnterValueLabel: 'Escriba un valor',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'con este nombre.',
        jsonConditionLabel: 'Especifique la regla JSON personalizada',
        jsonConditionWarningBannerText: 'Las reglas JSON personalizadas deben utilizarse con  precaución ya que pueden afectar al rendimiento.\nLa mala configuración de las reglas JSON puede provocar problemas de inestabilidad.',
        invalidJsonErrorMessage: 'Asegúrese de que la JSON sea válida.',
        apiPortalInfoText: 'Validar reglas JSON automáticamente al hacer clic fuera del área de texto ',
        apiPortalLabel: 'portal API.',
        inheritReadJsonConditionLabel: 'Especifique una regla JSON personalizada sobre las condiciones de lectura establecidas para el trabajo y el recurso.',
        //Skills
        addSkillHeader: 'Añadir habilidades y certificaciones a la página del perfil ',
        allSkillsLabel: 'Todas las habilidades y certificacion ',
        onlyTheseSkillsLabel: 'Solo estas ',
        onlyRelatedSkillsLabel: 'Solo las habilidades y certificaciones relacionadas ',
        emptySkillErrorMessage: 'Seleccione al menos un valor ',
        emptyJsonConditionErrorMessage: 'Seleccione al menos una de las opcion '
    },
    //entitiesConfiguration
    planningDataAliasUseCaseInfo: 'Un alias es un término más familiar que puede utilizarse en toda su aplicación. Añada alias para que los términos desconocidos sean más reconocibles.',
    aliasHeading: 'Alias',
    singularAliasLabel: 'Alias singular',
    pluralAliasLabel: 'Alias plural',
    BookingDescription: 'Una reserva representa la asignación de un recurso a un trabajo para un rango de fechas específico y tiene un número de horas medible. Se pueden crear reservas para trabajos como la gestión de proyectos o las pruebas, y también para trabajos como las vacaciones o las bajas por enfermedad. Se debe realizar cada reserva con relación a un trabajo.',
    JobDescription: 'Un trabajo es un proyecto, tarea o labor que requiere un recurso. Se puede crear un puesto de trabajo para actividades como la gestión de proyectos o las pruebas, y también para actividades como las vacaciones o las bajas por enfermedad.',
    ResourceDescription: 'Los recursos se refieren a los materiales, el personal u otros activos que pueden ser utilizados por una organización para funcionar eficazmente. Cualquier elemento con una capacidad finita que pueda realizar un trabajo puede considerarse un recurso. El personal, las salas de formación y la maquinaria son ejemplos de recursos.',
    ClientDescription: 'El término "clientes" se refiere a los clientes de su organización. Un cliente puede tener varios trabajos.',
    rolerequestgroupDescription: 'Escenario_ es una colección de funciones para un trabajo determinado.',
    rolerequestDescription: 'Una función es un precursor de una o más reservas. Pueden crearse con un recurso en mente o con determinados criterios.',
    DepartmentDescription: 'Un departamento es un segmento de una organización especializado en un proceso empresarial concreto, a menudo formado por recursos con conjuntos de habilidades y responsabilidades similares',
    DivisionDescription: 'Una división es una unidad de negocio de alto nivel o un segmento de una organización y estará formada por múltiples departamentos',
    //Skill Types Message
    //Levels
    skillPageHeader: 'Habilidades y certificacion',
    skillTypeLevelsTabTitle: 'Niveles',
    skillTypeSkillsTabTitle: 'Habilidades',
    fieldsTabTitle: 'Campos',
    addSkill: 'Añadir una habilidad',
    addRetainSkillLibrary: 'Añadir desde la biblioteca de Retain ',
    levelNameDescriptionText: 'Los niveles para este tipo de habilidad se conocen como',
    levelNameInfoText: '{levelName} 1 es el nivel más bajo.',
    addLevels: 'Añadir {levelName}',
    skillTypeLevelNameRequiredMessage: 'Escriba un nombre de nivel',
    whiteSpaceValidation: 'En la opción se permiten todos los elementos menos los espacios en blanco',
    skillLevelRequiredValidation: 'El nombre del nivel es obligatorio',
    skillLevelUniqueValidation: 'El nombre de nivel ya existe',
    skillLevelNamePlaceholder: 'Nombre del nivel',
    skillLevelDescriptionPlaceholder: 'Descripción del nivel',
    //Skill fields
    skillFieldHeading: 'Campos adicionales para este tipo de habilidad',
    addFieldButtonLabel: 'Añadir un campo',
    skillCommandBarFieldName: 'Editar el nombre de la habilidad',
    //reusable grid
    cancelDeletion: 'Cancelar borrado',
    markedForDeletion: 'Marcado para borrarlo. Se borrará cuando confirme los cambios. ',
    addButtonReusableGrid: 'Añadir una fila',
    mandatory: 'Obligatorio',
    markDeleteWarningTitle: '¿Confirma que desea marcar "{recordName}" para su borrado?',
    markDeleteWarningTitleDefault: '¿Está seguro de que quiere marcar este registro para su borrado?',
    //Field Properties
    fieldFormattingDisabledTooltipText: 'No puede cambiarse en los campos integrados',
    fieldPropetiesAliasDefinition: 'Un alias es un término más familiar que puede utilizarse en toda su aplicación.',
    fieldPropetiesChooseAliasMessage: 'Elija un alias para la tabla seleccionada y escriba etiquetas para los nombres de los campos que puedan utilizarse en toda la aplicación.',
    noDescriptionMsg: 'Haga clic aquí para añadir una descripción',
    configurePageField: 'Campos de {pageTitle}',
    descriptionText: 'Descripción',
    builtInTabsTitle: 'Campos integrados',
    customFieldTabsTitle: 'Campos personalizados',
    builtInTabsDescription: 'Estos son los campos de {entity} integrados por defecto al sistema. Estos no se pueden cambiar, pero se pueden ocultar si no los necesita.',
    customFieldTabsDesc: 'Son campos que usted ha añadido a la aplicación. Aparecerán junto a los campos {entity} del sistema.',
    builtInFieldTab: 'Integrados',
    customText: 'Personalizado',
    customFieldAddButton: 'Añadir campo personalizado',
    emptyCustomViewMessage: 'Todavía no se ha añadido ningún campo personalizado',
    emptyBuiltInViewMessage: 'Todavía no se ha añadido ningún campo integrado',
    fieldLabelReqdValidation: 'La etiqueta del campo no puede estar vacía',
    uniqueFieldNameValidation: 'El valor ${label} debe ser único ',
    bracketsFieldNameValidation: 'El valor ${label} no puede tener [ ni ] ',
    lookupFieldInputRequired: 'El campo de búsqueda no puede estar vacío',
    noMatchesText: 'No hay resultados',
    lookUpField: 'Valores desde',
    lookUpLinkText: 'Configuración de los campos de búsqueda',
    maxCharMessage: 'Máximo de {maxChar} caracteres',
    maxCharTagMessage: 'El límite solo se aplica a las etiquetas definidas por el usuario. Máx. {maxChar}',
    incorrectDecimalFormatMsg: 'Formato decimal incorrecto (escriba el valor numérico con un máximo de 10 decimales)',
    incorrectTargetBillabilityMessage: 'El valor mínimo permitido es 0',
    inputBeyondLimitMsg: 'El valor de entrada sobrepasa el límite',
    noOfChars: 'Número de caracteres',
    typeText: 'Tipo',
    labelText: 'Etiqueta',
    hiddenText: 'Oculto',
    decimalPlacesText: 'Decimales',
    exampleText: 'Ejemplo',
    lookupValuesLabel: 'Valores de búsqueda',
    newFieldValuesLabel: 'Nombre de la nueva lista',
    nextLabel: 'Siguiente',
    prevLabel: 'Anterior',
    fieldValuesTitle: 'Valores de campo',
    fieldValuesSubTitle: 'Especifique la lista de valores para este campo',
    lookupValuesNotifyMessage: 'No se puede cambiar después de crear el campo.{lineBreak} Los valores de la lista se pueden editar posteriormente en la página \'Valores\'.',
    newFieldNameRequiredMessage: 'El nombre del valor del campo es obligatorio',
    fieldAliasRequiredValidation: 'El alias {fieldAlias} es obligatorio',
    fieldDescriptionRequiredValidation: 'La descripción {fieldDescription} es obligatoria',
    newFieldValuesRequiredMessage: 'La lista de valores del campo no puede estar vacía',
    multiSelectText: 'Multiselección',
    valuesListLabel: 'Lista de valores',
    useExistingValuesText: 'Utilizar una lista de valores existente',
    createNewValuesListText: 'Crear una nueva lista de valores',
    createNewValuesSaveInfo: 'La nueva lista se guardará en la página de \'Valores\'',
    planningDataSaveInfo: 'No se puede cambiar después de crear el campo',
    formattingLabel: 'Aplicando formato',
    minimumLabel: 'Mín.',
    maximumLabel: 'Máx.',
    valuesText: 'Valores',
    valueFieldsCommandBarFieldName: 'Editar el nombre del valor del campo',
    calculatedMessage: 'Este campo se calcula automáticamente a partir de los datos de su aplicación.',
    rangeLabel: 'Rango',
    systemReadonlyMessage: 'Este campo es de solo lectura y contiene información actualizada por la aplicación',
    systemRequiredMessage: 'El sistema requiere este campo para crear {entity}',
    fieldNameLabel: 'Nombre del campo',
    deletefieldsHeading: 'Borrar campos',
    keepFieldsHeading: 'Conservar campos',
    newText: 'Nuevo',
    fieldText: 'campo',
    warningMessageWithFieldNames: 'Está a punto de borrar el {fieldsArray} ',
    warningMessageMoreFields: 'y {number} campos más. La eliminación de estos campos borrará cualquier dato que ya se encuentre en ellos. ',
    warningMessageLessFields: 'campos. La eliminación de estos campos borrará los datos que ya contengan.',
    deletefieldsHeadingConfirmation: 'Borrar los campos y sus datos',
    deleteSkillsButtonLabel: 'Borrar habilidades',
    keepSelectedSkillsLabel: 'Conservar las habilidades seleccionadas',
    fieldPropertiesLicenseWarning: 'Está cerca del límite que permite su licencia. {contactUs} para aumentar el límite de su plan.',
    fieldPropertieslicenseError: 'Ha alcanzado el límite que permite su licencia. {contactUs} para aumentar el límite de su plan.',
    showLookupLink: 'Mostrar valores de búsqueda',
    lookupValueUniqueValidation: 'Este valor ya se ha añadido',
    pressEnterMessage: 'Pulse Intro para añadir el valor',
    defaultValueLabel: 'Valor predeterminado',
    noDefaultValueStaticHeaderText: 'Sin valor predeterminado',
    multipleDefaultValueStaticHeaderText: 'Múltiples valores',
    noDefaultPlaceHolder: 'Sin valor predeterminado',
    invalidDefaultValueValidation: 'Valor predeterminado no válido',
    defaultValueSelectOwnValueText: 'Seleccione el valor o déjelo en blanco',
    defaultValueInheritValueText: 'Heredar el valor del usuario conectado',
    defaultValueInheritSummaryText: 'Heredar del usuario',
    defaultValueFieldText: 'Se heredará {fieldName} del usuario',
    defaultValueFieldDisabledText: 'La nueva lista de valores de búsqueda estará disponible para establecerla como predeterminada una vez que guarde este campo',
    inheritUserField: '"{fieldName}&quot del usuario;',
    yesMsg: 'Sí',
    noMsg: 'No',
    //Field look up values config
    fieldLookupValueAddButton: 'Añadir un valor',
    noResultsText: 'Sin resultados',
    fieldLookupValuesDescription: 'Añadir, editar o eliminar los valores que se muestran en las listas. Por ejemplo, una lista para Ubicación puede mostrar valores como Londres, París y Nueva York.',
    fieldLookupValuesEntityData: 'Campos que actualmente utilizan estos valores',
    saveFieldLookupValueAlert: '¿Borrar los valores seleccionados?',
    saveFieldLookupValueWarning: 'Al eliminar los valores, se eliminarán de los menús desplegables de búsqueda y de todas las demás áreas donde se muestran estos valores.',
    fieldLookupCheckMessage: 'Ha seleccionado {deleteCount} valores para eliminar.',
    yesDeleteValuesButtonLabel: 'Sí, elimine los valores',
    noKeepValuesButtonLabel: 'No, conservar los valores',
    optionIsRequiredMessage: 'Esta opción es obligatoria',
    optionAlreadyExist: 'La opción ya existe',
    uniqueValueNameMessage: 'Escriba un nombre de valor único',
    requiredValueNameMessage: 'Proporcione un nombre de valor',
    fieldLookupValueInUseTitle: 'No se pudieron borrar las listas de valores',
    fieldLookupValueInUseMessage: 'Las listas de valores que está utilizando un campo no pueden ser eliminadas.',
    fieldLookupValueInUseSubMessage: 'Debe eliminar los campos asociados antes de borrar las listas de valores.',
    fieldLookupValuesSortText: 'Orden de los valores',
    fieldLookupValuesDefaultSortModeText: 'Alfabético (A-Z)',
    fieldLookupValuesCustomSortModeText: 'Incremento (orden personalizado)',
    fieldLookupValuesSortModeDescriptionText: 'Los menús y las listas siguen este orden al mostrar estos valores',
    fieldLookupValuesCustomSortGridText: 'Coloque el valor menos significativo primero en la lista',
    noneText: 'ninguno',
    typeNewValue: 'Escriba un nuevo valor',
    //System Settings
    fieldPropertiesPageHeader: 'Campos',
    fieldLookupValuesPageHeader: 'Valores',
    gridColoumnTitle_Option: 'Opción',
    gridColoumnTitle_Target_Billability: 'Uso facturable',
    //PageNames
    pageNameHeader: 'Nombres de página',
    pageNamesHeaderSummary: 'Seleccione las páginas y los nombres de visualización que se mostrarán en el menú de la izquierda.',
    menuListTitle: 'Lista de menús',
    settingValMsg: 'Escriba el valor del ajuste',
    maxSizeFieldMsg: 'El tamaño máximo del campo puede ser de 40 caracteres',
    displayText: 'Mostrar',
    enableText: 'Habilitar',
    summaryPageNameText: 'Logotipo: sin etiqueta visible',
    //dynamic generated child sections from setting api (System settings)
    Talent_Profile: 'Perfil del talento',
    Jobs: 'Trabajos',
    Scheduler: 'Programador',
    Timesheets: 'Plantilla horaria',
    Report: 'Informe',
    Role_inbox: 'Bandeja de entrada de funciones',
    Rolboard: 'Tablón de funciones',
    Table_View: 'Vista de tabla',
    //Currencies
    currenciesPageHeader: 'Monedas',
    //charge codes
    chargeTypePageHeader: 'Tipo de gasto',
    whiteSpaceValidationChargeType: 'En el tipo de cargo se permiten todos los elementos menos los espacios en blanco',
    chargeCodeContent: 'Los tipos de cargos le permiten crear diferentes tarifas para distintos tipos de trabajos. Cree varios tipos de cargos para cubrir los diferentes tipos de trabajo realizados en su empresa.',
    chargeRateContent: 'Añadir, editar o eliminar las tarifas de costes e ingresos para diferentes códigos de cargos en rangos de tiempo específicos {ChargeRateLinkText}',
    chargeRateRedirection: 'aquí.',
    chargeCodeUsageWarning: 'Este tipo de cargo está siendo <strong></strong>utilizado por {chargeTypeJobCount} trabajos.',
    chargeCodeWarning: 'Si borra este tipo de cargo, se eliminará de todos los registros y no podrá utilizarse para la información presupuestaria y los informes.',
    chargeTypeDeletionWarning: 'Borrar el tipo de cargo {chargeTypeName}',
    deleteChargeTypePrimaryButton: 'Borrar tipo de cargo',
    deleteChargeTypeSecondaryButton: 'Conservar tipo de cargo',
    chargeCodeCheckMessage: 'Entiendo el impacto de borrar este tipo de carga',
    chargeCodeUniqueValidation: 'Escriba un tipo de cargo único',
    chargeCodeReqdValidation: 'El tipo de cargo es obligatorio',
    chargeCodeAddButton: 'Añadir un tipo de cargo',
    noChargeCodeAvailable: 'No hay ningún tipo de cargo disponible',
    waterMarkChargeCode: 'Tipo de cargo sin nombre',
    waterMarkDescription: 'Descripción del tipo de cargo',
    chargeTypeHeader: 'Tipo de cargo',

    //charge rates
    chargeRatePageHeader: 'Tasas del gasto',
    hourlyChargeRatesHeading: 'Tarifas de cargo por hora y por tipo de trabajo',
    waterMarkRevenue: 'Ingresos de la tasa de cargo',
    waterMarkCost: 'Coste de la tasa de cargo',
    chargeRateDeleteMessage: 'Eliminará esta tasa de cargo para cualquier uso futuro.',
    chargeRateDeleteSubMessage: 'Esta tasa de cargo se mantendrá en las reservas existentes.',
    cantUndoMessage: 'Esta acción no se puede deshacer.',
    checkMessage: 'Entiendo las consecuencias de borrar esto',
    deleteChargeRateTitle: 'Borrar "{deletedChargeRate}" tasa de cargo?',
    deleteChargeCodeSpecificChargeRateTitle: 'Borrar "{deletedChargeCodeName}" tasa de cargo de "{deletedChargeRate}"?',
    deleteChargeRateHeading: 'Esta tasa de cargo la están utilizando actualmente los recursos de {chargeRateResourceCount}.',
    deleteChargeRateWarningMessage: 'Si la borra, se eliminará de todos los registros y no podrá utilizarse para la información presupuestaria y los informes.',
    deleteChargeRateCheckboxMessage: 'Entiendo el impacto de borrar esta tasa',
    deleteChargeRateButtonLabel: 'Borrar tasa de cargo',
    keepChargeRateButtonLabel: 'Conservar la tasa de cargo',
    editChargeRateTitle: 'Modificar la tasa de cargo"{editedChargeRateName}" actual?',
    editChargeRateHeading: 'Esta es la tasa de cargo actual para el tipo de cargo "{editedChargeRateName}".',
    editChargeRateSubHeading: 'Si modifica esta tasa de cargo, la modificará para todos los cálculos e informes de este periodo.',
    editChargeRateWarningMessage: 'Confirma que desea cambiar la tasa de cargo actual para "{editedChargeRateName}"?',
    editChargeRatePrimaryButton: 'Modificar la tasa activa',
    addCostRevenueButtonLabel: 'Añadir costes e ingresos',
    customGridRangeOverlappingMessage: 'Ya hay un coste y unos ingresos definidos para las fechas de este rango',
    customGridRangeRequiredMessage: 'Proporcione un rango de fechas',
    customGridRevenueRequiredMessage: 'Proporcione los ingresos',
    customGridCostRequiredMessage: 'Proporcione los costes',
    chargeRateNameField: 'Nombre de la tasa de cargo',
    chargeRateTitleRequiredMessage: 'Proporcione el nombre de la tasa de cargo',
    chargeRateTitleUniqueMessage: 'Escriba un nombre único de tasa de cargo',
    customGridNegativeMessage: 'Este valor no puede ser negativo',
    dateRangeLabel: 'Rango de fechas',
    revenueLabel: 'Ingresos',
    costLabel: 'Coste',
    //skills
    noRecommendations: 'No hay nuevas recomendacion ',
    addSkillsHeading: 'Añadir habilidades para este tipo de habilidad',
    activeSkillTitle: '\'{activeSkillConfiguration}\' es una de las secciones de habilidades que aparecen en el perfil de talento de un usuario.',
    skillHeaderText: 'Habilidades de {activeSkillConfiguration}',
    skillHeaderDescription: 'Los usuarios podrán elegir las siguientes habilidades en la sección \'{activeSkillConfiguration}\' de sus perfiles.',
    skillImportMessage: 'Para las actualizaciones masivas, es posible que prefiera utilizar la función {Import}',
    addSkillButtonFromCommandBar: 'Añadir una habilidad',
    skillNameLabel: 'Nombre de la habilidad',
    skillType: 'Tipo',
    addSkillCategoryText: 'Añadir categoría',
    skillCategory: 'Categoría',
    skillSubCategory: 'Subcategoría',
    tagsLabel: 'Etiquetas',
    skillTagExplaination: 'Las etiquetas se pueden asignar a las competencias. Las etiquetas facilitan la búsqueda de una competencia, sobre todo cuando esta se conoce por otro nombre. Pulse Intro después de añadir cada etiqueta y antes de guardarla.',
    skillNameRequired: 'Escriba el nombre de la habilidad',
    uniqueSkillNameRequired: 'Escriba un nombre de habilidad único',
    uniqueSkillItem: 'El nombre de la habilidad debe ser único',
    deleteSkillFieldsPopupHeading: '¿Borrar los campos seleccionados?',
    deleteSkillFieldsPopupWarningMessage: '¿Desea eliminar permanentemente los campos de habilidad seleccionados? Se borrarán {number} campos de habilidad de este tipo de habilidad y se quitarán de los perfiles de los recursos, así como de cualquier otra área en la que se muestre el campo de habilidad.',
    deleteSkillFieldsButtonLabel: 'Borrar campos de habilidad',
    keepSkillFieldsButtonLabel: 'Conservar campos de habilidad',
    deleteSkillLevelsPopupHeading: '¿Borrar los niveles seleccionados?',
    deleteSkillLevelsPopupWarningMessage: '¿Desea eliminar permanentemente los niveles de habilidad seleccionados? Los niveles de habilidad borrados se eliminarán de los perfiles de los recursos y de cualquier otra área donde se muestren estos niveles de habilidad.',
    deleteSkillLevelsButtonLabel: 'Borrar niveles de habilidad',
    keepSkillLevelsButtonLabel: 'Conservar niveles de habilidad',
    deleteSkillsPopupHeading: '¿Borrar las habilidades seleccionadas?',
    deleteSkillsPopupWarningMessage: '¿Desea eliminar permanentemente las habilidades seleccionadas? Se borrarán {number} habilidades del sistema y se eliminarán de los perfiles de los usuarios y de cualquier otra área donde se muestren las habilidades.',
    keepSkillsButtonLabel: 'Conservar habilidades',
    deleteSkillsAndLevelsPopupHeading: '¿Borrar las habilidades y los niveles de habilidad seleccionados?',
    deleteSkillsAndLevelsPopupWarningMessage: '¿Desea eliminar permanentemente las habilidades y niveles de habilidad seleccionados?',
    deleteSkillsAndFieldsPopupHeading: '¿Borrar las habilidades y los campos de habilidad seleccionados?',
    deleteSkillsAndFieldsPopupWarningMessage: '¿Desea eliminar permanentemente las habilidades y los campos de habilidad seleccionados?',
    deleteLevelsAndFieldsPopupHeading: '¿Borrar los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteLevelsAndFieldsPopupWarningMessage: '¿Desea eliminar permanentemente los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteSkillsLevelsAndFieldsPopupHeading: '¿Borrar las habilidades, los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteSkillsLevelsAndFieldsPopupWarningMessage: '¿Desea eliminar permanentemente las habilidades, los niveles de habilidad y los campos de habilidad seleccionados?',
    deletePopupWarningMessage: 'Los elementos se borrarán de este tipo de habilidad y se eliminarán de los perfiles de los recursos y de cualquier otra área donde se muestren.',
    deleteSkillsPopupConfirmation: 'Entiendo las consecuencias de esta acción.',
    deleteItemsButtonLabel: 'Eliminar estos elementos',
    keepItemsButtonLabel: 'Conservar estos elementos',
    deleteAndModifySkillFieldsPopupHeading: '¿Guardar los cambios y borrar los campos seleccionados?',
    deleteAndModifySkillFieldsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente los campos de habilidad seleccionados? Se borrarán {number} campos de habilidad de este tipo de habilidad y se quitarán de los perfiles de los recursos, así como de cualquier otra área en la que se muestre el campo de habilidad.',
    deleteAndModifySkillLevelsPopupHeading: '¿Guardar los cambios y eliminar los niveles seleccionados?',
    deleteAndModifySkillLevelsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente los niveles de habilidad seleccionados? Los niveles de habilidad borrados se eliminarán de los perfiles de los recursos y de cualquier otra área donde se muestren estos niveles de habilidad.',
    deleteAndModifySkillsPopupHeading: '¿Guardar los cambios y eliminar las habilidades seleccionadas?',
    deleteAndModifySkillsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente las habilidades seleccionadas? Se borrarán {number} habilidades del sistema y se eliminarán de los perfiles de los usuarios y de cualquier otra área donde se muestren las habilidades.',
    deleteAndModifySkillsAndLevelsPopupHeading: '¿Guardar los cambios y borrar las habilidades y niveles de habilidad seleccionados?',
    deleteAndModifySkillsAndLevelsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente las habilidades y niveles de habilidad seleccionados?',
    deleteAndModifySkillsAndFieldsPopupHeading: '¿Guardar los cambios y eliminar las habilidades y los campos de habilidad seleccionados?',
    deleteAndModifySkillsAndFieldsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente las habilidades y los campos de habilidad seleccionados?',
    deleteAndModifyLevelsAndFieldsPopupHeading: '¿Guardar los cambios y borrar los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteAndModifyLevelsAndFieldsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteAndModifySkillsLevelsAndFieldsPopupHeading: '¿Guardar los cambios y borrar las habilidades, los niveles de habilidad y los campos de habilidad seleccionados?',
    deleteAndModifySkillsLevelsAndFieldsPopupWarningMessage: '¿Desea guardar los cambios y eliminar permanentemente las habilidades, los niveles de habilidad y los campos de habilidad seleccionados?',
    skillCategoryDeletePopupHeading: '¿Borrar tipo de habilidad?',
    skillCategoryDeletePopupWarningMessage: '¿Desea eliminar este tipo de categoría de habilidad? Esta categoría de habilidad contiene {number} habilidades. Todas ellas se desvincularán, y los niveles y campos asociados a esta categoría se eliminarán del sistema, así como todos los perfiles que hagan referencia a ella. ',
    deleteSkillCategoryButtonLabel: 'Borrar tipo de habilidad',
    keepSkillCategory: 'Conservar el tipo de habilidad',
    fieldAlreadyExistHeader: 'Escriba un nombre de campo único',
    fieldAlreadyExistDescription: 'Este nombre de campo ya existe. Utilice el campo existente o cambie el nombre de este campo para que sea único.',
    skillNamePlaceholder: 'Escriba el nombre de la habilidad',
    skillDescriptionPlaceholder: 'Escriba la descripción de la habilidad',
    skillNameHeader: 'Nombre de la habilidad',
    fieldTypeHeader: 'Tipo de campo',
    categoryText: 'Categoría',
    defaultValueText: 'Valor predeterminado',
    newTag: 'Nueva etiqueta',
    headerLevels: 'Niveles',
    licenseCountSkill: 'Se utilizan {activeCount} de {licensedCount} habilidades para este tipo de habilidad',
    skillsLicenseWarning: 'Está cerca del límite permitido por su licencia para las habilidades. {contactUs} para aumentar el límite de su plan. ',
    skillsLicenseError: 'Ha alcanzado el límite que permite su licencia para las habilidades. {contactUs} para aumentar el límite de su plan. ',
    licenseCountSkillLevel: 'Se utilizan {activeCount} de {licensedCount} niveles para este tipo de habilidad',
    skillLevelsLicenseWarning: 'Está cerca del límite permitido por su licencia para los niveles en este tipo de habilidad. {contactUs} para aumentar el límite de su plan.',
    skillLevelsLicenseError: 'Ha alcanzado el límite de niveles permitidos por su licencia en este tipo de habilidad. {contactUs} para aumentar el límite de su plan.',
    licenseCountSkillField: 'Se utilizan {activeCount} de {licensedCount} campos para este tipo de habilidad',
    skillFieldsLicenseWarning: 'Está cerca del límite permitido por su licencia para los campos de este tipo de habilidad. {contactUs} para aumentar el límite de su plan.',
    skillFieldsLicenseError: 'Ha alcanzado el límite permitido por su licencia para los campos de este tipo de habilidad. {contactUs} para aumentar el límite de su plan.',
    adminCommandBarActionLabel: 'Acciones',
    adminCommandBarEditLabel: 'Editar',
    adminCommandBarUserStatusLabel: 'Usuario activo',
    adminCommandBarSetActiveLabel: 'Ajustar como activo',
    adminCommandBarSetInactiveLabel: 'Ajustar como inactivo',
    adminCommandBarResendEmailInviteLabel: 'Enviar correo electrónico de invitación',
    adminCommandBarResetPassphraseLabel: 'Restablecer contraseña',
    disabledSkillType: 'Guarde o descarte los cambios antes de continuar',
    adminCommandBarSendCMeSurveyLabel: 'Enviar cuestionario de C-me',
    // Skill Category
    skillCategoryRequired: 'Escriba el nombre de la categoría',
    uniqueCategoryNameRequired: 'Introduzca un nombre de categoría',
    primarySkillLabel: 'Habilidad principal',
    secondarySkillLabel: 'Habilidad secundaria',
    preferredSkillLabel: 'Habilidad preferida',
    skillCategoryLabel: 'Categoría de habilidad',
    importInProgressText: 'Se están importando las habilidades desde la biblioteca de Retain',
    subscribed: 'Con suscripción',
    notSubscribed: 'Sin suscripción',
    selectedImportDataLabel: '<bold>${skillCount} habilidades elegidas de entre ${categoryCount} categorías</bold>',
    skillCategoryExpiryMandatoryInfo: 'Establezca esta opción a "Sí" para aplicar la caducidad de la habilidad como campo obligatorio',
    skillExpiryEnabledInfo: 'ES_Set this to Yes to enable Skill expiry fields for this skill category_ES',
    importClickInfo: 'Haga clic en <bold>Importar</bold> para poner en cola la tarea de importación',
    skillCategorySubscribeInfo: 'Suscríbase para recibir notificaciones sobre las actualizaciones de esta categoría de habilidad',
    importProgressInfo: 'Compruebe el <bold>registro de operaciones</bold> para ver el progreso de esta tarea',
    skillExpiryDateLabel: 'ES_Skill expiry is mandatory_ES',
    skillExpiryEnabledLabel: 'ES_Enable skill expiry dates_ES',
    preNotificationLabel: 'Notificación previa a la caducidad',
    watcherLabel: 'Supervisor de categorías de habilidad',
    existingSkillsHeader: 'Se ocultarán de esta lista las habilidades que ya se hayan añadido antes o que tengan el mismo nombre que las habilidades ya introducidas en la organización',

    //skill Library
    addSkillLibrary: 'Añadir desde la biblioteca de Retain',
    skillLibraryHeader: 'Biblioteca de habilidades de Retain',
    importLibraryStep1: {
        title: 'Elegir habilidad',
        description: 'Seleccione las habilidades que desea importar'
    },
    importLibraryStep2: {
        title: 'Revisar',
        description: 'Revise las habilidades seleccionadas'
    },
    importLibraryStep3: {
        title: 'Importar',
        description: 'Todo listo para importar'
    },
    searchSkillPlaceholder:'ES_Search skill_ES',
    //Notifications
    notificationsPage: {
        commandBarConfig: {
            pageTitle: 'Notificaciones'
        },
        notificationSettingSummary: {
            notificationSettingSubHeading: 'Elija si desea mostrar las notificaciones por tipo.'
        },
        notificationSettingConfig: {
            onLabel: 'Activado',
            offLabel: 'Desactivado'
        },
        notificationEvents: {
            bookingAssignedLabel: '${editorName} lo ha asignado a ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation}',
            bookingUpdateLabel: 'Su reserva en ${jobName} desde ${startDate} - ${endDate} ${timeAllocation} ha sido editada por ${editorName}',
            bookingDeleteLabel: 'Ya no está asignado a ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation}',
            roleRequestCreateLabel: '${resourceName} ha sido solicitado para ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation} por ${editorName}',
            roleRequestRejectLabel: 'Su solicitud de ${resourceName} para ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation} ha sido rechazada por ${editorName}',
            roleRequestLiveLabel: 'Su solicitud de ${resourceName} para ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation} ha sido puesta en marcha por ${editorName}',
            repeatBookingAssignedLabel: '${editorName} lo ha asignado a ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation}. Esta reserva se repite cada ${interval} hasta ${untilDate}',
            repeatBookingUpdateLabel: 'Su reserva en ${jobName} desde ${startDate} - ${endDate} ${timeAllocation} ha sido editada por ${editorName}. Esta reserva se repite cada ${interval} hasta ${untilDate}',
            repeatBookingDeleteLabel: 'Ya no está asignado a ${jobName} desde ${startDate} hasta ${endDate} ${timeAllocation}. Esta reserva se repitió cada ${interval} hasta ${untilDate}',
            loadingSectionSuffix: '% de horas trabajadas',
            timeSectionSuffix: 'horas reservadas en total',
            hoursPerDaySuffix: 'horas al día',
            resourceSkillExpiryLabel: 'ES_You have skills set to expire in ${expiryDay} days ⏳ Renew it before the expiry date to keep your credentials up to date_ES',
            resourceManagerSkillExpiryLabel: 'ES_${resourceName} has skills set to expire in ${expiryDay} days ⏳ Ensure they renew it before the expiry date to keep your credentials up to date_ES',
            resourceSkillRecommendationLabel: 'ES_Update your skills and get noticed! Here are some skills you could add to your profile_ES',
            skillExpiryLabel: 'ES_${skillName} ${entityType} expires on ${expiryDate}_ES',
            extraSkillExpiryLabel: 'ES_${count} more expiring soon_ES'
        },
        notificationActions: {
            viewBookingLabel: 'Ver ${bookingSingularLowerAlias}',
            editBookingLabel: 'Editar ${bookingSingularLowerAlias}',
            viewPlansLabel: 'Ver planes',
            viewRoleGroupLabel: 'Ver en ${entitySingularLower}',
            makeLiveLabel: 'Activar',
            markAsReadLabel: 'Marcar como leído',
            markAsUnreadLabel: 'Marcar como no leído',
            deleteLabel: 'Borrar'
        },
        notificationHistoryPageHeader: {
            informationMessage: 'Mostrar todas las notificaciones. Las notificaciones de lectura se eliminan automáticamente después de 30 días',
            markAllReadLabel: 'Marcar todo como leído',
            loadMoreButtonLabel: 'Mostrar más'
        },
        notificationTabs: {
            notificationHistoryLabel: 'Historial',
            notificationSettingsLabel: 'Ajustes'
        },
        notificationsSettings: {
            pageTitle: 'Notificaciones',
            notificationSettingsHeader: 'Notificaciones predeterminadas',
            notificationsSettingsAdminPageDescription: 'Establezca los ajustes de notificación predeterminados para el nuevo usuarios. Los usuarios pueden anular estos ajustes en "Notificaciones > Ajustes" en función de sus necesidades',
            notificationsSettingsDescription: 'Elija sobre qué desea que le envíen notificaciones y cómo',
            bookingNotifications: '${bookingSingularAlias} notificaciones',
            allNotifications: 'Todas las notificaciones',
            bookingPost: 'Cuando se le asigna a un/a ${bookingSingularAlias}',
            bookingPatch: 'Cuando se actualiza un/a  que se le ha asignado${bookingSingularAlias}',
            bookingDelete: 'Cuando se le ha desasignado de un/a  o se ha borrado${bookingSingularAlias}',
            emailFrequency: 'Frecuencia de los correos electrónicos',
            roleNotificaitons: '${rolerequestSingularAlias} notificaciones',
            roleRequestCreate: 'Cuando se solicita un/a  que usted gestiona${resourceSingularAlias}',
            roleRequestReject: 'Cuando  se rechaza un/a  que usted ha creado${rolerequestSingularAlias}',
            roleRequestLive: 'Cuando se publica un/a  que usted ha creado${rolerequestSingularAlias}',
            webApp: 'Aplicación web',
            email: 'Correo electrónico',
            globalNotificationSettingsHeaderTitle: 'Configuración de notificaciones global',
            globalNotificationSettingsHeaderDescription: 'Gestione los ajustes de las notificaciones para todos los usuarios',
            globalNotificationSettingsToggleTitle: 'Notifique a los usuarios acerca de las reservas \n\nno confirmadas',
            globalNotificationSettingsToggleDescription: 'Incluya las reservas no confirmadas en las notificaciones sobre las reservas',
            resourceSkillNotifications: 'ES_Skill notifications_ES',
            resourceSkillExpiry: 'ES_When a skill you have is expiring_ES',
            resourceManagerSkillExpiry: 'ES_When a resource you manage has a skill that\'s expiring_ES',
            resourceSkillRecommendation: 'ES_Recommend skills to add to your profile based on your job title or primary skill_ES'
        }
    },

    //Timesheets Page
    timesheetsPage: {
        noTimesheetsAvailable: 'No tiene ninguna hora registrada',
        jobLabel: 'Trabajo',
        monday: 'Lunes',
        tuesday: 'Martes',
        wednesday: 'Miércoles',
        thursday: 'Jueves',
        friday: 'Viernes',
        saturday: 'Sábado',
        sunday: 'Domingo',
        total: 'Total',
        hours_logged: 'horas registradas',
        commandBarConfig: {
            pageTitle: 'Hojas de horas'
        },
        hoursRequiredLabel: 'Este campo es obligatorio',
        maximumHourErrorLabel: 'Máximo de 24',
        minimumHourErrorLabel: 'Mínimo de 0',
        showTimesheetsLabel: 'Mostrar hoja de horas desde',
        submitButtonLabel: 'Guardar',
        cancelButtonLabel: 'Cancelar',
        timesheetDeletionWarning: '¿Eliminar el trabajo?',
        timesheetDataDeletionMessage: '¿Desea eliminar <bold>${jobDescription}</bold> y las horas asociadas de esta hoja de horas?',
        timesheetCheckMessage: 'Entiendo el impacto de borrar estos datos de la hoja de horas',
        deleteTimesheetPrimaryButton: 'Sí, eliminar el trabajo',
        deleteTimesheetSecondaryButton: 'No, conservar el trabajo',
        addJobButtonLabel: 'Añadir trabajo',
        applyJobButtonLabel: 'Añadir',
        hoursAriaLabel: 'Introducir horas'
    },

    //Reporting settings Page
    reportingTitle: 'Generación de informes',
    reportSettingPage: {
        datasetRefreshModalTitle: 'ES_Dataset refresh status_ES',
        lastRunLabel: 'ES_Last Run_ES',
        successStatus: 'ES_Dataset refresh completed successfully_ES',
        failureStatus: 'ES_Dataset refresh failed_ES',
        inProgressStatus: 'ES_Dataset refresh is in progress..._ES',
        cancelledStatus: 'ES_Dataset refresh was cancelled_ES',
        unknownStatus: 'ES_Unknown status_ES',
        datasetRefreshButton: 'ES_Refresh dataset_ES',
        cancelButton: 'ES_Cancel_ES'
    },

    //Report Page
    reportPage: {
        commandBarConfig: {
            pageTitle: 'Informes'
        },
        pageTitle: 'Informes',
        editLabel: 'Editar',
        viewLabel: 'Ver',
        printLabel: 'Imprimir',
        reportsIHaveCreatedLabel: 'Informes que he creado',
        sharedReportsItemLabel: 'Informes compartidos conmigo',
        manageMyReportsItemLabel: 'Gestionar mis informes…',
        reportNoEditPermissions: 'No tiene permisos suficientes para editar este informe',
        emptyReportTitle: 'No se han encontrado informes',
        emptyReportPageText: 'No hay informes que pueda ver',
        sharedReportsSectionTitle: 'Informes compartidos que puedo editar',
        reportDetailsTitle: 'Detalles del informe',
        systemInfoTitle: 'Información del sistema',
        updatedByLabel: 'Actualizado por',
        createdByLabel: 'Creado por',
        createdOnLabel: 'Creado el',
        updatedOnLabel: 'Actualizado el',
        nameLabel: 'Nombre',
        reportAccessLabel: 'Acceso al informe',
        emptyReportDetailsText: 'Empiece a crear informes y aparecerán en este modal.',
        modalHeaderTitle: 'Gestionar mis informes',
        newReportButtonLabel: 'Nuevo informe',
        newReportButtonTooltip: 'Se permite un máx. de ${licenseCount} informes por tenedor. Puede que no vea todos los informes si no tiene permiso de lectura.',
        deleteButtonLabel: 'Borrar informe seleccionado',
        saveChangesButtonLabel: 'Guardar los cambios',
        discardButtonLabel: 'Descartar los cambios',
        cancelButtonLabel: 'Cancelar',
        pendingDeleteButtonLabel: 'Borrando…',
        pendingSaveButtonLabel: 'Guardando…',
        unsavedChangesLabel: 'Cambios sin guardar',
        youHaveUnsavedChangesLine: 'Hay cambios sin guardar en este informe. ¿Desea salir?',
        allProfilesLabel: 'Todos los perfiles',
        readOnlyLabel: 'Solo lectura',
        editAccessLabel: 'Editar acceso',
        helpTextLabel: 'Los creadores siempre pueden ver y editar sus informes',
        maxLimitReachedLabel: 'Máx. 20 ${resourcePluralLowerAlias} o perfiles de seguridad',
        reportAccessEntityPicker: {
            searchLabel: 'Buscar',
            applyButtonText: 'Aplicar',
            notLoadedLabel: 'No cargado',
            notLoadedValueLabel: 'Valor no cargado',
            showMoreButtonText: 'Mostrar más'
        },
        deleteReportPrompt: {
            title: '¿Borrar el informe «${reportName}»?',
            confirmDeleteLabel: 'Sí, borrar el informe',
            declineDeleteLabel: 'No, conservar el informe',
            createdByLine: 'Este informe fue creado por ${createdBy}.',
            permanentlyDeleteLine: '¿Desea borrar este informe permanentemente?',
            youLabel: 'usted'
        },
        toasterMessages: {
            create: 'Informe creado',
            delete: 'Informe borrado',
            edit: 'Informe editado',
            save: 'Informe guardado'
        },
        selectLabel: 'Seleccionar',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        securityProfilesLabel: 'Perfiles de seguridad',
        emptyNameError: 'Introduzca un nombre',
        duplicateNameError: 'Este nombre ya está en uso. Introduzca un nombre único.'
    },

    //Callback component
    callbackComponentText: 'Redireccionando…',

    // Planner/Jobs page
    plannerPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            pageTitle: 'Planificador',
            addLabel: 'Añadir',
            editLabel: 'Editar',
            editRoleByNameButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por nombre',
            editRoleByCriteriaButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por requisitos',
            viewLabel: 'Ver',
            barsLabel: 'Opciones de la barra',
            showLabel: 'Mostrar',
            increaseDateRangeLabel: 'Aumentar el rango de fechas',
            decreaseDateRangeLabel: 'Disminuir el rango de fechas',
            dateRangeLabel: 'Rango de fechas',
            goToTodayLabel: 'Ir a hoy',
            goToDateLabel: 'Ir a fecha',
            jobsLabel: 'Trabajos',
            resourcesLabel: 'Recursos',
            filtersLabel: 'Filtros',
            bookingLabel: 'Reserva',
            roleLabel: 'Función',
            jobLabel: 'Trabajo',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} desde una plantilla',
            clientLabel: 'Cliente',
            cutLabel: 'Cortar',
            copyLabel: 'Copiar',
            pasteLabel: 'Pegar',
            restartLabel: 'Reiniciar',
            archiveLabel: 'Archivar',
            rejectLabel: 'Rechazar',
            makeLiveLabel: 'Activar',
            submitRequestLabel: 'Enviar solicitud',
            createLabel: 'Crear',
            deleteLabel: 'Borrar',
            manageLabel: 'Gestionar',
            manageRoleTemplatesLabel: 'Gestionar las plantillas ${rolerequestSingularLowerAlias}',
            moreLabel: 'Más',
            newWorkspaceLabel: 'ES_New Workspace_ES',
            saveAsNewWorkspaceLabel: 'ES_Save as a new workspace_ES',
            manageMyWorkspacesLabel: 'ES_Manage My Workspaces_ES',
            privateWorkspacesLabel: 'ES_Private Workspaces_ES',
            publicWorkspacesLabel: 'ES_Public Workspaces_ES',
            noPublicWorkspacesCreatedLabel: 'ES_No public workspaces have been created_ES',
            noPrivateWorkspacesCreatedLabel: 'ES_No private workspaces have been created_ES',
            newPlanLabel: 'Nuevo plan',
            saveAsNewPlanLabel: 'Guardar como plan nuevo',
            manageMyPlansLabel: 'Gestionar mis planes',
            privatePlansLabel: 'Planes privados',
            publicPlansLabel: 'Planes públicos',
            saveChangesLabel: 'Guardar los cambios',
            saveChangesToPublicLabel: 'Guardar los cambios a público',
            noPublicPlansCreatedLabel: 'No se han creado planes públicos',
            noPrivatePlansCreatedLabel: 'No se han creado planes privados',
            noRoleTemplatesCreatedLabel: 'No se han añadido plantillas',
            customDateRangeLabel: 'Rango de fechas personalizado',
            dayLabel: 'Día',
            '5daysLabel': '5 días',
            '7daysLabel': '7 días',
            '10daysLabel': '10 días',
            weekLabel: 'Semana',
            '2weeksLabel': '2 semanas',
            '4weeksLabel': '4 semanas',
            '6weeksLabel': '6 semanas',
            monthLabel: 'Mes',
            '2monthsLabel': '2 meses',
            '3monthsLabel': '3 meses',
            '6monthsLabel': '6 meses',
            yearLabel: 'Año',
            weekendsLabel: 'Fines de semana',
            potentialConflicts: 'Mostrar conflictos potenciales',
            baseFilterLabel: 'Ver trabajos',
            rollForwardLabel: 'Duplicar',
            rollForwardTooltipText: 'Copiar el ${bookingEntityAlias} seleccionado en otro ${jobEntityAlias} u otra fecha',
            byNameSuffix: 'por nombre',
            byRequirementSuffix: 'por requisitos',
            findResourcesLabel: 'Buscar ${resourceEntityAlias}...',
            findResourceToolTipText: 'Buscar ${resourceEntityAlias} en base a criterios (f)',
            showMenuTooltipText: '${rolePluralUpperCase} sin asignar están ahora ocultos por defecto. Use el menú "Mostrar" para cambiarlo.',
            showInViewLabel: 'Mostrar en vista ${pluralViewNameAlias}',
            restorePlansLabel: 'Restablecer vista',
            restorePlanTooltipText: 'Restablecer la vista actual a su estado original. Utilice el menú desplegable de la derecha para guardar esta vista como plan.'
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: '${subRowEntityAlias} pasado',
            hideFutureEntityLabel: '${subRowEntityAlias} futuro',
            hideUnassignedRowsEntityLabel: 'Filas sin asignar',
            hideUnassignedBookingsEntityLabel: 'ES_Unassigned ${bookingPluralLowerCase}_ES',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactivos ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Mostrar ${subRowEntityAlias} para los que solo hay ${bookingPluralLowerCase} que terminan hoy o antes',
            hideFutureEntitiesExplanation: 'Mostrar ${subRowEntityAlias} para los que solo hay ${bookingPluralLowerCase} que empiezan después del final del rango de fechas visible',
            hideRolesExplanation: 'Mostrar ${subRowEntityAlias} para los que hay ${rolePluralLowerCase}',
            hideDraftRolesExplanation: 'Mostrar borradores de ${roleSingularCapitalized}',
            hideRequestedRolesExplanation: 'Mostrar las solicitudes de ${rolePluralCapitalized} que podrían convertirse en ${bookingPluralLowerCase} activas',
            toggleShowUnassignedRoles: '${rolePluralUpperCase} sin asignar',
            toggleShowRolesByName: '${rolePluralCapitalized} por nombre',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} por requisitos',
            hideJobTimelineToggleLabel: '${jobEntityAlias} línea de tiempo',
            hideJobMilestonesToggleLabel: '${jobEntityAlias} hitos',
            hideJobTimelineExplanation: 'Mostrar fechas de inicio y fin de ${jobEntityAlias}',
            hideJobMilestonesExplanation: 'Mostrar  fechas específicas de ${jobEntityAlias}'
        },
        selectionBar: {
            editAllButtonLabel: 'Editar',
            deleteAllButtonLabel: 'Borrar',
            editButtonLabel: 'Editar',
            editRoleByNameButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por nombre',
            editRoleByCriteriaButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por requisitos',
            deleteButtonLabel: 'Borrar',
            archiveAllButtonLabel: 'Archivar',
            archiveButtonLabel: 'Archivar',
            restartAllButtonLabel: 'Reiniciar',
            restartButtonLabel: 'Reiniciar',
            submitRequestAllButtonLabel: 'Enviar solicitud',
            submitRequestButtonLabel: 'Enviar solicitud',
            createButtonLabel: 'Crear',
            makeLiveSingularButtonLabel: 'Activar',
            makeLivePluralButtonLabel: 'Activar todos',
            insufficientRightsToEditAndDelete: 'Permiso insuficiente para editar/borrar estos ${entityAlias}',
            insufficientActionRights: 'Permisos insuficientes para realizar la acción para todos los ${entityAlias}',
            selectedLabel: 'seleccionado',
            maxBookingsSuffix: 'máx.',
            rollForwardLabel: 'Duplicar',
            rollForwardTooltipText: 'Copiar el ${bookingEntityAlias} seleccionado en otro ${jobEntityAlias} u otra fecha',
            showInView: 'Mostrar en vista ${pluralViewNameAlias}'
        },
        barOptions: {
            defaultLabel: 'Predeterminado',
            mediumLabel: 'Medio',
            expandedLabel: 'Expandido'
        },
        showLabelsOnBarsLabel: 'Mostrar la etiqueta en las barras',
        legendLabel: 'Leyenda',
        barFieldsLabel: 'Campos de barra ${barSingularAlias}',
        colourSchemeLabel: 'Tema de color',
        customColourThemeLabel: 'Tema de color personalizado',
        customColourSchemeLabel: 'Tema de color de ${barSingularAlias} personalizado',
        editedSuffix: 'editado',
        createdSuffix: 'creado',
        deletedSuffix: 'borrado',
        archivedSuffix: 'archivado',
        restartedSuffix: 'reiniciado',
        rejectedSuffix: 'rechazado',
        requestedSuffix: 'solicitado',
        liveSuffix: 'avanzado a',
        publishedRoleSuffix: 'publicado en ${marketplaceAlias}',
        scheduleRoleForPublishingSuffix: 'programado para su publicación en ${marketplaceAlias}',
        publicationEditedSuffix: 'publicación editada',
        publicationRemovedSuffix: 'eliminada de ${marketplaceAlias}',
        applyButtonText: 'Aplicar',
        searchLabel: 'Buscar',
        notLoadedLabel: 'no cargado',
        notLoadedValueLabel: 'valor no cargado',
        goToPageLabel: 'Vaya a la página',
        legend: {
            legendTitle: 'Leyenda',
            coloursColumnSubTitle: 'Los colores de las barras se basan en',
            barTypes: {
                draftRoles: 'borradores de ${rolerequestSingularCapitalAlias}',
                roleRequestsToLiveBookings: 'solicitudes ${rolerequestSingularCapitalAlias} que podrían convertirse en ${bookingPluralLowerAlias} activas',
                unconfirmed: 'Sin confirmar',
                planned: 'Planificado',
                excludesNonWorkingDays: 'Excluye días no laborables',
                includesNonWorkingDays: 'Incluye días no laborables',
                inConflict: 'En conflicto',
                startDateNonWorking: 'Fecha de inicio en fin de semana oculto',
                endDateNonWorking: 'Fecha de fin en fin de semana oculto',
                bothDatesNonWorking: 'Fechas de inicio y fin en fin de semana oculto',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} solicitudes que se han publicado ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Tema de color',
                barTypesTabTitle: 'Tipos de barras',
                milestonesTabTitle: 'Detalles de ${jobSingularAlias}'
            },
            jobDetailsLabels: {
                milestonesColumnTitle: 'Hitos',
                timelineColumnTitle: 'Línea de tiempo',
                linesColumnTitle: 'Líneas',
                normalLineLabel: '${jobSingularAliasLinesSection} con fechas de inicio y fin',
                dashedLineLabel: '${jobSingularAliasLinesSection} con una fecha de inicio o de fin faltante',
                lineEndsColumnTitle: 'Fin de la línea',
                onscreenDatesLabel: 'Fecha de inicio o fin ${jobSingularAliasLineEndsSection} en pantalla',
                offscreenDatesLabel: 'Fechas de inicio o fin ${jobSingularAliasLineEndsSection} fuera de pantalla',
                statesColumnTitle: 'Estados',
                incompletedStateLabel: 'Incompleto',
                completedStateLabel: 'Completo',
                overduedStateLabel: 'Atrasado'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'No hay reglas de color añadidas para'
        },
        plans: {
            manageMyPlansLabel: 'Gestionar mis planes',
            newPlanLabel: 'Nuevo plan',
            privatePlansColumnTitle: 'Mis planes',
            copyPlanLabel: 'Copiar plan',
            readOnlyLabel: 'Solo lectura',
            editAccessLabel: 'Editar acceso',
            renameLabel: 'Cambiar de nombre',
            deleteLabel: 'Borrar',
            moveToPublicLabel: 'Mover a público',
            makeCopyLabel: 'Hacer una copia',
            makePublicCopyLabel: 'Hacer una copia pública',
            makePrivateCopyLabel: 'Hacer una copia privada',
            moveToPrivateLabel: 'Mover a privado',
            privatePlansLabel: 'Planes privados',
            publicPlansLabel: 'Planes públicos',
            manageMyWorkspacesLabel: 'ES_Manage My Workspaces_ES',
            newWorkspaceLabel: 'ES_New Workspace_ES',
            privateWorkspacesColumnTitle: 'ES_My Workspaces_ES',
            privateWorkspacesLabel: 'ES_Private Workspaces_ES',
            publicWorkspacesLabel: 'ES_Public Workspaces_ES',
            copyWorkspaceLabel: 'ES_Copy workspace_ES',
            editWorkspaceLabel: 'ES_Edit workspace_ES'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Reservar ${entityAlias}',
            addBookingToJobRecordListCaption: 'Reservar en ${entityAlias}',
            searchLabel: 'Buscar',
            sortLabel: 'Ordenar',
            sortyByLabel: 'Ordenar por',
            columnsLabel: 'Columnas',
            applyButtonText: 'Aplicar',
            detailsLabel: 'detalles',
            notLoadedLabel: 'no cargado',
            notLoadedValueLabel: 'valor no cargado',
            historyFieldPlaceholder: 'Sin especificar',
            pastLabel: 'pasado',
            lastLoginLabel: 'Último inicio de sesión',
            expandAndCollapseText: 'Expandir y contraer filas',
            expandAllCaption: 'Expandir todo',
            collapseAllCaption: 'Contraer todo',
            sortLabelButton: 'Ordenar ${order}',
            resourcesLabel: 'ES_Resources_ES',
            josLabel: 'ES_Jobs_ES',
            calculatingSortCalcFields: 'ES_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_ES',
            resourceLabel: 'ES_Resource_ES',
            jobLabel: 'ES_Job_ES'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Cambio de asignación',
            outsideJobDatesLabel: 'Fuera de las fechas del trabajo',
            datesConflictWithBookingLabel: 'Las fechas están en conflicto con la reserva',
            bookingConflictLabel: 'Conflicto de reserva',
            inactiveResourceLabel: ' está inactivo. Ajustar a un recurso activo o no asignado para guardar los cambios',
            fromLabel: 'Desde',
            toLabel: 'Hasta',
            noDiaryAssignmentLabel: 'No hay agenda asignada',
            selectMultipleBarsHintPrefix: '+ clic',
            selectMultipleBarsHint: 'para seleccionar múltiples',
            splitBookingBarHintPrefix: 'Mantenga pulsado S',
            splitBookingBar: 'para dividir ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            customDateRangeLabel: 'Rango de fechas personalizado',
            goToLabel: 'Ir a',
            todayLabel: 'Hoy',
            dayLabel: 'Día',
            '5daysLabel': '5 días',
            '7daysLabel': '7 días',
            '10daysLabel': '10 días',
            weekLabel: 'Semana',
            '2weeksLabel': '2 semanas',
            '4weeksLabel': '4 semanas',
            '6weeksLabel': '6 semanas',
            monthLabel: 'Mes',
            '2monthsLabel': '2 meses',
            '3monthsLabel': '3 meses',
            '6monthsLabel': '6 meses',
            yearLabel: 'Año',
            customLabel: 'Personalizado',
            weekendsLabel: 'Fines de semana',
            prevLabel: 'Anterior',
            nextLabel: 'Siguiente'
        },
        multiSelectionTooltip: {
            selectionTooltip: '${entityCount} ${entityAlias} seleccionado',
            mixedSelectionTooltip: '${bookingsCount} ${bookingAlias} y seleccionado${rolesCount} ${rolerequestAlias}'
        },
        multiSelectionAlert: {
            message: 'Se ha alcanzado el límite',
            description: 'Ha alcanzado el límite de selección de ${maximumItemsCount} elementos'
        }
    },
    jobsPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Añadir',
            jobsLabel: 'Trabajos',
            filtersLabel: 'Filtros',
            manageLabel: 'Gestionar',
            editLabel: 'Editar',
            jobLabel: 'Trabajo',
            duplicateLabel: 'Duplicar',
            clientLabel: 'Cliente',
            editDetailsLabel: 'Editar detalles',
            baseFilterLabel: 'Ver',
            viewAllJobsLabel: 'Todos',
            viewJobsIManageLabel: 'Gestionados por mí',
            viewJobsActionRequiredLabel: 'Acción necesaria',
            staticMessageAddJobsMenu: 'ES_Resources can be added via User management_ES'
        }
    },
    resourcesPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Añadir',
            jobsLabel: 'Trabajos',
            filtersLabel: 'Filtros',
            manageLabel: 'Gestionar',
            editLabel: 'Editar',
            jobLabel: 'Trabajo',
            duplicateLabel: 'Duplicar',
            clientLabel: 'Cliente',
            editDetailsLabel: 'Editar detalles',
            baseFilterLabel: 'Ver',
            viewAllJobsLabel: 'Todos',
            viewJobsIManageLabel: 'Gestionados por mí',
            viewJobsActionRequiredLabel: 'Acción necesaria'
        }
    },
    roleInboxPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Añadir',
            editLabel: 'Editar',
            editRoleByNameButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por nombre',
            editRoleByCriteriaButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por requisitos',
            deleteLabel: 'Borrar',
            filtersLabel: 'Filtros',
            rolesLabel: 'Funciones',
            showLabel: 'Mostrar',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} desde una plantilla',
            noRoleTemplatesCreatedLabel: 'No se han añadido plantillas',
            archiveLabel: 'Archivar',
            restartLabel: 'Reiniciar',
            rejectLabel: 'Rechazar',
            createLabel: 'Crear',
            makeLiveLabel: 'Activar',
            submitRequestLabel: 'Enviar solicitud',
            byNameSuffix: 'por nombre',
            byRequirementSuffix: 'por requisitos',
            unassignFromRoleLabel: 'Desasignar de ',
            toggleShowUnassignedRoles: '${rolePluralUpperCase} sin asignar',
            toggleShowRolesByName: '${rolePluralCapitalized} por nombre',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} por requisitos',
            manageRoleTemplatesLabel: 'Gestionar las plantillas ${rolerequestSingularLowerAlias}',
            publishToMarketplaceLabel: 'Publicar en ${marketplaceAlias}',
            editRolePublicationButtonLabel: 'Editar publicación ${rolerequestSingularLowerAlias}',
            removeRolePublicationButtonLabel: 'Eliminar publicación ${rolerequestSingularLowerAlias}'
        }
    },
    marketplacePage: {
        roleCard: {
            startsOnLabel: '${rolerequestSingularCapitalAlias} comienza el',
            categoryLabel: 'Categoría',
            availabilityLabel: 'Su disponibilidad para este ${rolerequestSingularLowerAlias} es',
            numberOfResources: 'Número de ${resourcePluralLowerAlias}',
            numberOfFte: 'FTE',
            systemDetails: 'Publicado el ${publishedOn} (Actualizado el ${updatedOn})',
            pendingResourcesNeededText: '${pendingResources} requeridos',
            defaultRoleName: 'Nuevo rol',
            notAvailableLabel: 'No disponible'
        },
        commandBarConfig: {
            allLabel: 'Todos',
            filtersLabel: 'Filtros',
            marketplaceLabel: 'Panel de funciones',
            appliedAllLabel: 'Todos',
            appliedToLabel: 'He solicitado',
            availableForLabel: 'Estoy disponible para'
        },
        entityWindow: {
            roleApplicationSubmitted: 'Solicitud enviada',
            roleApplicationWithdrawn: 'La solicitud se ha retirado'
        }
    },
    previewEntityPage: {
        sharePopoverTitle: 'Compartir esto ${roleAlias}'
    },
    tableViewPage: {
        commandBarConfig: {
            pageTitle: 'Vista de tabla',
            addLabel: 'Añadir',
            editLabel: 'Editar',
            viewLabel: 'Ver',
            showLabel: 'Mostrar',
            increaseDateRangeLabel: 'Aumentar el rango de fechas',
            decreaseDateRangeLabel: 'Disminuir el rango de fechas',
            dateRangeLabel: 'Rango de fechas',
            goToTodayLabel: 'Ir a hoy',
            goToDateLabel: 'Ir a fecha',
            jobsLabel: 'Trabajos',
            resourcesLabel: 'Recursos',
            filtersLabel: 'Filtros',
            bookingLabel: 'Reserva',
            roleLabel: 'Función',
            jobLabel: 'Trabajo',
            clientLabel: 'Cliente',
            cutLabel: 'Cortar',
            copyLabel: 'Copiar',
            pasteLabel: 'Pegar',
            createLabel: 'Crear',
            deleteLabel: 'Borrar',
            manageLabel: 'Gestionar',
            moreLabel: 'Más',
            newPlanLabel: 'Nuevo plan',
            saveAsNewPlanLabel: 'Guardar como plan nuevo',
            manageMyPlansLabel: 'Gestionar mis planes',
            privatePlansLabel: 'Planes privados',
            publicPlansLabel: 'Planes públicos',
            saveChangesLabel: 'Guardar los cambios',
            saveChangesToPublicLabel: 'Guardar los cambios a público',
            noPublicPlansCreatedLabel: 'No se han creado planes públicos',
            noPrivatePlansCreatedLabel: 'No se han creado planes privados',
            customDateRangeLabel: 'Rango de fechas personalizado',
            dayLabel: 'Día',
            '5daysLabel': '5 días',
            '7daysLabel': '7 días',
            '10daysLabel': '10 días',
            weekLabel: 'Semana',
            '2weeksLabel': '2 semanas',
            '4weeksLabel': '4 semanas',
            '6weeksLabel': '6 semanas',
            monthLabel: 'Mes',
            '2monthsLabel': '2 meses',
            '3monthsLabel': '3 meses',
            '6monthsLabel': '6 meses',
            yearLabel: 'Año',
            weekendsLabel: 'Fines de semana',
            potentialConflicts: 'Mostrar conflictos potenciales',
            baseFilterLabel: 'Ver trabajos',
            findResourcesLabel: 'Buscar ${resourceEntityAlias}...',
            findResourceToolTipText: 'Buscar ${resourceEntityAlias} en base a criterios (F)',
            showMenuTooltipText: '' // This is empty to prevent the Show menu tooltip from showing on tableview page as it is not needed for now.
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: '${subRowEntityAlias} pasado',
            hideFutureEntityLabel: '${subRowEntityAlias} futuro',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactivos ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Mostrar ${subRowEntityAlias} para los que solo hay ${bookingPluralLowerCase} que terminan hoy o antes',
            hideFutureEntitiesExplanation: 'Mostrar ${subRowEntityAlias} para los que solo hay ${bookingPluralLowerCase} que empiezan después del final del rango de fechas visible'
        },
        selectionBar: {
            editAllButtonLabel: 'Editar',
            deleteAllButtonLabel: 'Borrar',
            editButtonLabel: 'Editar',
            deleteButtonLabel: 'Borrar',
            createButtonLabel: 'Crear',
            makeLiveSingularButtonLabel: 'Activar',
            makeLivePluralButtonLabel: 'Activar todos',
            insufficientRightsToEditAndDelete: 'Permiso insuficiente para editar/borrar estos ${entityAlias}',
            insufficientActionRights: 'Permisos insuficientes para realizar la acción para todos los ${entityAlias}',
            selectedLabel: 'seleccionado',
            maxBookingsSuffix: 'máx.'
        },
        legendLabel: 'Leyenda',
        barFieldsLabel: 'Campos de barra ${barSingularAlias}',
        colourSchemeLabel: 'Tema de color',
        customColourThemeLabel: 'Tema de color personalizado',
        customColourSchemeLabel: 'Tema de color de ${barSingularAlias} personalizado',
        editedSuffix: 'editado',
        createdSuffix: 'creado',
        deletedSuffix: 'borrado',
        applyButtonText: 'Aplicar',
        searchLabel: 'Buscar',
        notLoadedLabel: 'no cargado',
        notLoadedValueLabel: 'valor no cargado',
        legend: {
            legendTitle: 'Leyenda',
            coloursColumnSubTitle: 'Los colores de las barras se basan en',
            barTypes: {
                draftRoles: 'borradores de ${rolerequestSingularCapitalAlias}',
                roleRequestsToLiveBookings: 'solicitudes ${rolerequestSingularCapitalAlias} que podrían convertirse en ${bookingPluralLowerAlias} activas',
                unconfirmed: 'Sin confirmar',
                planned: 'Planificado',
                excludesNonWorkingDays: 'Excluye días no laborables',
                includesNonWorkingDays: 'Incluye días no laborables',
                inConflict: 'En conflicto',
                startDateNonWorking: 'Fecha de inicio en fin de semana oculto',
                endDateNonWorking: 'Fecha de fin en fin de semana oculto',
                bothDatesNonWorking: 'Fechas de inicio y fin en fin de semana oculto',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} solicitudes que se han publicado ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Tema de color',
                barTypesTabTitle: 'Tipos de barras',
                milestonesTabTitle: '${jobSingularAlias} detalles'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'No hay reglas de color añadidas para'
        },
        plans: {
            manageMyPlansLabel: 'Gestionar mis planes',
            newPlanLabel: 'Nuevo plan',
            privatePlansColumnTitle: 'Mis planes',
            copyPlanLabel: 'Copiar plan',
            editPlanLabel: 'Editar plan',
            readOnlyLabel: 'Solo lectura',
            editAccessLabel: 'Editar acceso',
            renameLabel: 'Cambiar de nombre',
            deleteLabel: 'Borrar',
            moveToPublicLabel: 'Mover a público',
            makeCopyLabel: 'Hacer una copia',
            makePublicCopyLabel: 'Hacer una copia pública',
            makePrivateCopyLabel: 'Hacer una copia privada',
            moveToPrivateLabel: 'Mover a privado',
            privatePlansLabel: 'Planes privados',
            publicPlansLabel: 'Planes públicos'
        },
        recordsList: {
            addBookingToJobRecordListCaption: 'Reservar ${entityAlias}',
            addBookingToJobLabel: 'Registrar ${entityAlias} para ${jobName}',
            addBookingToResourceRecordListCaption: 'Reservar en un ${entityAlias}',
            addBookingToResourceLabel: 'Registrar en ${entityAlias} para ${resourceName}',
            searchLabel: 'Buscar',
            sortLabel: 'Ordenar',
            sortyByLabel: 'Ordenar por',
            columnsLabel: 'Columnas',
            applyButtonText: 'Aplicar',
            detailsLabel: 'detalles',
            notLoadedLabel: 'no cargado',
            notLoadedValueLabel: 'valor no cargado',
            historyFieldPlaceholder: 'Sin especificar',
            pastLabel: 'pasado',
            lastLoginLabel: 'Último inicio de sesión',
            expandAndCollapseText: 'Expandir y contraer fila',
            expandAllCaption: 'Expandir todo',
            collapseAllCaption: 'Contraer todo',
            sortLabelButton: 'Ordenar ${order}',
            resourcesLabel: 'ES_resources_ES',
            jobsLabel: 'ES_jobs_ES',
            calculatingSortCalcFields: 'ES_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_ES',
            resourceLabel: 'ES_Resource_ES',
            jobLabel: 'ES_Job_ES'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Cambio de asignación',
            outsideJobDatesLabel: 'Fuera de las fechas del trabajo',
            datesConflictWithBookingLabel: 'Las fechas están en conflicto con la reserva',
            bookingConflictLabel: 'Conflicto de reserva',
            inactiveResourceLabel: ' está inactivo. Ajustar a un recurso activo o no asignado para guardar los cambios',
            fromLabel: 'Desde',
            toLabel: 'Hasta',
            noDiaryAssignmentLabel: 'No hay agenda asignada',
            selectMultipleBarsHintPrefix: '+ clic',
            selectMultipleBarsHint: 'para seleccionar múltiples',
            splitBookingBarHintPrefix: 'Mantenga pulsado S',
            splitBookingBar: 'para dividir ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            goToLabel: 'Ir a fecha',
            todayLabel: 'Hoy',
            dayLabel: 'Día',
            monthLabel: 'Mes',
            '1monthsLabel': '1 mes',
            '2monthsLabel': '2 meses',
            '3monthsLabel': '3 meses',
            '4monthsLabel': '4 meses'
        }
    },
    rolegroupListPage: {
        emptyStateBoldLabel: 'No hay escenarios',
        emptyStateLabel: 'Cree y compare escenarios para este trabajo',
        actionRequiredLabel: 'acción requerida',
        rolesLabel: 'funciones',
        actionsRequiredLabel: 'acciones requeridas'
    },
    rolegroupDetailsPage: {
        emptyStateLabel: 'Su lista de ${rolerequestPluralLowerAlias} se mostrará aquí',
        resourceInactiveString: 'Ajustar a un ${resourceSingularLowerAlias} activo para avanzar ${rolerequestSingularLowerAlias}.',
        addRoleText: 'Añadir ${rolerequestSingularLowerAlias}',
        addRoleByNameLabel: '${rolerequestSingularCapitalAlias} por nombre',
        addRoleByRequirementsLabel: '${rolerequestSingularCapitalAlias} por requisitos',
        roleFromTemplateLabel: '${rolerequestSingularCapitalAlias} desde una plantilla',
        noRoleTemplatesCreatedLabel: 'No se han añadido plantillas',
        manageRoleTemplates: 'Gestionar las plantillas ${rolerequestSingularLowerAlias}',
        notLoadedLabel: 'No se ha cargado',
        noValuesLoadedLabel: 'No se ha cargado el valor',
        applyText: 'Aplicar',
        searchText: 'Buscar',
        actionButtonLabel: 'Acciones',
        viewDetails: 'Ver detalles',
        defaultRoleName: 'Nueva función',
        noRoleGroupSetLabel: 'No se ha ajustado ningún ${rolerequestgroupSingularLowerAlias}',
        noResourcesMeetCriteriaText: 'Intente cambiar o eliminar algunos requisitos, y luego guarde los cambios.',
        noResourcesFoundAdditionalText: 'Los datos sobre habilidades actualizados recientemente pueden tardar hasta 24 horas en reflejarse en las sugerencias.',
        moveToButtonLabel: 'Mover a...',
        noMatchesFoundTopText: 'No se encontraron resultados',
        unsavedChangesToRoleText: 'Cambios sin guardar en ${rolerequestSingularLowerAlias}',
        suggestionsNotUpToDateText: 'Las sugerencias no están actualizadas, guarde los cambios antes de sugerir ${resourcePluralLowerAlias}.',
        assignResource: 'Asignar a ${rolerequestSingularLowerAlias}',
        unassignResource: 'Desasignar de ${rolerequestSingularLowerAlias}',
        addToShortlist: 'Añadir a la lista corta',
        hiddenSuggestionsTopText: 'Sugerencias ocultas',
        hiddenSuggestionsText: 'Los ${resourcePluralLowerAlias} sugeridos están ocultos, ya que se basan en algunos requisitos que usted no tiene suficientes permisos para ver.',
        tailMessage: '¿No pudo encontrar una coincidencia adecuada? Pruebe a modificar o eliminar algunos requisitos de roles.',
        editButtonLabel: 'Editar ${rolerequestSingularCapitalAlias}',
        multipleRolesSelectedTopText: 'Múltiples ${rolerequestPluralLowerAlias} seleccionados',
        multipleRolesSelectedBodyText: 'Para ver los sugeridos ${resourcePluralLowerAlias}, seleccione solamente uno ${rolerequestSingularCapitalAlias} por requisitos.',
        removeFromShortlist: 'Eliminar de preselección',
        shortlist: 'Preselección',
        shortlisted: 'Seleccionados',
        notShortListed: 'No seleccionados',
        shortlistedBy: 'Seleccionados por',
        maxShortlistReachedFirstRow: 'Limitado a',
        maxShortlistReachedSecondRow: '6 recursos',
        saveAsTemplateLabel: 'Guardar como plantilla',
        potentialConflictsTooltip: 'Existen posibles conflictos con {bookingSingularLowerAlias} ya existent',
        maxAllowedRolesLabel: 'Máx. 100 ${rolerequestPluralLowerAlias}',
        commandBarConfig: {
            editLabel: 'Editar',
            duplicateLabel: 'Duplicar',
            deleteLabel: 'Borrar'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Reservar ${entityAlias}',
            addBookingToJobRecordListCaption: 'Reservar en ${entityAlias}',
            searchLabel: 'Buscar',
            sortLabel: 'Ordenar',
            sortyByLabel: 'Ordenar por',
            columnsLabel: 'Columnas',
            applyButtonText: 'Aplicar',
            detailsLabel: 'detalles',
            notLoadedLabel: 'no cargado',
            notLoadedValueLabel: 'valor no cargado',
            historyFieldPlaceholder: 'Sin especificar',
            pastLabel: 'pasado',
            lastLoginLabel: 'Último inicio de sesión',
            expandAndCollapseText: 'Expandir y contraer filas',
            expandAllCaption: 'Expandir todo',
            collapseAllCaption: 'Contraer todo',
            sortLabelButton: 'Ordenar ${order}',
            resourcesLabel: 'ES_Resources_ES',
            josLabel: 'ES_Jobs_ES',
            calculatingSortCalcFields: 'ES_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_ES',
            resourceLabel: 'ES_Resource_ES',
            jobLabel: 'ES_Job_ES'
        }
    },
    roleRequirementsSection: {
        addCriteriaButtonLabel: 'Añadir requisitos',
        resourceAttributesLabel: 'Atributos de ${resourceSingularCapitalAlias}',
        mustMeetCriteriaDescription: 'Debe cumplir',
        mustMeetCriteriaExplanation: 'Los ${resourcePluralCapitalAlias} que no cumplan estos requisitos no se sugerirán',
        criteriaEmptyStateMessage: 'No se han añadido requisitos',
        removeRequirementLabel: 'Eliminar',
        resourceSkillsLabel: '${resourceSingularCapitalAlias} habilidades',
        searchLabel: 'Buscar',
        applyButtonText: 'Aplicar',
        removeSkillsText: 'Borrar habilidades',
        removeFilterText: 'Eliminar habilidades',
        levelText: 'Nivele',
        levelsText: 'Niveles',
        anySkillsText: 'Cualquiera',
        oneOfSkillsText: 'Uno de...',
        saveSkillsText: 'Añadir habilidades',
        cancelSkillsText: 'Cancelar'
    },
    entityWindow: {
        basicDetailsSectionTitle: 'Detalles básicos',
        requirementsSectionTitle: 'Requisitos',
        milestonesSectionTitle: 'Hitos',
        cMeSectionTitle: 'Rasgos de C-me',
        workHistoryTitle: 'Trabajo reciente',
        workDetailsSectionTitle: 'Detalles del trabajo',
        budgetSectionTitle: 'Presupuesto',
        budgetDetailsSectionTitle: 'Detalles del presupuesto',
        timeAndFinancialsSectionTitle: 'ES_Time and financials_ES',
        revenueSectionTitle: 'ES_Revenue_ES',
        costsSectionTitle: 'ES_Costs_ES',
        profitSectionTitle: 'ES_Profit_ES',
        hoursSectionTitle: 'ES_Hours_ES',
        planningSectionTitle: 'Planificación',
        previousRelatedJob: 'Anterior trabajo relacionado',
        nextRelatedJob: 'Siguiente trabajo relacionado',
        contactSectionTitle: 'Contacto',
        emplyomentDetailsSectionTitle: 'Detalles del empleo',
        skillsSectionTitle: 'Habilidades',
        systemInfoSectionTitle: 'Información del sistema',
        timeAllocationTitle: 'Asignación de tiempo',
        projectHealthTitle: 'Estado del proyecto',
        fixedTimeSectionSuffix: 'horas',
        loadingSectionSuffix: '% de horas trabajadas',
        timeSectionSuffix: 'horas reservadas en total',
        hoursInTotalSuffix: 'horas en total',
        hoursPerDaySuffix: 'horas al día',
        FTESuffix: 'FTE',
        resourcesSuffix: '${resourcePluralLowerAlias}',
        nameLabel: 'Nombre',
        updatedToLabel: 'actualizado a',
        fromLabel: 'desde',
        numberOfResourcesPrefix: 'Número de recursos',
        chargeRateFieldsControlTitle: 'Tasa de cargo',
        bookingResourceChargeRateLabel: 'Tasa de cargo del recurso',
        bookingOverriddenChargeRateLabel: 'Usar tasa de cargo diferente',
        bookingCustomChargeRateLabel: 'Usar tasa personalizada',
        bookingRevenueRatesRowTitle: 'Ingresos',
        bookingCostRatesRowTitle: 'Coste',
        bookingProfitRatesRowTitle: 'Beneficios',
        bookingViewModeChargeRatesTitle: 'Tasas',
        bookingOwnResourceChargeModeLabel: 'Tasa de cargo del recurso',
        bookingDifferentResourceChargeModeLabel: 'Tasa de cargo diferente',
        bookingCustomChargeModeLabel: 'Tasa personalizada',
        rolerequestDescriptionPlaceholder: 'p. ej. gestor de proyectos',
        rolerequestOwnResourceChargeModeLabel: 'Tasa de cargo del recurso',
        rolerequestDifferentResourceChargeModeLabel: 'Tasa de cargo diferente',
        rolerequestCustomChargeModeLabel: 'Tasa personalizada',
        rolerequestResourceChargeRateLabel: 'Tasa de cargo del recurso',
        rolerequestOverriddenChargeRateLabel: 'Usar tasa de cargo diferente',
        rolerequestCustomChargeRateLabel: 'Usar tasa personalizada',
        rolerequestRevenueRatesRowTitle: 'Ingresos',
        rolerequestCostRatesRowTitle: 'Coste',
        rolerequestProfitRatesRowTitle: 'Beneficios',
        roleByNameWindowTitle: '${rolerequestCapitalEntityAlias} por nombre',
        manageRoleTemplatesWindowTitle: 'Gestionar mis plantillas',
        roleTemplateWindowTitle: '${rolerequestCapitalEntityAlias} plantilla',
        roleByRequirementWindowTitle: '${rolerequestCapitalEntityAlias} por requisitos',
        dateRangeLabel: 'Rango de fechas',
        datesRequiredLabel: 'Fechas obligatorias',
        nonWorkSectionFieldText: 'Incluir días no laborables',
        bookingSectionTitle: 'Reserva',
        rolerequestSectionTitle: 'Requisitos',
        rolerequestGroupSectionTitle: 'Grupo de función',
        jobSectionTitle: 'Trabajo',
        resourceSectionTitle: 'Recurso',
        clientSectionTitle: 'Cliente',
        bookingSectionTitlePlural: 'Reservas',
        jobSectionTitlePlural: 'Trabajos',
        resourceSectionTitlePlural: 'Recursos',
        clientSectionTitlePlural: 'Clientes',
        rolerequestSectionTitlePlural: 'Requisitos',
        rolerequestGroupSectionTitlePlural: 'Grupos de función',
        moreInfoButtonLabel: 'Más información',
        duplicateLabel: 'Duplicar',
        assignToRoleButtonLabel: 'Asignar a',
        unassignFromRoleButtonLabel: 'Desasignar de',
        rejectButtonLabel: 'Rechazar',
        restartButtonLabel: 'Reiniciar',
        archiveButtonLabel: 'Archivar',
        editButtonLabel: 'Editar',
        applyButtonLabel: 'Aplicar',
        closeButtonLabel: 'Cerrar',
        withdrawButtonLabel: 'Retirar aplicación',
        editRoleByNameButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por nombre',
        editRoleByCriteriaButtonLabel: 'Editar ${rolerequestPluralCapitalAlias} por requisitos',
        createButtonLabel: 'Crear',
        createEntityTitle: 'Crear ${entityTitleAlias}',
        editEntityTitle: 'Editar ${entityTitleAlias}',
        makeLiveSingularButtonLabel: 'Activar',
        makeLivePluralButtonLabel: 'Activar todos',
        submitRequestButtonLabel: 'Enviar solicitud',
        addBookingLabel: 'Añadir reserva',
        cancelButtonLabel: 'Cancelar',
        saveChangesButtonLabel: 'Guardar los cambios',
        saveAllButtonLabel: 'Guardar todos',
        discardChangesButtonLabel: 'Descartar cambios',
        progressButtonLabel: 'Avanzar',
        deleteButtonLabel: 'Borrar',
        editAllButtonLabel: 'Editar todo',
        archiveAllButtonLabel: 'Archivar todo',
        restartAllButtonLabel: 'Reiniciar todo',
        submitRequestAllButtonLabel: 'Enviar solicitud',
        deleteAllButtonLabel: 'Borrar todo',
        newButtonLabel: 'Crear',
        viewButtonLabel: 'Ver',
        compareButtonLabel: 'Comparar',
        createTemplateLabel: 'Crear plantilla',
        roleTemplateLabel: '${rolerequestSingularCapitalAlias} plantilla',
        rolePublicationWindowTitle: '${rolerequestCapitalEntityAlias} publicación',
        insufficientActionRights: 'Permisos insuficientes para realizar la acción para todos',
        addNewRoleLabel: 'Añadir un nuevo ${rolerequestSingularLowerAlias}',
        roleListBodyEmptyStateLabel: 'Puede añadir un ${resourceSingularLowerAlias} con nombre, o bien introducir los requisitos de ${rolerequestSingularLowerAlias} para encontrar los ${resourcePluralLowerAlias} adecuados',
        manageRoleTemplatesEmptyStateLabel: 'No tiene ninguna plantilla ${rolerequestSingularLowerAlias}.',
        templateDetailsLabel: 'Detalles de la plantilla',
        provideTemplateNameLabel: 'Escriba un nombre para su plantilla',
        maxLengthValidationMessage: 'Máximo ${maxNameLength} símbolos permitidos',
        renameLabel: 'Cambiar nombre',
        createdLabel: 'Creada',
        myTemplatesLabel: 'Mis plantillas',
        deleteMultipleBookinngsButtonLabel: 'Borrar',
        deleteMultipleRolerequestsButtonLabel: 'Borrar',
        bookingStatusFieldExplanation: 'El recurso permanecerá disponible en el tiempo reservado',
        tableViewBookingStatusFieldExplanation: 'Los no confirmados ${bookingSingularLowerAlias} podrán verse en la ${plannerPageAlias} página . ${resourceSingularCapitalAlias} seguirán disponibles en el tiempo reservado.',
        nonWorkSectionFieldExplanation: 'Las horas de contingencia se reservarán en los días no laborables',
        jobIsConfidentialFieldExplanation: 'Los trabajos confidenciales solo los pueden ver las personas que tengan acceso',
        rolerequestRolerequestGroupFieldExplanation: 'Dejar este espacio en blanco creará un rol fuera de un escenario.',
        rolerequestFTEFieldExplanation: '1 El equivalente de tiempo completo es ${referenceDiaryTime} horas por día',
        resourceSectionFieldExplanation: 'Al añadir varios recursos se creará una reserva para cada uno',
        jumpToSectionTitle: 'Saltar a',
        additionalSectionTitle: 'Sección adicional',
        additionalDetailsSectionTitle: 'Detalles adicionales',
        commentsSectionTitle: 'Comentarios',
        roleGroupListSectionTitle: 'Grupos de función',
        detailsPaneTooltipText: 'El panel de detalles le ofrece información sobre el elemento que ha seleccionado. Los iconos le llevarán a la información sobre el trabajo, los recursos o las reservas.',
        detailsPaneTooltipTitle: 'Panel de detalles',
        attachmentsSectionTitle: 'Documentos',
        moreOptionsButtonLabel: 'Más opciones',
        bookingBudgetDetailsMessage: 'Los cálculos del presupuesto se realizan con la tasa vigente el primer día de la reserva.',
        entityCreatedSuffix: 'creado',
        entityDeletedSuffix: 'borrado',
        notFoundPrefix: 'valor',
        notFoundSuffix: 'no encontrado',
        roleMarketplaceCriteriaMatchExplanation: 'Los solicitantes deben cumplir con los requisitos',
        rolerequestDiaryForEstimationLabel: 'Diario para su estimación',
        selectChargeRateLabel: 'Seleccione unos honorarios',
        customChargeRateLabel: 'Honorarios personalizados',
        estimatedBudgetLabel: 'Presupuesto estimado',
        estimatesTabLabel: 'Estimación',
        assignedTotalsTabLabel: 'Totales asignados',
        roleGroupCountLabel: '${roleGroupCount} ${rolerequestgroupPluralCapitalAlias}',
        messages: {
            bookingBudgetDetailsMessageText: 'Los cálculos del presupuesto están utilizando la tasa de cargo que era válida en el primer día de ${bookingSingularLowerAlias}.',
            roleBudgetDetailsMessageText: 'Los cálculos del presupuesto se realizan con los honorarios vigentes el primer día del ${rolerequestSingularLowerAlias}.',
            roleAssigneesTotalsDifferenceText: 'Los totales reales pueden variar si los asignados tienen diarios u honorarios diferentes a los de la estimación.',
            bookingMultipleResourcesBudgetDetailsMessageText: `Las tarifas de presupuesto específicas para cada \${bookingSingularLowerAlias} se pueden ver en sus detalles después de la creación.
            Los cálculos del presupuesto están utilizando la tasa de cargo que era válida en el primer día de \${bookingSingularLowerAlias}.`,
            roleResourceWarningText: 'Asigne un ${resourceSingularLowerAlias} para solicitar ${bookingSingularLowerAlias}.',
            roleResourcesContainUnassigedWarningText: `Las solicitudes \${bookingSingularCapitalAlias} no pueden avanzar con
            \${resourcePluralLowerAlias} no asignado, descarte la selección \${resourceSingularLowerAlias} 'Sin asignar'.`,
            criteriaRoleUnassignedResourceText: 'La información presupuestaria se calculará cuando se asignen ${resourcePluralLowerAlias} al ${rolerequestSingularLowerAlias}.',
            requirementSectionInsufficientPermissionsText: 'Algunos requisitos están ocultos porque no tiene suficientes permisos.',
            rolerequestCriteriaDPSuggestionPaneMessageText: 'Asignar ${resourcePluralLowerAlias} al ${rolerequestSingularLowerAlias} por medio de !{Suggestion pane}.',
            suggestionPaneButtonText: 'Panel de sugerencias',
            criteriaRoleAssignResourceText: 'Asignar ${resourcePluralLowerAlias} al ${rolerequestSingularLowerAlias} por medio de !{Suggestion pane}.',
            criteriaRoleAssignedResourceChangeMessageText: 'Cambiar la asignación al ${rolerequestSingularLowerAlias} mediante el panel de Sugerencias.',
            criteriaRoleAssignResourceToCalculateBudgetText: 'Para calcular el presupuesto debe asignar ${resourcePluralLowerAlias} al ${rolerequestSingularLowerAlias}.',
            criteriaRolePublishMessageText: 'La publicación ${rolerequestSingularLowerAlias} terminará automáticamente tras la fecha de fin de  ${rolerequestSingularLowerAlias} - ${endDate}',
            roleApplicationAppliedOnText: 'Ha solicitado este ${rolerequestSingularLowerAlias} el ${applyDate}.',
            bookingJobOverBudgetMessageText: 'Este ${bookingSingularLowerAlias} hará que este ${jobSingularLowerAlias} se salga del presupuesto.',
            bookingResourceOverBudgetMessageText: 'Reservar este ${resourceSingularLowerAlias} hará que este ${jobSingularLowerAlias} se salga del presupuesto.',
            bookingJobHoursOverBudgetMessageText: 'ES_This ${bookingSingularLowerAlias} will put this ${jobSingularLowerAlias}\'s Total hours at ${jobHoursPercentageBudget}% (${totalHoursOverBudget} hrs over) of its Budget hours_ES'
        },
        financialInformationSectionTitle: 'Información financiera',
        schedulingSectionTitle: 'Programación',
        rolesSectionTitle: 'Funciones',
        resourceSummaryTitle: 'Resumen',
        overlappingBookingsTitle: 'Superposición de reservas y funciones',
        saveAsADraft: 'Guardar como borrador',
        backToSuggestionLabel: 'Volver a sugerencias',
        suggestLabel: 'Sugerir',
        suggestedLabel: 'Sugerido',
        forLabel: 'para',
        moveButtonLabel: 'Mover',
        moveToModalTitle: 'Mover a...',
        searchForLabel: 'Buscar',
        lastRefreshedText: 'Última actualización',
        SelectionTitleLabel: 'Actualización masiva de todos ',
        SelectionDescriptionLabel: 'Establezca valores o deje en blanco para borrar valores',
        SelectionFieldsCaptionLabel: 'Campos',
        shortlistUptoSixText: 'Puede preseleccionar hasta 6 recursos.',
        manageBudgetLabel: 'Gestionar presupuesto',
        movePendingFTELabel: 'Mover FTE pendientes',
        removePendingFTELabel: 'Eliminar FTE pendientes',
        movePendingResourcesLabel: 'Mover ${resourcePluralLowerAlias} pendientes',
        removePendingResourcesLabel: 'Eliminar recursos pendientes',
        publishToMarketplaceLabel: 'Publicar en ${pageAlias}',
        publishRoleLabel: 'Publicar ${rolerequestSingularLowerAlias}',
        roleMarketplaceCategoryPlaceholder: 'Añadir categoría',
        saveAsTemplateLabel: 'Guardar como plantilla',
        editRolePublicationButtonLabel: 'Editar publicación ${rolerequestSingularLowerAlias}',
        removeRolePublicationButtonLabel: 'Eliminar publicación ${rolerequestSingularLowerAlias}',
        emptyState: {
            noRoleGroupItemsCoincidenceMessage: 'Para ver los detalles de ${rolerequestgroupSingularLowerAlias}, seleccione ${rolerequestPluralLowerAlias} que sean del mismo ${rolerequestgroupSingularLowerAlias}.',
            noRoleGroupItemsCoincidenceContent: 'Varios ${rolerequestgroupPluralLowerAlias} en la selección'
        },
        createScenarioLabel: 'Crear ${rolerequestgroupSingularLowerAlias}',
        editScenarioLabel: 'Editar ${rolerequestgroupSingularLowerAlias}',
        openScenarioButtonLabel: 'Abrir ${rolerequestgroupSingularCapitalAlias}',
        fieldValueCaption: {
            budget: 'ES_of budget_ES',
            target: 'ES_of target_ES'
        },
        reviewSettings: {
            headerTitle: 'ES_Review settings_ES',
            saveChangesButtonLabel: 'ES_Save changes_ES',
            cancelBtnLabel: 'ES_Cancel_ES',
            resourceSkillsReviewLabel: 'ES_Resource skills reviews_ES',
            reviewerOnThisJobLabel: 'ES_Reviewer on this job_ES',
            eligibleForReviewLabel: 'ES_Eligible for reviews_ES',
            reviewerOnThisJobCaptionLabel: 'ES_Choose who can review resource skills on this job_ES',
            pastBookingLabel: 'ES_Only resources with past booking on this job_ES',
            allBookedLabel: 'ES_All booked resources on this job_ES',
            clearSkillsReviewsLabel: 'ES_Clear all skills reviews_ES'
        },
        reviewSection: {
            skillReviewLabel: 'ES_Skill reviews_ES',
            resourceReviewedLabel: 'ES_{0} Resource reviewed_ES',
            submitReviewButtonLabel: 'ES_Submit skills review_ES'
        }
    },
    resourceSummarySection: {
        availabilityText: 'Disponibilidad',
        suitabilityText: 'Idoneidad',
        hoursSuffix: 'h',
        andLabel: 'y'
    },
    suggestedResources: {
        addToShortlist: 'Añadir a la lista',
        matchTextSuffix: 'coincide',
        isATextMessage: 'es un ',
        skillsLabel: 'habilidades',
        workExperienceLabel: 'Experiencia laboral',
        skillWithPrefix: 'Habilidad con',
        similarityTextSuffix: 'similaridad:',
        suitabilityText: 'Idoneidad',
        mixedSortOptionText: 'Idoneidad y disponibilidad',
        sortOrderText: 'Orden de clasificación: ',
        suggestionsLabel: 'Sugerencias',
        forLabel: 'para',
        appliedOn: 'Se aplica en:',
        lastRefreshedText: 'Última actualización',
        refreshLabel: 'Actualizar',
        loadingLabel: 'Cargando...',
        noRelevantAISuggestionDataText: 'Datos de experiencia o habilidades insuficientes para una puntuación de idoneidad',
        infoBannerMessage: 'Todos los resultados cumplen los requisitos de atributo de ${resourceSingularLowerAlias}.',
        aiSuggestionsLabel: 'Sugerencias de IA',
        aiToggleTooltipText: 'Active esta opción para utilizar sugerencias de IA.'
    },
    common: {
        selectRowTextAriaLabel: 'Seleccionar fila para ${name}',
        maximumFieldLengthValidationMessage: 'Máximo ${maximumFieldSize} caracteres',
        daysString: 'días',
        singleDayString: 'día',
        workingString: 'laborable',
        nonWorkingString: 'no laborable',
        hoursString: 'horas',
        FTEstring: 'FTE',
        pendingString: 'pendiente',
        hoursPerDaySuffix: 'horas al día',
        excludeNonWorkingString: 'excluye días no laborables',
        includeNonWorkingString: 'incluye días no laborables',
        addPrefix: 'Añadir',
        newPrefix: 'Nuevo',
        allPrefix: 'Todos',
        addAnotherPrefix: 'Añadir otro',
        clickToEditSuffix: '(haga clic para editar)',
        insufficientPermissionsToEditSuffix: 'Permisos insuficientes para editar',
        historyFieldPlaceholder: 'Sin especificar',
        noValueMessage: 'No se ha ajustado ningún ${fieldInfoAlias}',
        milestoneHistoryFieldPlaceholder: 'No se ha configurado ningún hito',
        startingLabel: 'Inicio',
        endingLabel: 'Fin',
        dueDate: 'Fecha de vencimiento',
        nameLabel: 'Nombre',
        restrictedLabel: 'Restringido',
        markAsCompletedLabel: 'Marcar como Completado',
        milestonesSectionTitle: 'Hitos',
        milestonesSectionTitleNameDueDate: 'Fecha de vencimiento del nombre del hito',
        milestonePlaceholder: 'P. ej., aprobación de proyectos',
        noString: 'No',
        setString: 'ajustar',
        totalSuffix: 'Total',
        hourlySuffix: 'por horas',
        noResultsFoundMessage: 'No se encontraron resultados',
        noResultsMessage: 'Sin resultados',
        checkedMessage: 'Sí',
        uncheckedMessage: 'No',
        shownLabel: 'Mostrar',
        hiddenLabel: 'Ocultar',
        jobUtitilizationInfo: 'Excluir reservas del uso',
        excludeValue: 'Excluir',
        includeValue: 'Incluir',
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'se ha encontrado con este nombre.',
        noOptionsSetSuffix: 'opciones de categorías establecidas por su administrador',
        showMorePrefix: 'Mostrar',
        showMoreSuffix: 'más',
        showLessText: 'Mostrar menos',
        seeMoreText: 'ES_See more_ES',
        detailsTabLabel: 'Detalles',
        historyTabLabel: 'Historial',
        editAllTabLabel: 'Editar todo',
        roleListLabel: 'Lista de funciones',
        newTemplateLabel: 'Nueva plantilla',
        audit: {
            sortLabel: 'Ordenar',
            showMoreText: 'Mostrar más',
            backToTopText: 'Volver arriba',
            oldValuePrefix: 'desde',
            actorPrefix: 'por',
            startingText: 'comenzado',
            timeLineActionCreateAlias: 'añadido',
            timeLineActionUpdateAlias: 'actualizado',
            unassignedValue: 'Sin asignar',
            levelString: 'nivel',
            timeLineActionUpdateSuffixAlias: 'a',
            timeLineActionDeleteAlias: 'eliminado',
            timeLineActionRemoveAlias: 'eliminado',
            falseString: 'No',
            trueString: 'Sí',
            anyLevelString: 'Todos los niveles',
            resourceNotFoundCaption: '${resourceSingularCapitalAlias} no encontrado',
            templateTexts: {
                historyRecordCreateText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionCreateAlias}',
                historyRecordUpdateText: '${alias} ${timeLineActionUpdateAlias} ${timeLineActionUpdateSuffixAlias} ${valueDescription} ${oldValuePrefix} ${oldValueDescription} ${startingText} ${valueStartDate}',
                historyRecordDeleteText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionRemoveAlias}'
            },
            auditSectionTitles: {
                projectHealthTitle: 'Estado del proyecto:'
            }
        },
        lastUpdatedLabel: 'Última actualización',
        updatedByLabel: 'por',
        noDiaryOnDatesLabel: 'Sin agenda',
        noOverlappingBookings: 'Sin reservas solapadas',
        archivedLabel: 'Archivar',
        restartedLabel: 'Reiniciar',
        requestedLabel: 'Solicitar',
        batchRequestedLabel: 'solicitudes',
        rejectedLabel: 'Rechazar',
        expiredLabel: 'Caduca',
        draftLabel: 'Borrador',
        liveLabel: 'Activo',
        actionsDropdownLabel: 'Acciones',
        unassignedPlaceholder: 'Sin asignar',
        availabilityText: 'Disponibilidad',
        movePendingFTELabel: 'Mover FTE pendientes',
        removePendingFTELabel: 'Eliminar FTE pendientes',
        movePendingResourcesLabel: 'Mover ${resourcePluralLowerAlias} pendientes',
        removePendingResourcesLabel: 'Eliminar recursos pendientes',
        rolerequestLoadingExplanation: 'de capacidad ${resourcePluralLowerAlias}',
        noRateSetLabel: 'Sin tarifa establecida',
        resourcesString: '${resourcePluralLowerAlias}',
        potentialConflictsTooltip: 'Posibles conflictos con ${booking} existentes',
        addFiltersButtonLabel: 'Añadir filtros',
        singleDayUnit: 'día',
        daysUnit: 'días',
        singleWeekUnit: 'semana',
        weeksUnit: 'semanas',
        singleMonthUnit: 'mes',
        monthsUnit: 'meses',
        singleYearUnit: 'año',
        yearsUnit: 'años',
        revertToStartAndEndDates: 'Volver a fechas de inicio y final',
        searchToSelect: 'Buscar para seleccionar',
        fieldMandatoryText: 'Este campo es obligatorio',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        estimatesTooltip: 'Estimación del presupuesto ${rolerequestSingularLowerAlias} antes de asignar recursos.',
        assigneesTotalsTooltip: 'Presupuesto ${rolerequestSingularLowerAlias} real con asignados actuales.',
        editLabel: 'Botón editar',
        closeLabel: 'Botón cerrar',
        deleteLabel: 'Botón borrar',
        addLabel: 'Botón añadir',
        quantityAria: 'Introducir ${fieldAlias}',
        criteriaRoleBudgetDescription: 'Se utilizan valores estimados en los totales del presupuesto ${rolerequestgroupSingularLowerAlias} hasta que el ${rolerequestSingularLowerAlias} cuente con una plantilla completa',
        noResourcesAssigned: 'No se ha asignado ningún ${resourcePluralLowerAlias}',
        noRoleGroupSetLabel: 'No se ha ajustado ningún ${rolerequestgroupSingularLowerAlias}',
        avatarAltText: 'Imagen de perfil de ${resourceName}',
        copyOfPrefix: 'Copia de',
        ascendingSort: 'Ascendente',
        descendingSort: 'Descendente',
        selectionListSort: 'Orden de la lista de opciones',
        deleteLabelText: 'Borrar',
        deleteLabelItemText: 'Borrar ${item}',
        scrollDownText: 'Desplazarse hacia abajo',
        scrollBarText: 'Barra de desplazamiento',
        scrollUpText: 'Desplazarse hacia arriba',
        cancelChangesLabel: 'Cancelar los cambios',
        saveChangesLabel: 'Guardar los cambios',
        fullScreenButtonLabel: 'vista de página completa',
        clearNameLabel: 'Borrar ${fieldName} campo',
        collapseLeftNavigation: 'Contraer navegación de la izquierda',
        expandLeftNavigation: 'Expandir navegación de la izquierda',
        fieldAriaLabel: 'Introducir ${fieldAlias}',
        selectAllRolesLabel: 'Seleccionar todos los puestos',
        expandRowLabel: 'Expandir la fila',
        collapseRowLabel: 'Contraer la fila',
        floatingActionBarLabel: 'ES_${entity} ${fieldName} may be different for this date range_ES',
        floatingActionBarButtonLabel: 'ES_Update sort order_ES'
    },
    tableOptions: {
        displayDensityOptionTitle: 'Mostrar densidad',
        chooseDetailsOptionTitle: 'Elegir detalles',
        compactDensityOptionTitle: 'Compacto',
        defaultDensityOptionTitle: 'Predeterminado',
        expandedDensityOptionTitle: 'Expandido',
        searchLabel: 'Buscar',
        applyButtonText: 'Aplicar',
        notLoadedLabel: 'no cargado',
        notLoadedValueLabel: 'no se cargó el valor',
        tableOptionsLabel: 'Opciones de tabla'
    },
    dataGrid: {
        sortLabel: 'Clasificar',
        sortyByLabel: 'Clasificar por',
        pageOptionSuffix: ' por página',
        noItemsMessage: 'No tiene ningún trabajo',
        noRolesItemsMessage: 'No tiene ninguna función',
        newJob: 'Nuevo trabajo',
        noMatchingItemsMessage: 'No se encontraron trabajos para los filtros',
        noMatchingResourceItemsMessage: 'ES_No resources found matching your filters_ES',
        noMatchingResourceItemsContentMessage: 'ES_Try changing your filters or view settings_ES',
        noMatchingRolesItemsMessage: 'No se encontraron funciones para los filtros',
        noMatchingItemsContent: 'Pruebe cambiando los filtros.',
        noChargetypeSet: 'No se ha establecido un tipo de cargo',
        noRoleGroupItemsMessage: 'No hay escenarios',
        noRoleGroupItemsContent: 'Cree y compare escenarios para este trabajo',
        noResourceFoundMessage: 'No ${resourceEntityAlias} se encontraron criterios de coincidencia',
        noMarketplaceRolesPublished: 'No hay ningún ${rolerequestPluralLowerAlias} publicado',
        showingCaption: 'Se muestran ${pageRolesCount} de ${totalRolesCount} ${rolerequestAlias}',
        entityPageOptionSuffix: '${rolerequestPluralLowerAlias} por página',
        noResultsMessage: 'No se han encontrado resultados',
        tryAdjustingFiltersMessage: 'Intente justar su búsqueda o filtro',
        sortLabelButton: 'Ordenar ${order}',
        sortAscending: 'Ascendente',
        sortDescending: 'Descendente',
        operationLogEmptyMessage: 'El registro de operaciones está vacío',
        operationLogEmptyContent: 'Este registro está actualmente vacío porque no se ha registrado ninguna operación.',
        operationLogSuccess: 'Operación completa',
        operationLogIncomplete: 'Operación completada con excepciones',
        operationLogCancelled: 'Operación cancelada por',
        operationLogFailed: 'Operación fallida',
        cancelOperation: 'Cancelar operación',
        undoOperation: 'Deshacer operación'
    },
    filterPane: {
        anyLevelLabel: 'Cualquier nivel',
        selectLabel: 'Seleccionar',
        filterSuffix: 'ver',
        headingTitle: 'Filtros',
        applyButtonText: 'Aplicar',
        discardChangesText: 'Restablecer filtros',
        applyButtonTooltipText: 'Añada filtros y complete todas las entradas para habilitarlas.',
        resetButtonTooltipText: 'Restablecer los filtros a su estado original.',
        showMoreButtonText: 'Mostrar más',
        maxDateRangeMessage: 'Máximo de 5 rangos de fechas',
        startDateLabel: 'Inicio',
        endDateLabel: 'Fin',
        fromDateLabel: 'Desde',
        toDateLabel: 'Hasta',
        searchLabel: 'Buscar',
        searchToSelectLabel: 'Buscar para seleccionar',
        textOperatorLabel: 'Como',
        loadingLabel: 'cargando',
        betweenLabel: 'entre',
        clearFiltersText: 'Borrar filtros',
        removeFilterText: 'Eliminar filtro',
        notLoadedLabel: 'no cargado',
        notLoadedValueLabel: 'valor no cargado',
        levelText: 'Nivel',
        levelsText: 'Niveles',
        resetFilterText: 'Restablecer todos',
        clearAllFiltersText: 'Borrar todo',
        numberResults: '${rowCount} resultados',
        allLabel: 'Todo',
        yesLabel: 'Sí',
        noLabel: 'No',
        addLabel: 'Añadir ${fieldAlias}',
        removeLabel: 'Eliminar ${fieldAlias}',
        removeFilterButtonLabel: 'Borrar el rango de fechas de ${startDate} a ${endDate}',
        typeHereLabel: 'Escribir aquí',
        hiddenFiltersMessage: 'Algunos filtros no se pudieron aplicar. Esto puede deberse a sus permisos de usuario o a un campo/valor no válido. Al guardar este espacio de trabajo',
        hiddenFiltersBoldMessage: 'se borrará cualquier filtro oculto.',
        operators: {
            DB_OPERATORS: {
                LESS_THAN: 'Menos que',
                LESS_THAN_OR_EQUAL: 'Menos que o igual a',
                EQUALS: 'es igual a',
                GREATER_THAN_OR_EQUAL: 'Mayor que o igual a',
                GREATER_THAN: 'Mayor que',
                LIKE: 'Como',
                CONTAINS: 'Contiene'
            },
            NUMERIC_OPERATORS_ALIAS: {
                LessThan: 'menos que',
                LessThanOrEqual: 'como máximo',
                Equals: 'igual a',
                GreaterThanOrEqual: 'al menos',
                GreaterThan: 'más que'
            },
            NUMERIC_PARAMETER_OPERATORS_ALIAS: {
                LessThan: 'menos que',
                LessThanOrEqual: 'como máximo',
                Equals: 'igual a',
                GreaterThanOrEqual: 'al menos',
                GreaterThan: 'más que'
            },
            TEXT_OPERATORS_ALIAS: {
                Like: 'contiene'
            },
            MULTY_VALUES_OPERATORS_ALIAS: {
                Contains: 'es uno de'
            },
            DATE_OPERATORS_ALIAS: {
                LessThanOrEqual: 'hasta',
                GreaterThanOrEqual: 'desde'
            },
            DATE_SENSITIVE_OPERATORS_ALIAS: {
                GreaterThanOrEqual: 'al menos',
                LessThanOrEqual: 'como máximo'
            },
            SKILL_OPERATORS_ALIAS: {
                Equals: 'es'
            },
            CHECKBOX_OPERATORS_ALIAS: {
                Equals: 'en'
            },
            BOOLEAN_OPERATORS_ALIAS: {
                Equals: 'es'
            }
        },
        advancedFilterOperators: {
            TEXT: {
                Contains: 'Contiene',
                IsBlank: 'Está vacío',
                IsNotBlank: 'No está vacío'
            },
            NUMERIC: {
                Is: 'Es',
                IsNot: 'No es',
                IsMoreThan: 'Es mayor de',
                IsLessThan: 'Es menor de',
                IsBlank: 'Está vacío',
                IsNotBlank: 'No está vacío'
            },
            BOOLEAN: {
                Is: 'Es',
                IsNot: 'No es'
            },
            SKILL: {
                Is: 'Es',
                IsNot: 'No es',
                IsBlank: 'Está vacío',
                IsNotBlank: 'No está vacío'
            }
        },
        logicalOperators: {
            and: 'Y',
            or: 'O'
        },
        uiFilterOperators: {
            Is: 'es',
            IsNot: 'no es',
            IsMoreThan: 'es mayor de',
            IsLessThan: 'es menor de',
            Contains: 'contiene',
            UpTo: 'es menor o igual',
            From: 'es mayor o igual',
            IsNotBlank: 'no está vacío',
            IsBlank: 'está vacío',
            IsMoreOrEqual: 'es más o igual que',
            IsLessOrEqual: 'es menos o igual que'
        },
        filterFieldMessages: {
            resource_has_skill_resource_skill_levelAlias: 'Habilidades',
            resource_guidAlias: 'Nombre del recurso',
            availabilityAlias: 'Disponibilidad',
            utilisationAlias: 'Uso',
            departmentAlias: 'resource_current_department_guidDepartment',
            resource_manager_resource_guidAlias: 'Gerente directo',
            resource_location_guidAlias: 'Ubicación',
            resource_localgrade_guidAlias: 'Grado',
            resource_resourcetype_guidAlias: 'Tipo de empleo',
            resource_rolenameAlias: 'Cargo',
            resource_booking_countAlias: 'Recuento de reserva',
            'Charge RateAlias': 'Tasa de cargo',
            job_guidAlias: 'Nombre del trabajo',
            job_startAlias: 'Inicio del trabajo',
            job_endAlias: 'Fin del trabajo',
            job_client_guidAlias: 'Cliente',
            job_engagementlead_resource_guidAlias: 'Gerente directo',
            job_location_guidAlias: 'Ubicación del trabajo',
            job_jobstatus_guidAlias: 'Estado del trabajo',
            job_codeAlias: 'Código de referencia del trabajo',
            job_chargetype_guidAlias: 'Tipo de cargo',
            booking_is_assignedAlias: 'Sin asignar',
            booking_startAlias: 'Inicio de la reserva',
            booking_endAlias: 'Fin de la reserva',
            booking_bookingtype_guidAlias: 'Estado de la reserva',
            booking_notesAlias: 'Notas de la reserva',
            booking_workactivity_guidAlias: 'Actividad del trabajo',
            updatedonAlias: 'actualizado el',
            updatedbyAlias: 'actualizado por el recurso',
            createdonAlias: 'creado el',
            createdbyAlias: 'creado por el recurso'
        }
    },
    talentProfilePage: {
        profileTitle: 'Mi perfil',
        shareProfileCaption: 'ES_Share profile_ES',
        uploadLabel: 'Cargar documentos',
        changeProfielPictureText: 'Cambiar imagen del perfil',
        viewOtherProfile: 'Ver otro perfil',
        viewMyProfile: 'Ver mi perfil',
        cMeProfileTitle: 'Perfil C-Me',
        editDetailsLabel: 'ES_Edit details_ES',
        updateAvatarWindowMessages: {
            headerTitle: 'Cargar imagen del perfil',
            applyBtnTitle: 'Aplicar',
            removeBtnTitle: 'Eliminar imagen',
            cancelBtnTitle: 'Cancelar',
            uploadAreaText: 'Haga clic o arrastre el archivo a esta zona para cargarlo',
            fileTypesString: 'Tipos de archivos: ',
            dragControlLabel: 'Arrastrar',
            zoomControlLabel: 'Acercar/Alejar'
        },
        recommendationTitle: 'Recomendacion',
        recommendationAlertHeader: 'Actualice sus habilidades para que le tengan en cuenta',
        recommendationAlertDescription: 'A continuación le ofrecemos sugerencias de habilidades que puede añadir a su perfil',
        skillApproval: 'ES_Your changes will be sent to manager for approval. Changes to skill preferences do not require approval._ES',
        additionalDetailsSectionTitle: 'Detalles adicionales',
        messages: {
            fileTooLargeLabel: 'Fallo al cargar el documento: el archivo es demasiado grande',
            fileTypeForbiddenLabel: 'Se ha producido un fallo al cargar el documento: el tipo de archivo no está permitido',
            noFilesUploadedLabel: 'No tiene ningún documento cargado',
            uploadsLimitReachedLabel: 'Límite de documentos alcanzado: elimine documentos para subir más'
        },
        uploadDocumentsWindowMessages: {
            headerTitle: 'ES_Upload document_ES',
            uploadBtnTitle: 'ES_Upload_ES',
            cancelBtnTitle: 'ES_Cancel_ES',
            uploadAreaText: 'ES_Click or drag file to this area to upload_ES',
            fileTypesString: 'ES_Accepted formats: _ES',
            documentType: 'ES_Type of document_ES',
            expiryDate: 'ES_Expiry date_ES',
            maxFileSize: 'ES_Max ${maxFileSize} MB per file_ES'
        }
    },
    prompts: {
        createOrSaveAsNewPlanPrompt: {
            title: 'Nuevo plan',
            placeholder: 'Insertar nombre del plan',
            helpMessage: 'Proporcione un nombre para su plan',
            okText: 'Crear Plan',
            cancelText: 'Cancelar',
            name: 'nombre',
            type: 'Tipo',
            access: 'Acceso',
            editAccess: 'Editar acceso',
            readOnlyAccess: 'Solo lectura',
            subHeading: 'Guardar como un plan privado o público nuevo',
            privatePlan: 'plan privado',
            publicPlan: 'plan público',
            newPlanLabel: 'Nuevo plan',
            maxLengthValidationMessage: 'Máximo ${maxNameLength} símbolos permitidos',
            switchOnLabel: 'Activar'
        },
        createOrSaveAsNewWorkspacePrompt: {
            title: 'ES_New workspace_ES',
            placeholder: 'ES_Insert workspace name_ES',
            helpMessage: 'ES_Please provide a name for your workspace_ES',
            okText: 'Crear Plan',
            cancelText: 'Cancelar',
            name: 'nombre',
            type: 'Tipo',
            access: 'Acceso',
            editAccess: 'Editar acceso',
            readOnlyAccess: 'Solo lectura',
            subHeading: 'ES_Save as a new private or public workspace_ES',
            privatePlan: 'ES_Private workspace_ES',
            publicPlan: 'ES_Public workspace_ES',
            maxLengthValidationMessage: 'Máximo ${maxNameLength} símbolos permitidos',
            newPlanLabel: 'ES_New Workspace_ES',
            switchOnLabel: 'Activar'
        },
        extendJobRangePrompt: {
            okText: 'Sí, modificar fechas del trabajo',
            cancelText: 'No, cancelar movimiento',
            title: '¿Extender el trabajo?',
            message: '¿Desea extender el periodo de trabajo para permitir el movimiento de esta reserva?',
            detailsText: 'Nuevas fechas de trabajo para'
        },
        deleteBookingPrompt: {
            title: 'Borrar',
            message: '¿Desea borrar este',
            okText: 'Sí, borrar el',
            cancelText: 'No, conservar el',
            noEntityDescriptionPrefix: 'No',
            noEntityDescriptionSuffix: 'descripción'
        },
        makePlanPrivatePrompt: {
            okText: 'Hacer plan privado',
            cancelText: 'Conservar el plan público',
            makeString: 'Hacer',
            privatePlanString: 'un plan privado',
            planTypeMessageStart: 'Actualmente, este plan es',
            messageFirstPart: 'Si hace este plan privado,',
            messageBold: 'ya no estará disponible para otros.'
        },
        makeWorkspacePrivatePrompt: {
            okText: 'ES_Make workspace private_ES',
            cancelText: 'ES_Keep workspace public_ES',
            makeString: 'Hacer',
            privatePlanString: 'ES_a private workspace_ES',
            planTypeMessageStart: 'ES_This workspace is currently_ES',
            messageFirstPart: 'ES_If you make this workspace private,_ES',
            messageBold: 'ya no estará disponible para otros.'
        },
        deletePlanPrompt: {
            okText: 'Sí, borrar el plan',
            cancelText: 'No, conservar el plan',
            deleteString: 'Borrar',
            planString: 'plan',
            workspaceTypeMessage: 'Actualmente, este plan es',
            publicDeleteMessageStart: 'Si borra este plan',
            publicDeleteMessageEnd: 'ya no estará disponible para los demás',
            question: '¿Desea borrar este plan permanentemente?'
        },
        deleteWorkspacePrompt: {
            okText: 'ES_Yes, delete workspace_ES',
            cancelText: 'ES_No, keep workspace_ES',
            deleteString: 'Borrar',
            planString: 'ES_workspace_ES',
            workspaceTypeMessage: 'ES_This workspace is currently_ES',
            publicDeleteMessageStart: 'ES_If you delete this workspace_ES',
            publicDeleteMessageEnd: 'ya no estará disponible para los demás',
            question: 'ES_Do you wish to permanently delete this workspace?_ES'
        },
        deleteRoleTemplatePrompt: {
            okText: 'Sí, borrar plantilla',
            cancelText: 'No, conservar plantilla',
            warning: 'Esta acción no se puede deshacer.',
            question: '¿Desea borrar permanentemente esta plantilla ${rolerequestSingularLowerAlias}?',
            title: '¿Borrar plantilla ${roleTemplateDescription}?'
        },
        renameRoleTemplatePrompt: {
            okText: 'Cambiar nombre de la plantilla',
            cancelText: 'Conservar el nombre antiguo',
            question: 'Le ha cambiado el nombre a la plantilla. ¿Desea guardar este cambio?',
            title: 'Cambiar nombre de la plantilla',
            fromText: 'de ',
            toText: 'a '
        },
        deleteClientPrompt: {
            title: '¿Borrar cliente?',
            message: 'Al borrar este cliente se eliminará permanentemente de todo el sistema, incluyendo todos los trabajos asociados. No será posible asignar este cliente a futuros trabajos. \n ¿Desea continuar?',
            okText: 'Sí, borrar el cliente',
            cancelText: 'No, conservar el cliente'
        },
        deleteJobPrompt: {
            title: 'Borrar',
            okText: 'Borrar',
            cancelText: 'Conservar',
            thereString: 'Hay',
            isString: 'un',
            areString: 'varios',
            andString: 'y',
            onString: 'en',
            thisString: 'este',
            theseString: 'estos',
            betweenString: 'entre',
            onThisJobString: 'en este trabajo',
            withString: 'con',
            deletingThisJobWillAlsoDeleteString: 'Al borrar este trabajo también se borrará',
            doYouWishToPermanentlyDeleteString: '¿Desea borrar permanentemente',
            messages: {
                deleteJobTitle: '¿Borrar ${jobDescription}?',
                deleteJobLabel: '¿Borrar ${jobAlias}, ${bookingAlias}, ${roleGroupAlias} y ${roleRequestAlias}?'
            }
        },
        deleteRolePrompt: {
            title: 'Borrar',
            message: '¿Desea borrar este',
            okText: 'Sí, borrar el',
            cancelText: 'No, guardar el ',
            noEntityDescriptionPrefix: 'No',
            noEntityDescriptionSuffix: 'descripción',
            defaultRoleName: 'Nuevo rol',
            noRoleGroupSetLabel: 'No se ha ajustado ningún ${rolerequestgroupSingularLowerAlias}'

        },
        removeRolePublicationPrompt: {
            title: 'Eliminar ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias}',
            message: 'Hay ${countOfResources}${resourcePluralLowerAlias} aplicadas a este ${rolerequestSingularLowerAlias}. Al eliminar el ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias}también se eliminarán sus solicitudes.',
            question: '¿Desea eliminar ${rolePublicationDescription} de ${roleBoardPageAlias}?',
            confirmation: '¿Eliminar solicitudes ${resourcePluralLowerAlias} y ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias}?',
            okText: 'Eliminar ${rolerequestSingularLowerAlias}',
            cancelText: 'Conservar ${rolerequestSingularLowerAlias}',
            defaultRoleName: 'Nuevo rol'
        },
        unsavedChangesPrompt: {
            message: 'Hay cambios sin guardar en esta página. ¿Desea salir?',
            saveLabel: 'Guardar los cambios',
            discardLabel: 'Descartar los cambios',
            cancelLabel: 'Cancelar'
        },
        renamePlanPrompt: {
            okText: 'Cambiar el nombre del plan',
            cancelText: 'Conservar el nombre antiguo',
            title: 'Cambiar el nombre del plan',
            message: 'Ha cambiado el nombre del plan. ¿Desea guardar este cambio?',
            oldNamePrefix: 'desde',
            newNamePrefix: 'hasta'
        },
        renameWorkspacePrompt: {
            okText: 'ES_Rename workspace_ES',
            cancelText: 'Conservar el nombre antiguo',
            title: 'ES_Rename workspace_ES',
            message: 'ES_You have renamed the workspace. Do you wish to save this change?_ES',
            oldNamePrefix: 'desde',
            newNamePrefix: 'hasta'
        },
        saveChangesPrompt: {
            okText: 'Continuar sin guardar',
            cancelText: 'Cancelar',
            title: 'Cambios no guardados',
            message: 'es un',
            planSuffix: 'plan',
            saveAsPrivatePlanButtonLabel: 'Guardar como privado',
            saveChangesToPublicButtonLabel: 'Guardar cambios como público'
        },
        saveWorkspaceChangesPrompt: {
            okText: 'Continuar sin guardar',
            cancelText: 'Cancelar',
            title: 'Cambios no guardados',
            message: 'es un',
            planSuffix: 'ES_workspace_ES',
            saveAsPrivatePlanButtonLabel: 'Guardar como privado',
            saveChangesToPublicButtonLabel: 'Guardar cambios como público'
        },
        deleteResourceSkillPrompt: {
            okText: 'Sí, eliminar la habilidad',
            cancelText: 'No, conservar la habilidad',
            selectedSkill: 'Habilidad seleccionada',
            title: '¿Eliminar la habilidad?',
            messagePrefix: '¿Desea eliminar ',
            messageSuffix: ' del perfil?'
        },
        deleteCommentPrompt: {
            okText: 'Borrar comentario',
            cancelText: 'Guardar comentario',
            title: '¿Borrar comentario?'
        },
        singleCreateBookingErrorPrompt: {
            title: 'Error al crear ${entitySingularLower}',
            message: 'Hubo un error al crear la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleCreateJobErrorPrompt: {
            title: 'Error al crear ${entitySingularLower}',
            message: 'Hubo un error al crear la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleCreateClientErrorPrompt: {
            title: 'Error al crear ${entitySingularLower}',
            message: 'Hubo un error al crear la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleCreateRolegroupErrorPrompt: {
            title: 'Error al crear ${entitySingularLower}',
            message: 'Hubo un error al crear la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singlePublishRoleErrorPrompt: {
            title: 'Error al publicar ${entitySingularLower}',
            message: 'Se produjo un error al publicar el siguiente ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para publicar este ${entitySingularLower} en ${marketplaceAlias}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar detalles de publicación',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleEditRolePublicationErrorPrompt: {
            title: 'Error al editar la publicación ${entitySingularLower}',
            message: 'Se produjo un error al publicar la siguiente publicación de ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar esta publicación de ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleRemoveRolePublicationErrorPrompt: {
            title: 'Error al eliminar la publicación de ${entitySingularLower}',
            message: 'Se produjo un error al eliminar la siguiente publicación de ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para eliminar esta publicación de ${entitySingularLower} de  ${marketplaceAlias}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleEditBookingErrorPrompt: {
            title: 'Error al editar ${entitySingularLower}',
            message: 'Hubo un error al editar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleEditJobErrorPrompt: {
            title: 'Error al editar ${entitySingularLower}',
            message: 'Hubo un error al editar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleEditClientErrorPrompt: {
            title: 'Error al editar ${entitySingularLower}',
            message: 'Hubo un error al editar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleEditResourceErrorPrompt: {
            title: 'Error al editar el perfil',
            message: 'Hubo un error al editar el siguiente perfil:',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar este perfil',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar perfil',
            discardButtonLabel: 'Descartar perfil'
        },
        singleSaveTemplateErrorPrompt: {
            title: 'Error al crear la plantilla ${entitySingularLower}',
            message: 'Hubo un error al crear la siguiente plantilla ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear la plantilla  ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar plantilla ${entitySingularLower}',
            defaultRoleTemplateName: 'Nueva ${entitySingularUpper}'
        },
        singleDeleteErrorPrompt: {
            title: 'Error al borrar la plantilla ${entitySingularLower}',
            message: 'Se produjo un error al borrar la siguiente plantilla de ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para borrar la plantilla ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar plantilla ${entitySingularLower}',
            defaultRoleTemplateName: 'Nueva ${entitySingularUpper}'
        },
        singleEditErrorPrompt: {
            title: 'Error al editar la plantilla ${entitySingularLower}',
            message: 'Se produjo un error al editar la siguiente plantilla de ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar la plantilla ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar plantilla ${entitySingularLower}',
            defaultRoleTemplateName: 'Nueva ${entitySingularUpper}'
        },
        singleEditRolegroupErrorPrompt: {
            title: 'Error al editar ${entitySingularLower}',
            message: 'Hubo un error al editar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para editar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleDeleteBookingErrorPrompt: {
            title: 'Error al borrar ${entitySingularLower}',
            message: 'Hubo un error al borrar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para borrar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleDeleteJobErrorPrompt: {
            title: 'Error al borrar ${entitySingularLower}',
            message: 'Hubo un error al borrar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para borrar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleDeleteClientErrorPrompt: {
            title: 'Error al borrar ${entitySingularLower}',
            message: 'Hubo un error al borrar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para borrar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        singleDeleteRolegroupErrorPrompt: {
            title: 'Error al borrar ${entitySingularLower}',
            message: 'Hubo un error al borrar la siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para borrar esta ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            editButtonLabel: 'Editar ${entitySingularLower}',
            discardButtonLabel: 'Descartar ${entitySingularLower}'
        },
        carouselCreateErrorPrompt: {
            title: 'Error al editar ${entityString} (${failedCount})',
            successfulCountMessage: 'Se editaron correctamente ${succeededCount} de ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Permisos insuficientes para editar esta ${entityString}',
            retry: 'Volver a intentarlo',
            errorSectionMessage: 'Hubo un error al editar la siguiente ${entityString}',
            edit: 'Editar ${entityString}',
            cancel: 'Descartar ${entityString}',
            close: 'Cerrar'
        },
        carouselRollForwardCreateErrorPrompt: {
            title: 'Error al crear ${entityString} (${failedCount})',
            successfulCountMessage: 'Creado con éxito ${succeededCount} de ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Permisos insuficientes para crear este ${entityString}',
            retry: 'Intentar nuevamente',
            errorSectionMessage: 'Hubo un error al crear el siguiente ${entityString}',
            edit: 'Opciones de Retroceder \'Avanzar\'',
            cancel: 'Eliminar los erróneos ${entityString}',
            close: 'Cerrar'
        },
        carouselEditErrorPrompt: {
            title: 'Error al editar ${entityString} (${failedCount})',
            successfulCountMessage: 'Se editaron correctamente ${succeededCount} de ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Permisos insuficientes para editar esta ${entityString}',
            retry: 'Volver a intentarlo',
            errorSectionMessage: 'Hubo un error al editar la siguiente ${entityString}',
            edit: 'Editar ${entityString}',
            cancel: 'Descartar ${entityString}',
            close: 'Cerrar'
        },
        carouselDeleteErrorPrompt: {
            title: 'Error al borrar ${entityString} (${failedCount})',
            successfulCountMessage: 'Se borraron correctamente ${succeededCount} de ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Permisos insuficientes para borrar esta ${entityString}',
            retry: 'Volver a intentarlo',
            errorSectionMessage: 'Hubo un error al borrar la siguiente ${entityString}',
            edit: 'Editar ${entityString}',
            cancel: 'Descartar ${entityString}',
            close: 'Cerrar'
        },
        batchedCreateEntityErrorPrompt: {
            successfulCountMessage: 'Se crearon correctamente ${succeededCount} de ${attemptedCount} ${entityString}.',
            insufficientPermissionsMessage: 'Permisos insuficientes para crear esta ${entityString}',
            title: 'Error al crear ${entityString}',
            errorSectionMessage: 'Hubo un error al crear la siguiente ${entityString}',
            retryButtonLabel: 'Volver a intentarlo',
            cancelButtonLabel: 'Cancelar',
            editEntitiesButtonLabel: 'Editar ${entityString}',
            discardEntitiesButtonLabel: 'Descartar ${entityString}',
            closeDialogButtonLabel: 'Cerrar',
            tryAgainMessage: '¿Desea volver a intentarlo?',
            requests: 'solicitudes'
        },
        deleteMultipleBookingsPrompt: {
            title: '¿Borrar ${bookingsAliasUpper}? (${bookingsCount})',
            message: '¿Desea borrar ${pronounString} ${bookingsAliasLower}?',
            okText: 'Sí, borrar el ${bookingsAliasUpper}',
            cancelText: 'No, conservar el ${bookingsAliasUpper}',
            close: 'Cerrar',
            theseString: 'estos',
            thatString: 'que'
        },
        cantPasteBarPrompt: {
            message: 'Seleccione una celda en la que pegar su ${barAliasLower}'
        },
        deleteMultipleRolerequestsPrompt: {
            title: '¿Borrar ${rolerequestsAliasUpper}? (${rolerequestsCount})',
            message: '¿Desea borrar ${pronounString} ${rolerequestsAliasLower}?',
            okText: 'Sí, borrar el ${rolerequestsAliasUpper}',
            cancelText: 'No, conservar el ${rolerequestsAliasUpper}',
            close: 'Cerrar',
            theseString: 'estos',
            thatString: 'que'
        },
        moveRolerequestTimeAllocationPrompt: {
            title: 'Mover pendiente desde${pluralFieldName} ${rolerequestDescription}',
            message: 'Cualquier pendiente solicitada se moverá a un nuevo "Borrador" , y este  se establecerá como "Activo".${pluralFieldName}${rolerequestsSingularLower}${rolerequestsSingularLower} Los asignados que estén "Solicitados" se desasignarán. Esta acción no se puede deshacer.',
            warningMessage: 'Esta acción no se puede deshacer.',
            okText: 'Mover pendiente${pluralFieldName}',
            cancelText: 'Mantener la solicitud abierta',
            close: 'Cerrar',
            FTEs: 'FTE'
        },
        removeRolerequestTimeAllocationPrompt: {
            title: 'Eliminar pendiente de${pluralFieldName} ${rolerequestDescription}',
            message: 'Cualquier pendiente solicitada se eliminará, y el  se ajustará a "Activo".${pluralFieldName}${rolerequestsSingularLower} Los asignados que estén "Solicitados" se desasignarán. Esta acción no se puede deshacer.',
            warningMessage: 'Esta acción no se puede deshacer.',
            okText: 'Eliminar pendiente ${pluralFieldName}',
            cancelText: 'Mantener la solicitud abierta',
            close: 'Cerrar',
            FTEs: 'FTE'
        },
        createRolegroupModal: {
            placeholder: 'Nuevo ${roleGroupAlias}',
            title: 'Crear ${roleGroupAlias}',
            nameDescriptor: 'Nombre',
            createLabel: 'Crear',
            cancelLabel: 'Cancelar',
            currentValue: '${jobDescription} ${roleGroupAlias} ${currentSubsequentNumber}',
            helpMessage: 'Proporcione un nombre para su ${roleGroupAlias}',
            maxLengthValidationMessage: '${maxNameLength} símbolos permitidos como máximo'
        },
        saveAsTemplateModal: {
            placeholder: 'Nueva plantilla ${roleAlias}',
            title: 'Nueva plantilla',
            headerTitle: 'Nombro y guarde la nueva plantilla ${roleAlias}.',
            nameDescriptor: 'Nombrar',
            createLabel: 'Guardar',
            cancelLabel: 'Cancelar',
            currentValue: '${rolerequestDescription}',
            defaultNewRole: 'Nueva ${roleAlias}',
            helpMessage: 'Escriba un nombre para su plantilla ${rolerequestDescription}',
            maxLengthValidationMessage: 'Se permiten ${maxNameLength} símbolos como máximo'
        },
        progressRolesWindow: {
            title: 'Error al avanzar ${roleAlias}'
        },
        deleteRoleGroupPrompt: {
            title: '¿Borrar ${roleGroupDescription}?',
            roleGroupInfoMessage: 'Hay <bold>${rolesNumber}</bold> ${roleAliasPlural} incluyendo <bold>${roleRequests}</bold> solicitudes en este ${roleGroupAliasSingular} entre el <bold>${roleStartDate}</bold> - <bold>y el ${roleEndDate}</bold>. Al borrar este ${roleGroupAliasSingular} también se borrarán estos ${roleAliasPlural} y las solicitudes.',
            shouldDeleteQuestion: '¿Desea borrar permanentemente <bold>${roleGroupDescription}</bold>?',
            checkboxText: 'Borrar ${roleGroupAliasSingular}, ${roleAliasPlural} y las solicitudes.',
            cancelMessage: 'Conservar ${roleGroupAliasSingular}',
            confirmMessage: 'Borrar ${roleGroupAliasSingular}'
        },
        extendJobRangeDetailsPagePrompt: {
            okText: 'Sí, modificar fechas del trabajo',
            cancelText: 'No, cancelar programación',
            title: '¿Extender el trabajo?',
            message: '¿Desea extender el periodo del trabajo para permitir la programación de',
            roleTailMessage: 'esta función?',
            rolesTailMessage: 'estas funciones?',
            detailsText: 'Nuevas fechas de trabajo para'
        },
        singleMoveResourceRolegroupErrorPrompt: {
            title: 'Error al mover pendiente ${resourcePluralLower}',
            message: 'Se ha producido un error al mover lo siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para mover esto ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleRemoveResourceRolegroupErrorPrompt: {
            title: 'Error al eliminar pendiente ${resourcePluralLower}',
            message: 'Se ha producido un error al eliminar lo siguiente ${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para mover esto ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleMoveFTERolegroupErrorPrompt: {
            title: 'Error al mover FTE pendientes',
            message: 'Se ha producido un error al mover los FTE pendientes para los siguientes :${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para mover esto ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        singleRemoveFTERolegroupErrorPrompt: {
            title: 'Error al eliminar los FTE pendientes',
            message: 'Se ha producido un error al eliminar los FTE pendientes para los siguientes :${entitySingularLower}',
            insufficientPermissionsMessage: 'Permisos insuficientes para mover esto ${entitySingularLower}',
            retryButtonLabel: 'Volver a intentarlo',
            discardButtonLabel: 'Desechar ${entitySingularLower}'
        },
        updateRolerequestStatusWindow: {
            title: 'Error al actualizar el estado de ${roleAlias}'
        },
        publishRoleErrorPrompt: {
            title: 'Error al publicar ${roleAlias}'
        },
        editRolePublicationPrompt: {
            title: 'Error al editar la publicación ${roleAlias}'
        },
        withdrawRoleApplicationPrompt: {
            title: 'Confirmar retirada',
            question: 'Ya no se le considerará para esto ${entitySingularLower}.',
            warning: '¿Confirma que desea retirar su solicitud?',
            okText: 'Sí, retirar mi solicitud',
            cancelText: 'No, deseo conservar mi solicitud'
        },
        tableViewHoursValidationPrompt: {
            title: 'Horas no válidas',
            message: 'Puedes reservar entre 0 y 168 horas semanales.',
            okText: 'Aceptar',
            tooltipText: 'Pulse Esc para cerrar'
        },
        tableViewCellEditErrornPrompt: {
            title: 'Error al guardar los cambios',
            errorMessage: 'Actualizar la página ${tableViewPageAlias} puede requerir una combinación de creación, edición y eliminación de ${bookingPluralForm} en segundo plano.',
            contactMessage: 'Póngase en contacto con su administrador para asegurarse de que tiene los permisos necesarios.',
            discardText: 'Desechar los cambios',
            retryText: 'Volver a intentarlo'
        },
        splitBookingsErrorPrompt: {
            title: 'Error al guardar los cambios',
            question: 'Puede que el ${bookingSingularLowerAlias} seleccionado haya sido eliminado o editado por otra persona, o que no tenga permisos suficientes. Dividir ${bookingPluralLowerAlias} exige una combinación de creación y edición de ${bookingPluralLowerAlias} en segundo plano.',
            warning: 'Actualice la página e inténtelo de nuevo, o póngase en contacto con su administrador.',
            cancelText: 'Aceptar'
        },
        setPasswordConfirmationPrompt: {
            title: 'Es necesaria una confirmación',
            question: 'Al cambiar la contraseña se invalidarán las integraciones existentes. ¿Seguro que desea continuar?',
            okText: 'Sí, seguro.',
            cancelText: 'No, volver.'
        },
        duplicateJobErrorPrompt: {
            title: 'Error al guardar los cambios',
            errorMessage: 'Esto puede deberse a una o más de las siguientes razones:',
            suggestedActions: [
                'Este ${jobSingularLowerAlias} o no tiene ${bookingPluralLowerAlias} en la gama de datos seleccionada',
                'La cola de duplicación ${jobSingularLowerAlias} está llena',
                'No tiene permisos suficientes para la opción seleccionada${bookingPluralLowerAlias}, ya que para duplicar ${jobSingularLowerAlias} es necesario crear ${bookingPluralLowerAlias} en segundo plano'
            ],
            contactMessage: 'Actualice la página e inténtelo de nuevo, o póngase en contacto con su administrador.',
            okText: 'Aceptar'
        },
        duplicateRoleGroupErrorPrompt: {
            title: 'Error en la creación ${rolerequestPluralLowerAlias}',
            errorMessage: 'No tiene permisos suficientes para crear estos ${rolerequestPluralLowerAlias}.',
            contactMessage: 'Actualice la página e inténtelo de nuevo, o póngase en contacto con su administrador.',
            okText: 'Volver a intentarlo',
            cancelText: 'Cancelar'
        },
        deleteOperationLogPrompt: {
            title: '¿Desea borrar los elementos creados en esta operación?',
            shouldDeleteQuestion: '¿Desea eliminar permanentemente los elementos creados en esta operación? Si se crearon nuevos trabajos, también se eliminarán las reservas, escenarios o funciones creados en estos trabajos.',
            checkboxText: 'Eliminar los elementos creados en esta operación',
            cancelMessage: 'Conservar elementos',
            confirmMessage: 'Eliminar elementos'
        },
        confirmMassDuplicatePrompt: {
            title: 'Duplicar',
            message: 'Su operación ha sido puesta en cola. Puedes seguir su estado en el Operaciones.',
            closeText: 'OK'
        },
        updateSeriesBookingPrompt: {
            message: 'Se van a borrar estas reservas y se van a volver a crear con datos actualizados.',
            okText: 'Continuar',
            cancelText: 'Cancelar'
        }
    },
    comments: {
        editedFlag: 'editado',
        editButtonLabel: 'Editar',
        deleteButtonLabel: 'Borrar',
        confirmEditButtonLabel: 'Actualizar',
        cancelButtonLabel: 'Cancelar',
        createButtonLabel: 'Añadir comentario',
        createPlaceholder: 'Comience a escribir un comentario',
        showMoreButtonLabel: 'Mostrar más comentarios'
    },
    navigation: {
        title: 'Ayuda',
        contactSupport: 'Contactar con el servicio de asistencia',
        helpPageLink: 'Documentos de ayuda',
        keyboardShortcuts: 'Atajos del teclado',
        legend: 'Leyenda',
        operationLogButtonLabel: 'Operaciones',
        notifications: 'Notificaciones',
        settings: 'Configuración',
        logout: 'Cerrar sesión'
    },
    skills: {
        noAddedSkills: 'No se ha añadido ninguna habilidad',
        noRecommendations: 'No hay nuevas recomendacion',
        recommendationTitle: 'Recomendacion',
        mySkillsLabel: 'ES_My skills_ES',
        approvalRequestsLabel: 'ES_Approval requests_ES',
        approvalRequestSent: 'ES_Approval request sent_ES',
        noSkillPendingRequestsLabel: 'ES_No skill update requests_ES',
        noSkillApprovalHistoryLabel: 'ES_No skill approval historic requests_ES',
        skillApprovalHistoryLabel: 'ES_Historic requests are automatically removed after 1 year_ES',
        skillTagLabel: 'Etiqueta',
        skillCategoryLabel: 'Categorías',
        defaultSelectedSection: 'Todos los tipos de habilidades',
        noMatchingSkillsText: 'No se encontraron habilidades.',
        searchPlaceholder: 'Buscar una habilidad',
        saveButtonLabel: 'Añadir habilidades',
        cancelButtonLabel: 'Cancelar',
        headerTitle: 'Añadir habilidades',
        primarySaveButtonLabel: 'Guardar',
        skillsToAddSectionName: 'Habilidades para añadir',
        addSkillsButtonLabel: 'Añadir habilidades',
        editSkillsButtonLabel: 'Editar habilidades',
        skillsSectionTitle: 'Habilidades',
        expandAllCaption: 'Expandir todo',
        collapseAllCaption: 'Contraer todo',
        singularSkillString: 'habilidad',
        pluralSkillsString: 'habilidades',
        markDeletedMessage: 'Marcado para borrarlo. Se borrará cuando guarde los cambios.',
        cancelDeletionMessage: 'Cancelar borrado',
        skillLevelDeletedMessage: 'Se ha borrado el nivel de esta habilidad. Debe ajustarse un nuevo nivel.',
        validationRequiredText: 'es obligatorio',
        validationLessThanText: 'no puede ser menos de',
        validationGreaterThanText: 'no puede ser más de',
        validationIntegerNumberText: 'debe ser un número entero',
        maxCharactersPrefix: 'Máximo',
        maxCharactersSuffix: 'caracteres',
        tagsPrefixText: 'Etiquetas:',
        markedForDeletionMessage: 'Marcado para borrarlo. Se borrará cuando guarde los cambios.',
        deleteLabel: 'Borrar ${skillName}',
        cancelDeletionSkillLabel: 'Cancelar borrado de ${skillName}',
        noValueMessage: 'No se ha ajustado ningún ${fieldInfoAlias}',
        insufficientPermissionsToEditSuffix: 'Permisos insuficientes para editar',
        searchSkillFilterCascaderPlaceholder: 'ES_Select skills and levels_ES',
        noManagerToApproveMessage:'ES_You do not have manager to approve your skills_ES'
    },
    pages: {
        plannerPage: 'Planes',
        adminSettings: 'Ajustes',
        jobsPage: 'Trabajos',
        talentProfile: 'Perfil del talento',
        translation: 'Mi perfil',
        report: 'Informe',
        logout: 'Cerrar sesión',
        collapseText: 'Contraer',
        expandText: 'Expandir',
        errorMessages: {
            goBackText: 'Volver a la última página',
            goToText: 'Ir a',
            errorCodeMessagePrefix: 'Error',
            defaultHeaderText: 'Se ha producido un error',
            closeText: 'Cerrar',
            '401': {
                headerText: 'Sin autorización.',
                message: 'Su sesión ha caducado. Vuelva a iniciar sesión.'
            },
            '403': {
                headerText: 'Acceso denegado.',
                message: 'Permisos insuficientes.'
            },
            '404': {
                headerText: 'Lo sentimos.',
                message: 'No encontramos la página que busca.'
            },
            error: {
                headerText: 'Lo sentimos.',
                message: 'Parece que se ha producido un error.',
                errorCodeMessage: 'Se ha producido un problema. Vuelva a cargar la página.'
            },
            calculateFteError: {
                headerText: 'No se ha podido calcular el FTE',
                message: 'Seleccione un diario de referencia de FTE válido para este trabajo.'
            }
        }
    },
    manageEntityLookupWindow: {
        searchForLabel: 'Buscar',
        showLessLabel: 'Mostrar menos',
        showMoreLabels: {
            prefix: 'Mostrar',
            suffix: 'más'
        },
        noResultsMessagePrefix: 'No',
        noResultsMessageSuffix: 'se encontró con este nombre.'
    },
    contextualMenu: {
        createEntity: 'Crear',
        editEntity: 'Editar',
        deleteEntity: 'Borrar',
        copyEntity: 'Copiar',
        rollForwardEntity: 'Duplicar',
        cutEntity: 'Cortar',
        pasteEntity: 'Pegar',
        clearEntity: 'Borrar',
        setDateRange: 'Ajustar rango de fecha',
        loadingCaption: '...cargando',
        restart: 'Reiniciar',
        archive: 'Archivar',
        reject: 'Rechazar',
        makeLive: 'Activar',
        submitRequest: 'Enviar solicitud',
        rollForwardTooltipText: 'Copiar el ${bookingEntityAlias} seleccionado en otro ${jobEntityAlias} u otra fecha',
        unassignResource: 'Desasignar de ${rolerequestSingularLowerAlias}',
        createCriteriaRole: 'Crear ${rolerequestSingularCapitalAlias} por requisitos',
        createRoleByName: 'Crear ${rolerequestSingularCapitalAlias} por nombre',
        movePendingResources: 'Mover pendiente ${resourcePluralLowerAlias}',
        removePendingResources: 'Eliminar pendiente ${resourcePluralLowerAlias}',
        movePendingFTEs: 'Mover FTE pendientes',
        removePendingFTEs: 'Eliminar FTE pendientes',
        manageBudget: 'Gestionar presupuesto',
        saveAsTemplate: 'Guardar como plantilla',
        showInViewLabel: 'Mostrar en vista ${pluralViewNameAlias}'
    },
    detailsPane: {
        paneLabel: 'panel',
        inModalLabel: 'panel en Modal',
        showPanePrefixLabel: 'Mostrar',
        showPaneSuffixLabel: 'panel',
        suggestionsSingular: 'Sugerencia',
        suggestionsPlural: 'Sugerencias'
    },
    blankValues: {
        notFoundString: 'no encontrado',
        dateNotFoundString: 'Fecha no encontrada',
        noChargeTypeSetString: 'No se ha establecido un tipo de cargo',
        unspecifiedString: 'sin especificar',
        noString: 'No',
        setString: 'establecido'
    },
    hotKeysHelpWindow: {
        generalLabel: 'General',
        helpWindowTitle: 'Atajos del teclado',
        bookingsLabel: '${bookingPluralCapitalAlias}',
        jobsLabel: '${jobPluralCapitalAlias}',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        rolesLabel: '${rolerequestPluralCapitalAlias}',
        dateManipulationLabel: 'Manipulación de fecha',
        menusLabel: 'Menús',
        filtersLabel: 'Filtros',
        viewOptionsLabel: 'Ver opciones',
        createLabel: 'Crear',
        helpLabel: 'Ayuda',
        helpDocumentationLabel: 'Documentos de ayuda',
        orLabel: 'O',
        plannerPage: {
            addLabel: 'Añadir',
            editLabel: 'Editar',
            cutLabel: 'Cortar',
            copyLabel: 'Copiar',
            pasteLabel: 'Pegar',
            deleteLabel: 'Borrar',
            restartLabel: 'Reiniciar',
            archiveLabel: 'Archivar',
            rejectLabel: 'Rechazar',
            makeLiveLabel: 'Activar',
            submitRequestLabel: 'Enviar solicitud',
            expandDateRangeLabel: 'Expandir rango de fecha',
            reduceDateRangeLabel: 'Reducir rango de fecha',
            setRangeTo5DayLabel: 'Ajustar rango de fecha a 5 días',
            setRangeTo7DaysLabel: 'Ajustar rango de fecha a 7 días',
            setRangeTo10DaysLabel: 'Ajustar rango de fecha a 10 días',
            setRangeTo2WeekLabel: 'Ajustar rango de fecha a 2 semanas',
            setRangeTo4WeeksLabel: 'Ajustar rango de fecha a 4 semanas',
            setRangeTo6WeeksLabel: 'Ajustar rango de fecha a 6 semanas',
            setRangeTo2MonthsLabel: 'Ajustar rango de fecha a 2 meses',
            setRangeTo3MonthsLabel: 'Ajustar rango de fecha a 3 meses',
            setRangeTo6MonthsLabel: 'Ajustar rango de fecha a 6 meses',
            setRangeTo1YearLabel: 'Ajustar rango de fecha a 1 año',
            goToTodayLabel: 'Ir a hoy',
            addMenuLabel: 'Añadir menú',
            editMenuLabel: 'Editar menú',
            viewMenuLabel: 'Ver menú',
            addJobLabel: 'Añadir un ${jobSingularLowerAlias}',
            findResourcesLabel: 'Buscar ${resourcePluralLowerAlias}',
            defaultDensityLabel: 'Ajustar densidad de muestra a predeterminada',
            mediumDensityLabel: 'Ajustar densidad de muestra a mediana',
            expandedDensityLabel: 'Ajustar densidad de muestra a expandida',
            helpWindowLabel: 'Ventana de ayuda',
            openLegendLabel: 'Abrir leyenda de colores',
            showHideWeekendsLabel: 'Mostrar/ocultar fines de semana',
            showHidePotentialConflictsLabel: 'Mostrar/Ocultar conflictos potenciales',
            addRoleByName: 'Añadir ${rolerequestSingularCapitalAlias} por nombre',
            addRoleByRequirements: 'Añadir ${rolerequestSingularCapitalAlias} por requisitos',
            editRoleByNameLabel: 'Editar por nombre${rolerequestSingularCapitalAlias}',
            editRoleByCriteriaLabel: 'Editar por requisitos${rolerequestSingularCapitalAlias}',
            rollForwardLabel: 'Duplicar',
            movePendingResources: 'Mover recursos pendientes',
            removePendingResources: 'Eliminar recursos pendientes',
            movePendingFTEs: 'Mover FTE pendientes',
            removePendingFTEs: 'Eliminar FTE pendientes',
            splitBookingLabel: 'Dividir ${bookingSingularLowerAlias}',
            showInViewLabel: 'Mostrar en vista ${jobPluralLowerAlias}/${resourcePluralLowerAlias}',
            dragToSelect: 'Drag to select'
        },
        jobsPage: {
            addLabel: 'Añadir',
            editLabel: 'Editar',
            deleteLabel: 'Borrar',
            addMenuLabel: 'Añadir menú',
            editMenuLabel: 'Editar menú',
            compactDensityLabel: 'Ajustar densidad de muestra a compacta',
            defaultDensityLabel: 'Ajustar densidad de muestra a predeterminada',
            expandedDensityLabel: 'Ajustar densidad de muestra a expandida',
            helpWindowLabel: 'Ventana de ayuda'
        },
        roleInboxPage: {
            movePendingResources: 'Mover recursos pendientes',
            removePendingResources: 'Eliminar recursos pendientes',
            makeLiveLabel: 'Activar',
            submitRequestLabel: 'Enviar solicitud',
            restartLabel: 'Reiniciar',
            rejectLabel: 'Rechazar',
            deleteLabel: 'Borrar',
            archiveLabel: 'Archivar',
            addRoleByName: 'Añadir ${rolerequestSingularLowerAlias} por nombre',
            addRoleByRequirements: 'Añadir ${rolerequestSingularLowerAlias} por requisitos',
            editRoleByNameLabel: 'Editar por nombre${rolerequestSingularCapitalAlias}',
            editRoleByCriteriaLabel: 'Editar  por requisitos${rolerequestSingularCapitalAlias}',
            helpWindowLabel: 'Ventana de ayuda',
            publishToMarketplaceLabel: 'Publicar en el ${marketplaceAlias}'
        },
        adminSettingsPage: {
            addNewItemLabel: 'Añadir nuevo elemento',
            helpWindowLabel: 'Ventana de ayuda'
        },
        createScenarioLabel: 'Crear ${rolerequestgroupSingularLowerAlias}'
    },
    validationMessages: {
        unableToSaveChanges: 'No se han podido guardar los cambios. Revise los errores resaltados.',
        mandatoryFieldsNotCompleted: 'Hay que rellenar los campos obligatorios',
        formHasErrors: 'Este formulario tiene errores',
        activeUserText: 'está inactivo. Ajustar a un recurso activo o no asignado para guardar los cambios',
        fieldMandatoryText: 'Este campo es obligatorio',
        mandatoryText: 'es obligatorio',
        minimumText: 'Mínimo',
        maximiumText: 'Máximo',
        maxCharactersPrefix: 'Máximo',
        maxCharactersSuffix: 'caracteres',
        selectValidText: 'Seleccione uno válido',
        invalidNamePrefix: 'No válido',
        invalidNameSuffix: 'nombre',
        integerTypeText: 'es de tipo entero',
        maxSelectedYearText: 'El año seleccionado debe ser antes de 10000',
        minSelectedYearText: 'El año seleccionado debe ser después de 0000',
        startDateInvalid: 'Se ha proporcionado una fecha de inicio no válida',
        jobEndBeforeJobStart: 'El fin no puede estar antes del inicio',
        fteMaxValidationText: 'El FTE total supera la cantidad solicitada',
        fteString: 'FTE'
    },
    attachmentsMessages: {
        uploadButtonLabel: 'Cargar documentos',
        deleteButtonLabel: 'Borrar',
        fileTooLargeLabel: 'Fallo al cargar el documento: el archivo es demasiado grande',
        fileTypeForbiddenLabel: 'Se ha producido un fallo al cargar el documento: el tipo de archivo no está permitido',
        noFilesUploadedLabel: 'No tiene ningún documento cargado',
        uploadsLimitReachedLabel: 'Límite de documentos alcanzado: elimine documentos para subir más',
        allowedFormatsLabel: 'Los documentos pueden tener los siguientes formatos ${formattedAcceptedFileTypes}',
        maxFileSizeLabel: 'Hay un máximo de ${defaultMaxFileSizeMb}MB por cada documento',
        maxUploadsAllowed: 'Puede subir un máximo de ${defaultMaxUploadsAllowed} documentos'
    },
    treeSelectionMessages: {
        chargeMode: 'Cambiar modo',
        revenue: 'Ingresos',
        cost: 'Coste',
        profit: 'Beneficios',
        dateRange: 'Rango de fechas',
        timeAllocation: 'Asignación de tiempo',
        selectFieldsCaption: 'Seleccionar campos',
        addButtonCaption: 'Añadir',
        historyFieldsSuffix: '(sobrescribir desde la fecha)'
    },
    contextualDropdown: {
        detailsLabel: 'detalles',
        editLabel: 'Editar',
        duplicateLabel: 'Duplicar',
        viewLabel: 'Ver',
        newLabel: 'Nuevo',
        deleteLabel: 'Borrar',
        roleByName: '${roleSingularCapitalAlias} por nombre',
        roleByRequirements: '${roleSingularCapitalAlias} por requisitos',
        newRoleLabel: 'Nuevo ${roleSingularLowerAlias}',
        editDetailsLabel: 'Editar detalles',
        archiveLabel: 'Archivar',
        restartLabel: 'Reiniciar',
        rejectLabel: 'Rechazar',
        makeLiveLabel: 'Activar',
        submitRequestLabel: 'Enviar solicitud',
        createLabel: 'Crear',
        unassignLabel: 'Desasignar de',
        movePendingFTE: 'Mover FTE pendientes',
        removePendingFTE: 'Eliminar FTE pendientes',
        movePendingResourcesLabel: 'Mover pendiente ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Eliminar pendiente ${resourcePluralLowerAlias}',
        createBookingEllipsisLabel: 'Crear ${bookingSingularCapitalAlias}...',
        createRoleByNameEllipsisLabel: 'Crear ${rolerequestSingularCapitalAlias}...',
        editEllipsisLabel: 'Editar...',
        goToProfileEllipsisLabel: 'Ir al perfil',
        copyProfileUrlEllipsisLabel: 'Copiar URL del perfil',
        manageBudgetLabel: 'Gestionar presupuesto',
        saveAsTemplateLabel: 'Guardar como plantilla',
        publishToMarketplaceLabel: 'Publicar en ${marketplaceAlias}',
        editRolePublicationButtonLabel: 'Editar publicación ${rolerequestSingularLowerAlias}',
        removeRolePublicationButtonLabel: 'Eliminar publicación ${rolerequestSingularLowerAlias}',
        detailsJobLabel: '${jobSingularCapitalAlias} detalles',
        editJobLabel: 'Editar ${jobSingularLowerAlias}',
        duplicateJobLabel: 'Duplicar ${jobSingularLowerAlias}',
        viewRoleRequestGroupLabel: 'Comparar ${rolerequestgroupSingularLowerAlias}',
        newRoleRequestGroupLabel: 'Crear ${rolerequestgroupSingularLowerAlias}',
        detailsResourceLabel: '${resourceSingularCapitalAlias} detalles',
        openLabel: 'Abrir',
        moreOptionsButtonLabel: 'Más opciones'
    },
    progressRolesWindow: {
        totalText: 'Cambio total al trabajo',
        cancelText: 'Cancelar',
        progressRoleLabel: 'Avanzar rol',
        jobLabel: 'Trabajo',
        roleLabel: 'Función',
        dateRangeLabel: 'Rango de fechas',
        budgetLabel: 'Presupuesto',
        makeLive: {
            selectMessage: 'Seleccionar ${rolePluralAlias} para avanzar a ${bookingPluralAlias}',
            title: 'Activar',
            submitText: 'Activar'
        },
        submitRequest: {
            selectMessage: 'Seleccione ${rolePluralAlias} para solicitar como ${bookingPluralAlias}',
            title: 'Enviar solicitud',
            submitText: 'Enviar solicitud'
        }
    },
    progressRoleErrors: {
        alreadyLiveMsg: 'Ya está activo',
        noPermissionsMsg: 'Permisos insuficientes'
    },
    rejectRolesWindow: {
        title: 'Rechazar',
        submitText: 'Rechazar solicitud',
        cancelText: 'Cancelar',
        rejectErrorMessage: 'Se ha producido un problema. No se pudo completar la acción',
        buttonLabel: 'Cerrar',
        rejectReasonText: 'Seleccionar razón del rechazo de esta solicitud',
        errorDialogTitle: 'Error en transición de función',
        customReasonPlaceholderText: 'Escriba el motivo personalizado para el rechazo de esta función',
        jobLabel: 'Trabajo',
        roleLabel: 'Función',
        dateRangeLabel: 'Rango de fechas',
        budgetLabel: 'Presupuesto',
        statusLabel: 'Estado'
    },
    carousel: {
        defaultRoleName: 'Nuevo ${rolerequestSingularCapitalAlias}',
        ungrouped: 'No se ha ajustado ningún ${rolerequestgroupSingularLowerAlias}'
    },
    rollForwardDialog: {
        title: 'Duplicar ${bookingEntityAlias}',
        submitText: 'Crear ${bookingEntityAlias}',
        cancelText: 'Cancelar',
        duplicateBooking: '${bookingSingularCapitalAlias} duplicada',
        duplicateBookings: '${bookingPluralLowerAlias} duplicadas',
        forwardOptions: {
            alertMessage: 'Copiando ${noOfBooking} ${bookingEntityAlias}',
            destinationStartDateLabel: 'Fecha de inicio de destino',
            destinationStartDateLabelError: 'La fecha de inicio de destino es obligatoria',
            destinationStartDateLabelErrorDescription: 'Las posiciones relativas de los ${bookingEntityAlias} seleccionados se mantendrán después de la duplicación',
            destinationJobLabel: '${jobSingularAlias} de destino',
            destinationJobError: 'El ${jobSingularAlias} de destino es obligatorio',
            destinationBookingTypeLabel: 'Tipo de ${bookingEntityAlias} de destino',
            destinationBookingTypeError: 'El tipo de ${bookingEntityAlias} de destino es obligatorio',
            destinaltionJobExplanation: 'Los rangos de destino de ${jobSingularAlias} se modificarán en consecuencia',
            offsetExplanation: 'Desde el inicio del primer seleccionado ${bookingEntityAlias}',
            editBookingLabel: 'Editar ${bookingEntityAlias} después del duplicado',
            editBookingDescription: 'Abre el diálogo de edición para realizar cambios adicionales en el nuevo ${bookingEntityAlias}',
            valuePostfix: '${bookingEntityAlias}',
            keepBookingTypeText: 'Conservar el tipo de ${bookingEntityAlias} como está',
            onPrefix: 'El',
            inPrefix: 'En'
        }
    },
    repeatBookingDialog: {
        createRepeatBooking: {
            title: 'Establecer recurrencia',
            submitText: 'Guardar',
            cancelText: 'Cancelar',
            repeatEvery: 'Repetir cada',
            repeatUntil: 'Hasta',
            noRepeatText: 'No se repite',
            positiveWholeNumberErrorMessage: 'Introduzca un número entero positivo'
        },
        editRepeatBooking: {
            title: '¿Qué reservas recurrentes desea modificar?',
            selectedOnly: 'Solo la reserva seleccionada',
            selectedAndFuture: 'Reservas seleccionadas y siguientes',
            allBookings: 'Todas las reservas de la serie',
            actionLabel: 'Modificar serie',
            singleBookingMessage: 'Está modificando una sola reserva de una serie.',
            singleAndFutureBookingsMessage: 'Estás editando esta y todas las reservas siguientes en una serie recurrente.',
            allBookingsMessage: 'Está modificando todas las reservas de una serie recurrente.',
            partOfSeriesMessage: 'Esta reserva forma parte de una serie recurrente.',
            updateFailureMessage: 'No se ha podido actualizar la serie de reservas.',
            bulkBookingMessage: 'Está modificando ocurrencias únicas de reservas de una serie recurrente.',
            editedSingleBookingMessage: 'Esta reserva forma parte de una serie recurrente, pero se ha modificado aparte. Puede que algunos datos sean diferentes.'
        },
        deleteRepeatBooking: {
            title: '¿Qué reservas recurrentes desea borrar?',
            cannotBeUndone: 'Esto no se puede deshacer.',
            selectedOnly: 'Solo la reserva seleccionada',
            selectedAndFuture: 'Reservas seleccionadas y siguientes',
            allBookings: 'Todas las reservas de la serie'
        },
        doesNotRepeatText: 'no se repite',
        repeatsEveryText: 'se repite cada',
        on: 'el',
        starting: 'desde',
        until: 'hasta',
        intervalText: {
            day: 'día',
            days: 'días',
            week: 'semana',
            weeks: 'semanas',
            month: 'mes',
            months: 'meses'
        },
        dayOfWeekText: {
            0: 'domingo',
            1: 'lunes',
            2: 'martes',
            3: 'miércoles',
            4: 'jueves',
            5: 'viernes',
            6: 'sábado'
        },
        dayText: 'día',
        confirmRepeatBookingPrompt: {
            title: 'Creando reservas recurrentes',
            message: 'Se ha puesto su operación en la cola. Puede consultar su estado en el registro de operaciones.',
            closeText: 'Aceptar'
        },
        auditTrail: {
            recurringIntervalCreated: 'Se ha creado una serie de recurrencia para que se repita cada',
            recurringIntervalEdited: 'Se ha modificado un intervalo de recurrencia para que se repita cada',
            recurrentSeries: 'Serie recurrente'
        },
        seriesText: 'serie'
    },
    jobDuplicateDialog: {
        title: 'Duplicar ${jobSingularLowerAlias} ',
        submitText: 'Crear ${jobSingularLowerAlias}',
        cancelText: 'Cancelar',
        newPrefix: 'Nuevo',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        searchToSelect: 'Buscar para seleccionar',
        changeRangeToIncludeBookingsString: 'Cambiar rango para incluir todo ${bookingPluralLowerAlias}?',
        forwardOptions: {
            destinationStartDateLabel: 'Fecha de inicio de destino',
            destinationStartDateLabelError: 'La fecha de inicio de destino es obligatoria',
            destinationStartDateLabelErrorDescription: 'Las posiciones relativas de los ${bookingPluralLowerAlias} seleccionados se conservarán',
            outOfRangeEntityExplanation: 'Parece que algunos ${bookingPluralLowerAlias} se han quedado fuera de la fecha de inicio y final de este ${jobSingularLowerAlias}.',
            rolesPositionWarning: 'Los ${rolerequestPluralLowerAlias} existentes permanecerán en el ${jobSingularLowerAlias} seleccionado y no se duplicarán',
            jobRangeLabelError: 'El intervalo de fechas del ${jobSingularLowerAlias} de destino es obligatorio',
            maximumJobRangeMessage: 'El rango de fechas del ${jobSingularCapitalAlias} debe estar dentro de los 24 meses',
            dateRangeValueMandatory: 'Este campo es obligatorio',
            destinationJobLabel: 'Destino ${jobSingularLowerAlias}',
            destinationJobError: 'El destino ${jobSingularLowerAlias} es obligatorio',
            destinationBookingTypeLabel: 'Tipo de destino ${bookingSingularLowerAlias}',
            destinaltionJobExplanation: 'Los rangos de ${jobSingularLowerAlias} de destino se modificarán en consecuencia.',
            offsetExplanation: 'Desde el comienzo del primer ${bookingSingularLowerAlias} en el rango de fechas',
            dateRangeForJobLabel: 'Intervalo de fechas para el ${jobSingularLowerAlias} seleccionado',
            selectedJobLabel: '${jobSingularLowerAlias} seleccionado',
            destinationBookingTypeError: 'El tipo de destino ${bookingSingularLowerAlias} es obligatorio',
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Conservar el tipo de ${bookingEntityAlias} como está',
            onPrefix: 'El',
            inPrefix: 'En'
        }
    },
    roleGroupDuplicateDialog: {
        title: 'Duplicar ${rolerequestgroupSingularLowerAlias}',
        submitText: 'Duplicar ${rolerequestgroupSingularLowerAlias}',
        cancelText: 'Cancelar',
        forwardOptions: {
            scenarioNameLabel: '${rolerequestgroupSingularCapitalAlias} nombre',
            destinationJobLabel: 'Destino ${jobSingularLowerAlias}',
            destinationJobError: 'El destino ${jobSingularLowerAlias} es obligatorio',
            destinationStartDateLabel: 'Fecha de inicio de destino',
            destinationStartDateLabelError: 'La fecha de inicio de destino es obligatoria',
            destinationStartDateLabelErrorDescription: 'Las posiciones relativas de ${rolerequestPluralLowerAlias} se conservarán después de la duplicación.',
            destinaltionStartDateExplanation: 'desde el comienzo del primer ${rolerequestSingularLowerAlias} en el ${rolerequestgroupSingularLowerAlias}',
            newRoleGroupDescriptionLabel: 'Descripción',
            onPrefix: 'El',
            inPrefix: 'en',
            scenarioNameError: 'Este campo es obligatorio'
        }
    },
    peopleFinderDialog: {
        createBookingText: 'Crear ${bookingEntityAlias}',
        createRoleText: 'Crear ${roleEntityAlias} por nombre',
        closeText: 'Cerrar',
        filterTitle: 'Añadir filtros',
        infoToolTipText: 'Rango de fecha activo en el plan',
        refreshButtonLabel: 'Actualizar',
        profileUrlCopied: 'Profile URL copied',
        plannerPage: {
            title: 'Find ${resourceEntityAlias}',
            emptyFinderText: 'Select criteria to find ${resourceEntityAlias}',
            resourceFoundInRangeText: '${resourcePluralCapitalAlias} found during <bold>${startDate} - ${endDate}</bold> will be shown here.',
            summaryText: '<bold>${resourceCount} results</bold> for range'
        },
        profilePage: {
            title: 'View other profile',
            emptyFinderText: 'Select criteria to find profile',
            summaryText: '<bold>${resourceCount} results</bold>'
        }
    },
    jobFilterDialog: {
        filterTitle: 'Añadir filtros',
        placeholderLabel: 'Buscar por nombre de ${jobAlias}...',
        modalTitle: 'Seleccionar un ${jobAlias}',
        resultSingular: '${rowCount} resultado',
        resultPlural: '${rowCount} resultados',
        upToResults: 'Hasta ${rowCount} resultados'
    },
    //mass duplicate settings
    massDuplicateJobs: {
        filterTitle: 'Añadir filtros',
        placeholderLabel: 'Buscar por nombre ${jobAlias}...',
        modalTitle: 'Seleccionar un ${jobAlias}',
        resultSingular: '${rowCount} resultado',
        resultPlural: '${rowCount} resultados',
        upToResults: 'Hasta ${rowCount} resultados',
        massDuplicateJobsTitle: 'Duplicar datos',
        massDuplicateJobsFieldTitle: 'Seleccionar trabajos para duplicación masiva',
        massDuplicateJobsSubLabel: 'prueba',
        saveButtonLabel: 'Duplicar trabajos',
        cancelButtonLabel: 'Borrar',
        formHasErrorsMessage: 'Este formulario contiene errores',
        massDuplicateJobsInfoText: 'Filtre los trabajos que desea duplicar.',
        massDuplicateJobsInfoTips1: 'Las <b>funciones</b> de los trabajos seleccionados no se duplicarán',
        massDuplicateJobsInfoTips2: 'Se mantendrán las fechas relativas de los trabajos y reservas; por ejemplo, una reserva que comience 3 días después de \nla fecha de inicio del trabajo se creará 3 días después de la fecha de inicio del nuevo trabajo',
        massDuplicateJobsInfoTips3: 'Los trabajos con un <i>Siguiente trabajo relacionado</i> válido <b>no se duplicarán</b>. Solo se copiarán sus reservas \nal trabajo relacionado.',
        massDuplicateJobsInfoTips4: 'Las reservas asignadas a <b>recursos inactivos</b> se duplicarán y permanecerán asignadas a ellos',
        massDuplicateJobsTextNewBooking: 'Crear nuevas reservas basadas en',
        massDuplicateJobsNewBookingTextBookings: 'Reservas',
        massDuplicateJobsNewBookingTextActuals: 'Datos reales',
        massDuplicateJobsNewBookingTipMessage: 'Crea reservas semanales a partir de los datos de la plantilla horaria',
        massDuplicateJobsTextDestinationBooking: 'Tipo de reserva de destino',
        massDuplicateJobsDestinationOption1: 'Reservas planificadas',
        massDuplicateJobsDestinationOption2: 'Reservas no confirmadas',
        massDuplicateJobsDestinationOption3: 'Mantener el tipo de reserva como está',
        massDuplicateJobsDaterangeText: 'Seleccionar trabajos y reservas entre',
        massDuplicateJobsNewJobsText: ' Crear nuevos trabajos',
        forwardOptions: {
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Conservar el tipo de ${bookingEntityAlias} como está',
            chooseHowJobsCreatedTitle: 'Elija cómo se crean los trabajos y las reservas',
            chooseHowJobsCreatedTitleExtra: 'En los trabajos con un <i>Siguiente trabajo relacionado</i>, las nuevas fechas de inicio de reserva se desplazarán desde la \nfecha de inicio del nuevo trabajo en la misma cantidad que en el trabajo original',
            dateRangeForJobLabel: 'Trabajos iniciados entre',
            fieldMandatoryText: 'Este campo es obligatorio',
            createNewJobsLabel: 'Crear nuevos trabajos',
            inPrefix: 'En',
            onPrefix: 'El',
            inPrefixExtra: 'Desde el inicio de la primera reserva en el \nintervalo de fechas seleccionado',
            onPrefixExtra: 'Los nuevos trabajos comienzan en esta fecha. Las posiciones de reserva \nse mantienen en relación con la fecha de inicio.',
            createNewBookingsLabel: 'Crear nuevas reservas basadas en',
            bookings: 'Reservas',
            actuals: 'Datos reales',
            destinationBookingTypeLabel: 'Tipo de reserva de destino',
            plannedBookings: 'Reservas planificadas',
            unconfirmedBookings: 'Reservas no confirmadas',
            keepBookingType: 'Mantener el tipo de reserva como está',
            nextRelatedJobsLabel: 'Solo se duplican trabajos con un Siguiente trabajo relacionado válido',
            newJobNamesLabel: 'Nombres de nuevos trabajos',
            explanationNewJobNames: 'Si el trabajo original tiene seleccionado un Siguiente trabajo relacionado válido, el nombre del trabajo no se modificará',
            newJobsNamesDefaultOption: 'Por defecto',
            newJobsNamesDefaultOptionExample: 'Ejemplo: Copia de Aqua Audit 2023',
            newJobsNamesOriginalOption: 'Usar el nombre original',
            newJobsNamesReplaceOption: 'Sustituir',
            newJobsNamesReplaceWithOption: 'por',
            newJobsNamesReplaceOptionExample: 'Ejemplo: Aqua Audit 2023 se convierte en Aqua Audit 2024',
            newJobsNamesCantFindTextToReplaceLabel: 'Si no se encuentra el texto a sustituir, se utilizará el nombre por defecto (Copia de...)'
        },
        massDuplicateJobsReviewTitle: 'Resumen: ${totalNumberOfJobs} trabajos seleccionados para prorrogar',
        massDuplicateJobsReviewPoint1: '${totalNumberOfBookings} total de reservas seleccionadas',
        massDuplicateJobsReviewPoint2: '${totalNumberOfConfirmedHours} horas confirmadas',
        massDuplicateJobsReviewPoint3: '${totalNumberOfUnconfirmedHours} horas no confirmadas',
        massDuplicateJobsReviewPoint4: '${totalNumberOfJobsWithoutNextJob} se crearán nuevos trabajos',
        massDuplicateJobsReviewPoint5: '${totalNumberOfJobsWithNextJob} En los trabajos con un Siguiente trabajo relacionado se prorrogarán sus reservas o datos reales',
        massDuplicateJobsReviewSummaryButtonLabel: 'Actualizar resumen'
    },
    operationsLogDialog: {
        heading: 'Registro de operaciones',
        dataGridExplanations: 'Registra las operaciones llevadas a cabo en nuestro sitio'
    },
    expandedOperationsLogDialog: {
        heading: 'Duplicación de trabajos',
        jobsRollForwarded: 'trabajos prorrogados',
        bookingsCreated: 'total de reservas creadas',
        newJobsCreated: 'nuevos trabajos creados',
        jobsDuplicated: 'En los trabajos con un Siguiente trabajo relacionado solo se han prorrogado sus reservas o datos reales'
    },
    actionBarWithFooterButtons: {
        saveButtonLabel: 'Añadir habilidades',
        cancelButtonLabel: 'Cancelar',
        formHasErrorsMessage: 'Este formulario tiene errores'
    },
    cMeSection: {
        title: 'Rasgos de C-me'
    },
    educationSection: {
        formConfiguration: {
            education: 'Educación',
            dialogConfig: {
                saveText: 'Guardar',
                cancelText: 'Cancelar'
            },
            institutionLabel: 'Institución',
            institutionError: 'La institución es obligatoria',
            fieldLabel: 'Campo',
            fieldError: 'El campo es obligatorio',
            degreeLabel: 'Grado',
            noResultsFoundMessage: 'Su administrador no ha establecido opciones de grado',
            startDateLabel: 'Fecha de inicio',
            endDateLabel: 'Fecha de fin',
            endFieldError: 'La fecha de fin no puede ser anterior a la fecha de inicio',
            endDateDescription: 'La fecha de fin puede referirse al mes/año de graduación previsto',
            detailsLabel: 'Detalles',
            addInstitutionPlaceholder: 'Añadir institución',
            addFieldPlaceholder: 'Añadir campo',
            addDegreePlaceholder: 'Añadir grado',
            addDetailsPlaceholder: 'Añadir detalles',
            maxCharErrorPrefix: 'Máximo',
            maxCharErrorSuffix: 'caracteres permitidos'
        },
        addEducationButtonLabel: 'Añadir educación',
        editEducationButtonLabel: 'Editar educación'
    },
    experienceSection: {
        formConfiguration: {
            experience: 'Experiencia',
            companyLabel: 'Empresa',
            roleLabel: 'Puesto',
            locationLabel: 'Ubicación',
            startDateLabel: 'Fecha de inicio',
            endDateLabel: 'Fecha de fin',
            detailsLabel: 'Detalles',
            endDateFieldError: 'La fecha de fin no puede ser anterior a la fecha de inicio',
            roleError: 'El puesto es obligatorio',
            companynameError: 'El nombre de la empresa es obligatorio',
            maxCharErrorPrefix: 'Máximo',
            maxCharErrorSuffix: 'caracteres permitidos',
            addDetailsPlaceholder: 'Añadir detalles',
            addCompanyPlaceholder: 'Añadir empresa',
            addRolePlaceholder: 'Añadir puesto',
            addLocationPlaceholder: 'Añadir ubicación',
            dialogConfig: {
                saveText: 'Guardar',
                cancelText: 'Cancelar'
            }
        },
        addExperienceButtonLabel: 'Añadir experiencia',
        editExperienceButtonLabel: 'Editar experiencia'
    },
    cookieConsentBanner: {
        accept: 'Aceptar',
        title: 'Este sitio web utiliza cookies',
        info: 'Utilizamos cookies para mejorar su experiencia y para fines analíticos. Al hacer clic en "Aceptar" da su consentimiento al uso de estas cookies. Para obtener más información sobre cómo utilizamos las cookies y sobre sus derechos de privacidad, lea nuestra <cookie>Política de cookies</cookie> y la <privacy>Política de privacidad</privacy>.'
    },
    banners: {
        maintenanceStatusBanner: {
            dismissLabel: 'Descartar',
            singleDayInfo: '<bold>Mantenimiento programado el ${startDate}, de ${startTime} a ${endTime}</bold>. Retain no estará disponible temporalmente  mientras mejoramos nuestro sitio.',
            multiDayInfo: '<bold>Mantenimiento programado el ${startDate} a las ${startTime} hasta el ${endDate} a las ${endTime}</bold>. Retain no estará disponible temporalmente  mientras mejoramos nuestro sitio.'
        },
        jobsPageBookmarkBanner: {
            dismissLabel: 'ES_Dismiss_ES',
            description: 'ES_We have changed the URL of this page. Update your bookmark if needed_ES'
        }
    },
    lastLoginSection: {
        minutesAgo: '${timeAgo}Hace m',
        hoursAgo: '${timeAgo}Hace h',
        daysAgo: '${timeAgo}Hace d',
        now: 'Ahora',
        lastLoginLabel: 'Último inicio de sesión'
    },
    longRunningTaskBanners: {
        duplicateJob: {
            processing: {
                title: '\'${jobDescription}\' ${progress} % duplicado finalizado...',
                content: {
                    message: 'Le notificaremos cuando estemos preparados.',
                    progressSeparator: 'de'
                }
            },
            completed: {
                title: 'Su ${jobSingularLowerAlias} está listo',
                content: {
                    message: 'Se ha duplicado \'${jobDescription}\' correctamente.'
                }
            },
            failed: {
                title: 'No se pudo duplicar ${jobSingularLowerAlias}',
                content: {
                    message: 'No se ha podido duplicar \'${jobDescription}\'.',
                    retry: 'Volver a intentarlo'
                }
            },
            queued: {
                title: 'Su ${jobSingularLowerAlias} duplicado se ha puesto en cola',
                content: {
                    message: 'Le notificaremos cuando estemos preparados.',
                    progressSeparator: 'de'
                }
            },
            multiple: {
                title: 'Su duplicado ${jobSingularLowerAlias} se ha puesto en cola',
                content: {
                    message: 'Se han puesto en cola varias operaciones.',
                    button: 'Ver Registro de operaciones'
                }
            }
        },
        longRunningjobIndicatorTooltipMessage: 'Hay una operación de larga duración actualmente en curso para este ${entityAlias}.'
    },
    cMeProfiling: {
        aboutcMeColours: 'About C-me colours',
        about: 'ES_About_ES',
        cMeColourProfilingDialog: {
            dialogTitle: 'About C-me colour profiling',
            topMessageLine1: 'Human behaviours can be complicated to describe. We\'ve partnered with C-me, a behaviour profiling service that associates behaviours with colours. A resource\'s C-me data tells you about their preferred ways of doing things, expressed in the language of four colours.',
            topMessageLine2: 'We automatically update a resource\'s skills with traits from their most dominant colour.',
            redBox: {
                title: 'Red',
                boxList: [
                    'Action oriented',
                    'Assertive',
                    'Competitive',
                    'Decisive',
                    'Determined',
                    'Fast paced',
                    'Strategic'
                ]

            },
            yellowBox: {
                title: 'Yellow',
                boxList: [
                    'Dynamic presenter',
                    'Energetic',
                    'Flexible',
                    'Imaginative',
                    'Inspirational',
                    'Optimistic',
                    'Spontaneous'
                ]
            },
            greenBox: {
                title: 'Green',
                boxList: [
                    'Collaborative',
                    'Democratic',
                    'Diplomatic',
                    'Empathetic',
                    'Non-judgemental',
                    'Patient',
                    'Values driven'
                ]
            },
            blueBox: {
                title: 'Blue',
                boxList: [
                    'Analytical',
                    'Disciplined',
                    'Methodical',
                    'Organised',
                    'Precise',
                    'Systematic',
                    'Thorough'
                ]
            }
        }
    },
    summaryPage: {
        personalGreeting: 'Hola',
        customiseButtonLabel: 'Personalizar',
        doneButtonLabel: 'Listo',
        arrangeWidgetsText: 'Haga clic en los widgets y arrástrelos para reorganizarlos.',
        welcomeGreeting: 'Le damos la bienvenida a Retain',
        summaryDurationOptionLabels: {
            4: 'Próximas 4 semanas',
            6: 'Próximas 6 semanas',
            12: 'Próximas 12 semanas'
        },
        widgets: {
            yourRequests: {
                title: 'Sus solicitud',
                emptyStateMessage: 'No requests submitted by you for this time period'
            },
            plannedHours: {
                title: 'Horas planificadas',
                emptyStateMessage: ''
            },
            ongoingJobs: {
                title: 'En curso ${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            actionRequired: {
                title: '${rolerequestPluralCapitalAlias} para activar',
                emptyStateMessage: 'No hay ${rolerequestPluralLowerAlias} para activar en este periodo de tiempo'
            },
            jobsOverBudgetDetails: {
                title: '${jobPluralCapitalAlias} por encima del presupuesto',
                emptyStateMessage: 'No hay ${jobPluralLowerAlias} en curso por encima del presupuesto'
            },
            chargeableUtilisation: {
                title: 'Uso facturable',
                emptyStateMessage: ''
            },
            utilisation: {
                title: 'Uso',
                emptyStateMessage: ''
            },
            pendingRequests: {
                title: '${rolerequestPluralCapitalAlias} para activar',
                subTitleUnit: 'horas',
                emptyStateMessage: ''
            },
            unassignedBookingsDetails: {
                title: 'Sin asignar ${bookingPluralLowerAlias}',
                emptyStateMessage: 'No hay ${bookingPluralLowerAlias} sin asignar en este periodo de tiempo'
            },
            unassignedBookings: {
                title: 'Sin asignar ${bookingPluralLowerAlias}',
                subTitleUnit: '${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            upcomingBookingsDetails: {
                title: 'Próximas ${bookingPluralLowerAlias}',
                emptyStateMessage: 'No hay ${bookingPluralLowerAlias} que se le hayan asignado en este periodo de tiempo'
            }
        },
        widgetDetailsTotalsUnit: {
            bookings: '${bookingPluralLowerAlias}',
            jobs: '${jobPluralLowerAlias}',
            hours: 'horas',
            planned: 'planificadas',
            unconfirmed: 'sin confirmar',
            availability: 'disponibilidad',
            requests: 'solicitud',
            rejected: 'Rechazadas',
            requested: 'Solicitud',
            draft: 'Borrador',
            live: 'Activo'
        },
        budgetConsumedText: 'Presupuesto consumido',
        pageTitle: 'Resumen',
        configurationPane: {
            arrangeButtonLabel: 'Organizar',
            searchPlaceholder: 'Buscar',
            openOnLoginLabel: 'Abrir esta página al iniciar sesión',
            emptySearchMessage: 'No se han encontrado resultados.',
            sectionTitles: {
                personal: 'Personal',
                bookings: '${bookingPluralCapitalAlias}',
                jobs: '${jobPluralCapitalAlias}',
                resources: '${resourcePluralCapitalAlias}',
                roles: '${rolerequestPluralCapitalAlias}'
            }
        },
        explainSummaryPageTextSecurity: 'Elija widgets que los usuarios puedan añadir a la página de resumen. Los widgets personales están disponibles para todos los usuarios. \n\nLos widgets no mostrarán registros o campos ocultos para este perfil de seguridad.'
    },
    listPage: {
        pageTitle: 'ES_Lists_ES',
        workspacesMessages: {
            defaultWorkspaceLabel: 'ES_Default workspace_ES',
            newWorkspaceLabel: 'ES_New workspace_ES',
            saveChangesToPublicLabel: 'ES_Save changes to public_ES',
            saveAsNewWorkspaceLabel: 'ES_Save as a new workspace_ES',
            manageMyWorkspacesLabel: 'ES_Manage my workspaces_ES',
            privateWorkspacesLabel: 'ES_Private workspaces_ES',
            publicWorkspacesLabel: 'ES_Public workspaces_ES',
            noPublicWorkspacesCreatedLabel: 'ES_No public workspaces have been created_ES',
            noPrivateWorkspacesCreatedLabel: 'ES_No private workspaces have been created_ES'
        }
    }
};
