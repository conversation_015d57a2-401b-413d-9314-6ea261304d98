import React from 'react';
import { createSelector } from 'reselect';
import { getAvatarAltValueSelector, getAvatarImageURL } from './avatarSelectors';
import { AVATAR_SIZES, UNASSIGNED_AVATAR_ICON } from '../constants/avatarConsts';
import { PLANNER_PAGE_ALIAS, UNASSIGNED_BOOKINGS_RESOURCE, TABLE_NAMES, FILTER_FIELD_NAMES } from '../constants';
import { find, isObject } from 'lodash';
import { getFieldInfo, getFieldInfoSelector, getTableStructure } from '../selectors/tableStructureSelectors';
import { getNoDiaries, getDiaryGroup } from '../utils/diaryUtils';
import { getTranslationsSelector } from './internationalizationSelectors';
import { addCustomDynamicLogic } from '../utils/entityDynamicConfigUtils';
import { getEmptyStateByTabConfig, isExcludedSectionModule } from '../utils/entityWindowUtils';
import { getSingleRoleWindowDependantTitle, getBatchRolesWindowDependantTitle } from '../utils/entityWindowTitleUtils';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates, translateConfig } from '../utils/translationUtils';
import { JOBS_PAGE_ALIAS, JOBS_PAGE_DP_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import { getCollectionAliasForTableName, getCurrentPlannerDetailsPane } from './plannerPageSelectors';
import { getSelectedWorkspaceSettings } from './workspaceSelectors';
import { entityWindowOpen, entityWindowOpenForMultiple } from '../actions/entityWindowActions';
import { BATCH_DETAILS_PANE_PAGE_MODULES_MAP, DEFAULT_ACTIVE_EW_TAB_KEY, DETAILS_PANE_NO_ROLE_GROUPS_COINCIDENCE_STATE, DETAILS_PANE_TAB_KEYS, EMPTY_STATE, ENTITY_WINDOW_OPERATIONS, SINGLE_DETAILS_PANE_PAGE_MODULES_MAP } from '../constants/entityWindowConsts';
import { getFieldTableName, isCustomLookupOrPlanningDataField } from '../utils/tableStructureUtils';
import { CME_BLUE, CME_GREEN, CME_PERCENTAGES, CME_RED, CME_YELLOW, FIELD_LOOKUP_VALUE_TABLE, JOB_TOTAL_RAGHEALTH_GUID, RAG_FIELDS, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMERED, RESOURCE_CMEYELLOW, RESOURCE_USERSTATUS, ROLEREQUESTGROUP_FIELDS, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { getIsCustomPlanningDataField } from '../utils/fieldUtils';
import { getAddedRolesSelector, getDeletedRolesSelector, getModifiedRolesSelector, getRolesWithErrors } from './roleGroupDetailsPageSelectors';
import { ENTITY_WINDOW_MODULES } from '../constants/entityWindowConsts';
import { resourceSkillsChangesHasErrorsSelector } from './resourceSkillsSelectors';
import { ENTITY_OPTION_FIELDS, UNASSIGNED_RESOURCE_OPTION } from '../constants/plannerConsts';
import { entityOptionFieldsMap } from '../connectedComponents/connectedEntityLookupWindow';
import { getPageTableDatasSelector } from './tableDataSelectors';
import { getCurrentPageAliasSelector } from './navigationSelectors';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_DP_ALIAS } from '../constants/roleInboxPageConsts';
import { getLicenseValuesByKeySelector } from './commonSelectors';
import { findIndexOf, getData, extractProps, isEmptyObject, getArrayHasSameValues, isDetailsPane } from '../utils/commonUtils';
import { getCriteriaControlPlaceholderTextSelector, getRequirementsSectionIsReadOnlyMode, getCriteriaSectionsFieldsSelector } from './requirementsSelectors';
import { disableSectionConditionsMap } from '../utils/entityWindowConditionsUtils';
import { getIsCriteriaRole } from '../utils/roleRequestsUtils';
import { getSuggestedResourcesDataById } from './suggestedResourcesSelectors';
import { getAssigneesBudgetSectionErrorrs, getCriteriaRoleAssignedResourcesSelector, getRolesForRoleRequestGroupSelector } from './roleRequestsSelector';
import { getIsMultipleAssigneesEnabled } from './functionalityConfigurationSelectors';
import { MARKETPLACE_PAGE_ALIAS, MARKETPLACE_PAGE_DP_ALIAS } from '../constants/marketplacePageConsts';
import { entityWindowWorkHistoryPagedDataSelector } from './workHistorySelector';
import getServerinfo from '../serverInfo';
import { getPreviewEntityPageStaticMessages } from './previewEntityPageSelectors';
import { getEntitySingularAliasSelector } from './entityStructureSelectors';
import { getRootStateAssigneesDataSelector } from './roleAssigneesSelectors';
import { getEntityWindowUIEntity } from './entityWindowUiEntitySelectors';
import { getRoleGroupIdsFromSelectedRolesSelector } from './roleGroupSelectors';
import { Icon } from '../lib';
import withStatus from '../lib/icon/withStatus';
import withClassNameMap from '../lib/hocs/withClassNameMap';
import { HEALTH_ICON_STATUS_CLASSNAME } from '../lib/badges/healthBadge/healthBadgeClassNameMaps';
import withHealthTooltip from '../lib/tooltipContent/withHealthTooltip';
import { isFieldAccessHidden } from '../utils/fieldControlUtils';
import { getRepeatBookingDialogDataSelector } from './repeatBookingSelectors';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';
const sectionTypePropName = 'sectionType';

const getEntityWindowTitle = (window, settings, isPlural = false) => {
    const { moduleName, tableName } = window;
    const { titles } = settings[moduleName] || {};
    let result = {};

    if (titles && tableName) {
        result = isPlural ? titles[`${tableName}_plural`] : titles[tableName] || {};
    }

    return result;
};

function getRagHealthValues(entity, tableName, getFieldInfo, getData) {
    if (isFieldAccessHidden(getFieldInfo(tableName, RAG_FIELDS[0]))) {
        return [];
    }

    return RAG_FIELDS.map((field) => {
        const { linkTable, alias } = getFieldInfo(tableName, field);
        const fieldData = getData(linkTable, entity[field]) || {};

        return {
            label: alias,
            value: fieldData[`${linkTable}_description`]

        };
    });
}

export const getCustomTitleIconSelector = createSelector(
    (entityWindow) => entityWindow,
    (entityWindow, getFieldInfo) => getFieldInfo,
    (entityWindow, getFieldInfo, getData) => getData,
    (entityWindow, getFieldInfo, getData, iconSize) => iconSize || 'large',
    (entityWindow, getFieldInfo, getData, iconSize) => {
        const {
            tableName,
            moduleName,
            batchIds = [],
            showPluralForm = true,
            entity = {}
        } = entityWindow;

        const isPlural = showPluralForm && batchIds.length > 1;

        let CustomIcon = null;

        if (isDetailsPane(moduleName) && !isPlural) {
            if (tableName === TABLE_NAMES.JOB) {
                const totalHealthFieldInfo = getFieldInfo(tableName, JOB_TOTAL_RAGHEALTH_GUID);

                if (isFieldAccessHidden(totalHealthFieldInfo)) {
                    return CustomIcon;
                }

                const totalHealthValue = (getData(TABLE_NAMES.RAGHEALTH, entity[JOB_TOTAL_RAGHEALTH_GUID]) || {})[`${TABLE_NAMES.RAGHEALTH}_description`];

                CustomIcon = withStatus(Icon, iconSize);
                CustomIcon = withClassNameMap(
                    CustomIcon,
                    HEALTH_ICON_STATUS_CLASSNAME,
                    'status'
                );

                const healthFields = getRagHealthValues(entity, tableName, getFieldInfo, getData);

                if (healthFields.length) {
                    CustomIcon = withHealthTooltip(
                        CustomIcon,
                        healthFields
                    );
                }

                CustomIcon = <CustomIcon type={tableName} status={totalHealthValue} />;
            }
        }

        return CustomIcon;
    }
);

const getEntityWindowSections = (window, settings) => {
    const { moduleName, tableName } = window;

    return settings[moduleName].sections[tableName];
};

const getEntityWindowActions = (window, settings, moduleName) => {
    return settings[moduleName].actions[window.operation];
};

const getEntityWindowSettings = createSelector(
    (entityWindow, moduleName) => entityWindow.settings,
    (entityWindow, moduleName) => moduleName,
    (settings, moduleName) => settings[moduleName] || {}
);

const getEntityWindowEntity = createSelector(
    (entityWindow, moduleName) => entityWindow.window,
    (entityWindow, moduleName) => moduleName,
    (window, moduleName) => {
        const entityWindow = window[moduleName];
        const { isBatch } = entityWindow;

        return isBatch ? (entityWindow.windows[entityWindow.activeEntity] || {}).entity : entityWindow.entity;
    }
);

const getEntityWindowEntityById = createSelector(
    (entityWindow, _) => entityWindow.window,
    (_, moduleName) => moduleName,
    (window, moduleName) => (entityId) => {
        const entityWindow = window[moduleName];
        const { isBatch } = entityWindow;

        return isBatch ? (entityWindow.windows[entityId] || {}).entity : entityWindow.entity;
    }
);

const getEntityWindowTableName = ({ window }, moduleName) => {
    return window[moduleName].tableName;
};

const getEntityWindowAllComments = (state) => {
    return state.entityWindow.comments;
};

const getEntityWindowComments = ({ comments = {} }, moduleName) => {
    return comments[moduleName] || {};
};

const getEntityWindowPagedComments = (comments, moduleName) => {
    return getEntityWindowComments({ comments }, moduleName).pagedComments;
};

const getEntityWindowAvatarConfig = createSelector(
    state => state.avatars,
    state => getAvatarAltValueSelector(state),
    (avatars, getAvatarAltValue) => {
        return {
            getAvatarImageURL: (resourceID) => getAvatarImageURL(avatars, resourceID, AVATAR_SIZES.SMALL.label),
            getAvatarIcon: (id) => id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? UNASSIGNED_AVATAR_ICON : null,
            getAvatarAltText: (resourceName) => getAvatarAltValue(resourceName),
            entityLookupAvatarProps: {
                size: AVATAR_SIZES.SMALL.value
            }
        };
    }
);

const entityWindowHasSection = ({ entityWindow }, tableName, sectionType, moduleName, operation) => {
    const sections = ((entityWindow.settings[moduleName] || {}).sections || {})[tableName] || [];
    const sectionIndex = findIndexOf(sections, sectionTypePropName, sectionType);

    return sectionIndex > -1 && !isExcludedSectionModule(moduleName, operation, sections[sectionIndex].excludeSectionModules);
};

const getSectionLicenseValueSelector = createSelector(
    state => getLicenseValuesByKeySelector(state),
    state => state.entityWindow,
    (getLicenseValuesByKey, entityWindow) => (tableName, moduleName, sectionType) => {
        const sections = entityWindow.settings[moduleName].sections[tableName] || [];
        const sectionIndex = findIndexOf(sections, sectionTypePropName, sectionType);
        const licenseKey = (sections[sectionIndex] || {}).licenseKey;

        return (getLicenseValuesByKey(licenseKey) || {}).subscribedCount;
    }
);

const getEWFieldInfoSelector = createSelector(
    state => state.entityWindow.settings,
    (settings) => (moduleName, tableName, fieldName) => {
        let fieldInfo = {};
        const sections = settings[moduleName].sections[tableName];

        for (let i = 0; i < sections.length; i++) {
            const section = sections[i];
            const field = find(section.fields, { name: fieldName });
            if (field) {
                fieldInfo = field;
                break;
            }
        }

        return fieldInfo;
    }
);

const getCommentsStaticMessagesSelector = createSelector(
    state => getTranslationsSelector(state, { sectionName: 'comments' }),
    (messages) => messages
);

const getResourceDates = (resourceGuid, dirayGroups, getFieldInfo) => {
    const inactiveUserDates = !(((dirayGroups || []).find(group => group.id === resourceGuid) || {})[RESOURCE_USERSTATUS]);

    let disabledDates = {
        betweenDates: [],
        beforeDate: null,
        inactiveUserDates
    };

    const resourceDiaryGroupIndex = dirayGroups.findIndex(group => group.id === resourceGuid);

    if (resourceDiaryGroupIndex > -1) {
        const currentDiaryGroup = getDiaryGroup(dirayGroups, resourceDiaryGroupIndex, getFieldInfo);

        if (currentDiaryGroup.length > 0) {
            const noDiaryRanges = getNoDiaries(resourceDiaryGroupIndex, dirayGroups, getFieldInfo);
            const firstDiaryBeforeDate = currentDiaryGroup[0].startDate;

            disabledDates.beforeDate = firstDiaryBeforeDate;
            disabledDates.betweenDates = noDiaryRanges;
        } else {
            disabledDates.betweenDates = [{ startDate: null, endDate: null }];
        }
    }

    return disabledDates;
};

const getDisabledDates = (uiEntity, dirayGroups, tableName, getFieldInfo) => {
    let dates = {};
    const resourceGuidField = `${tableName}_resource_guid`;

    if (uiEntity && uiEntity[resourceGuidField].value) {
        const resourceGuid = uiEntity[resourceGuidField].value;
        dates = getResourceDates(resourceGuid, dirayGroups, getFieldInfo);
    }

    return dates;
};

const getDisabledDatesSelector = createSelector(
    (state, payload) => getEntityWindowUIEntity(state.entityWindow, payload.moduleName),
    (state, payload) => payload.dirayGroups,
    (state, payload) => payload.tableName,
    (state => getFieldInfoSelector(state)),
    (uiEntity, dirayGroups, tableName, getFieldInfo) => {
        return getDisabledDates(uiEntity, dirayGroups, tableName, getFieldInfo);
    }
);

function createGetEntityWindowDynamicConfigSelector() {
    return createSelector(
        state => state.entityWindow,
        (state, moduleName) => moduleName,
        (entityWindow, moduleName) => {
            let expandedDynamicConfig = entityWindow.settings[moduleName].dynamicConfig || {};
            expandedDynamicConfig = addCustomDynamicLogic(expandedDynamicConfig);

            return expandedDynamicConfig;
        }
    );
}

function createGetSelectedEditAllKeysSelector() {
    return createSelector(
        state => state.entityWindow.settings,
        (settings) => {
            return (moduleName) => {
                return settings[moduleName].selectedFieldsKeys;
            };
        }
    );
}

const getEntityWindowSectionByKey = ({ entityWindow }, moduleName, tableName, sectionKey) => {
    const sections = entityWindow.settings[moduleName].sections[tableName] || [];

    return sections.find(section => section.key === sectionKey) || {};
};

const isEntityWindowSectionContentHidden = (section) => {
    return section.hideContent;
};

const getEntityWindowVisibleModules = createSelector(
    state => state.entityWindow.window,
    (entityWindow) => Object
        .keys(entityWindow)
        .filter(moduleName => entityWindow[moduleName].visible)
        .map(moduleName => entityWindow[moduleName])
);

const getEntityWindowOperation = ({ entityWindow }, moduleName) => {
    return entityWindow.window[moduleName].operation;
};

const getResourceSurrogateId = ({ entityWindow }, moduleName) => {
    const entity = getEntityWindowEntity(entityWindow, moduleName) || {};

    return entity[FILTER_FIELD_NAMES.RESOURCE_SURROGATE_ID];
};

const getEntityWindowButtonLabelsSelector = createSelector(
    state => state.internationalization,
    buildCommonEntityAliasesPlaceholdersSelector,
    (internationalization, commonEntityAliasesPlaceholders) => {

        const staticLabels = getTranslationsSelector({ internationalization }, {
            sectionName: 'entityWindow',
            idsArray: [
                'editButtonLabel',
                'editAllButtonLabel',
                'editRoleByCriteriaButtonLabel',
                'editRoleByNameButtonLabel',
                'deleteButtonLabel',
                'deleteAllButtonLabel',
                'rejectButtonLabel',
                'insufficientActionRights',
                'addNewRoleLabel',
                'roleListBodyEmptyStateLabel',
                'archiveButtonLabel',
                'archiveAllButtonLabel',
                'restartButtonLabel',
                'restartAllButtonLabel',
                'submitRequestButtonLabel',
                'submitRequestAllButtonLabel',
                'createButtonLabel',
                'makeLiveSingularButtonLabel',
                'makeLivePluralButtonLabel',
                'rollForwardLabel',
                'SelectionTitleLabel',
                'SelectionDescriptionLabel',
                'SelectionFieldsCaptionLabel',
                'manageRoleTemplatesEmptyStateLabel',
                'createdLabel',
                'myTemplatesLabel',
                'templateDetailsLabel',
                'renameLabel',
                'editRolePublicationButtonLabel',
                'provideTemplateNameLabel',
                'maxLengthValidationMessage',
                'createScenarioLabel',
                'editScenarioLabel',
                'openScenarioButtonLabel'
            ]
        });

        const populatedStrings = populateStringTemplates(staticLabels, commonEntityAliasesPlaceholders);

        return populatedStrings;
    }
);

const getEntityWindowTemplateSelector = createSelector(
    state => ((state.entityWindow || {}).settings || null),
    settings => {
        return (alias, tableName) => settings ? ((settings[alias] || {}).entityTemplates || {})[tableName] : {};
    }
);

const getEntityWindow = (state, moduleName) => state.entityWindow.window[moduleName] || {};

const getActiveEntityID = createSelector(
    (state, moduleName) => getEntityWindow(state, moduleName),
    window => {
        return window.isBatch ? window.activeEntity : window.entityId;
    }
);

const getCurrentDetailsPaneForPage = createSelector(
    state => state[PLANNER_PAGE_ALIAS].detailsPane,
    state => state[PLANNER_PAGE_ALIAS].workspaces,
    state => state[JOBS_PAGE_ALIAS].detailsPane,
    state => state[ROLE_GROUP_LIST_PAGE].detailsPane,
    state => state[ROLE_GROUP_DETAILS_PAGE].roleGroupDetailsPane,
    state => state[ROLE_INBOX_PAGE_ALIAS].detailsPane,
    state => state[MARKETPLACE_PAGE_ALIAS].detailsPane,
    getCurrentPageAliasSelector,
    (detailsPane, workspaces, jobsPageDpState, roleGroupListDpState, roleGroupDetailsDpState, roleInboxPageDpState, marketplacePageDpState, currentPageAlias) => {
        let result = {};

        switch (currentPageAlias) {
            case PLANNER_PAGE_ALIAS:
                result = getCurrentPlannerDetailsPane({ detailsPane, workspaces });
                break;
            case JOBS_PAGE_ALIAS:
                result = jobsPageDpState[JOBS_PAGE_DP_ALIAS];
                break;
            case ROLE_GROUP_LIST_PAGE:
                result = roleGroupListDpState[ROLE_GROUP_LIST_PAGE];
                break;
            case ROLE_GROUP_DETAILS_PAGE:
                result = roleGroupDetailsDpState[ROLE_GROUP_DETAILS_PAGE];
                break;
            case ROLE_INBOX_PAGE_ALIAS:
                result = roleInboxPageDpState[ROLE_INBOX_PAGE_DP_ALIAS];
                break;
            case MARKETPLACE_PAGE_ALIAS:
                result = marketplacePageDpState[MARKETPLACE_PAGE_DP_ALIAS];
                break;
        }

        return result;
    }
);

const getSelectedDetailsPaneTabKey = createSelector(
    state => (state[PLANNER_PAGE_ALIAS] || {}).detailsPane || {},
    state => (state[JOBS_PAGE_ALIAS] || {}).detailsPane || {},
    state => (state[ROLE_GROUP_LIST_PAGE] || {}).detailsPane || {},
    state => (state[ROLE_GROUP_DETAILS_PAGE] || {}).roleGroupDetailsPane || {},
    state => (state[ROLE_INBOX_PAGE_ALIAS] || {}).detailsPane || {},
    state => getCurrentPageAliasSelector(state) || {},
    state => (state[PLANNER_PAGE_ALIAS] || {}).workspaces || {},
    state => (state[MARKETPLACE_PAGE_ALIAS] || {}).detailsPane || {},
    (plannerPageDpState, jobsPageDpState, roleGroupListDpState, roleGroupDetailsDpState, roleInboxPageDpState, pageAlias, workspaces, marketplacePageDpState) => {
        let result = '';

        switch (pageAlias) {
            case PLANNER_PAGE_ALIAS: {
                const detailsPaneGuid = getSelectedWorkspaceSettings(workspaces).detailsPaneGuid;
                result = plannerPageDpState[detailsPaneGuid].selectedTabKey;
                break;
            }
            case JOBS_PAGE_ALIAS:
                result = (jobsPageDpState[JOBS_PAGE_DP_ALIAS] && jobsPageDpState[JOBS_PAGE_DP_ALIAS].selectedTabKey) || '';
                break;
            case ROLE_GROUP_LIST_PAGE:
                result = (roleGroupListDpState[ROLE_GROUP_LIST_PAGE] && roleGroupListDpState[ROLE_GROUP_LIST_PAGE].selectedTabKey) || '';
                break;
            case ROLE_GROUP_DETAILS_PAGE:
                result = (roleGroupDetailsDpState[ROLE_GROUP_DETAILS_PAGE] && roleGroupDetailsDpState[ROLE_GROUP_DETAILS_PAGE].selectedTabKey) || '';
                break;
            case ROLE_INBOX_PAGE_ALIAS:
                result = (roleInboxPageDpState[ROLE_INBOX_PAGE_DP_ALIAS] && roleInboxPageDpState[ROLE_INBOX_PAGE_DP_ALIAS].selectedTabKey || '');
                break;
        }

        return result;
    }
);

const getShowSubTabSelector = createSelector(
    state => getCurrentDetailsPaneForPage(state),
    state => getSelectedDetailsPaneTabKey(state),
    state => getSuggestedResourcesDataById(state),
    (detailsPane, selectedTabKey, getSuggestedResourceData) => (activeRoleGuid) =>
        (((detailsPane || {}).tabs || {})[selectedTabKey] || {}).showSubTab
        && !isEmptyObject(getSuggestedResourceData(activeRoleGuid))
);

const getModuleNameForPageSelector = createSelector(
    state => state.navigation.subPage || state.navigation.page,
    (pageAlias) => (isBatched, key) => {

        const modulesForPage = isBatched
            ? BATCH_DETAILS_PANE_PAGE_MODULES_MAP[pageAlias]
            : SINGLE_DETAILS_PANE_PAGE_MODULES_MAP[pageAlias];

        return isObject(modulesForPage) ? modulesForPage[key] : modulesForPage;
    }
);

const getRolerequestgroupSurrogateIdSelector = createSelector(
    (state, pageAlias, pageTableDataAlias) => state[pageAlias][pageTableDataAlias],
    (pageTableData) => (rolerequestgroupId) => {

        return pageTableData.rolerequestgroup.data
            .filter(entity => entity[ROLEREQUESTGROUP_FIELDS.GUID] === rolerequestgroupId)[0][ROLEREQUESTGROUP_FIELDS.SURROGATE_ID];
    }
);

const getDetailsPaneState = createSelector(
    state => state.entityWindow.window,
    getModuleNameForPageSelector,
    (window, getModuleNameForPage) => (isBatched, key) => {
        const moduleName = getModuleNameForPage(isBatched, key);

        return window[moduleName];
    }
);

const getSingleDetailsPaneOpenAction = createSelector(
    getSelectedDetailsPaneTabKey,
    getDetailsPaneState,
    getModuleNameForPageSelector,
    (detailsPaneSelectedKey, getDetailsPaneState, getModuleNameForPage) => {
        const isBatched = false;
        const detailsPane = getDetailsPaneState(isBatched, detailsPaneSelectedKey);
        const { tableName, collectionAlias, operation, entityId } = detailsPane;
        const lazyLoadEntityData = true;

        return entityWindowOpen(
            getModuleNameForPage(false, detailsPaneSelectedKey),
            tableName,
            collectionAlias,
            operation,
            {},
            entityId,
            lazyLoadEntityData
        );
    }
);

const getBatchDetailsPaneOpenAction = createSelector(
    state => state.plannerPage.workspaces,
    state => state.navigation.subPage || state.navigation.page,
    getModuleNameForPageSelector,
    getCurrentDetailsPaneForPage,
    getDetailsPaneState,
    getSelectedDetailsPaneTabKey,
    (workspaces, currentPageAlias, getModuleNameForPage, detailsPane, getDetailsPaneState, detailsPaneSelectedKey) => {
        const isBatched = true;
        const detailsPaneTab = getDetailsPaneState(isBatched, detailsPaneSelectedKey);
        const { tableName, operation, batchIds, activeEntity } = detailsPaneTab;
        const { selectedTabKey } = detailsPane;
        const detailsPaneSelectedTab = detailsPane.tabs[selectedTabKey];

        let collectionAlias = tableName;

        if (currentPageAlias === PLANNER_PAGE_ALIAS) {
            const workspaceSettings = getSelectedWorkspaceSettings(workspaces);
            collectionAlias = getCollectionAliasForTableName(workspaceSettings, detailsPaneSelectedTab.table);
        }

        return entityWindowOpenForMultiple(
            getModuleNameForPage(true),
            tableName,
            collectionAlias,
            operation,
            [],
            [...batchIds],
            activeEntity
        );
    }
);

function createGetActiveWindowSelector() {
    return createSelector(
        state => state.entityWindow,
        (entityWindow) => {
            let window = entityWindow;
            const { isBatch } = entityWindow;

            if (isBatch) {
                const { activeEntity, windows = {}, batchIds = [], showTitleCounter = true, showPluralForm = true, moduleName } = entityWindow;

                window = {
                    ...(windows[activeEntity] || {}),
                    batchIds,
                    showTitleCounter,
                    showPluralForm,
                    moduleName,
                    windows
                };
            }

            return window;
        }
    );
}

function createGetLinkedDataWrappedSelector() {
    return createSelector(
        state => state.primaryTableName,
        state => state.tableStructure,
        state => state.dataCollections,
        state => state.autoComplete,
        (primaryTableName, tableStructure, dataCollections, autoComplete) => (fieldName, recordId) => {
            let result = null;
            fieldName = fieldName.toLowerCase();
            const fieldInfo = getFieldInfo(tableStructure, primaryTableName, fieldName);

            if (fieldInfo != null && isCustomLookupOrPlanningDataField(fieldInfo)) {
                if (getIsCustomPlanningDataField(fieldInfo)) {
                    const { plannerLookupTable } = fieldInfo;
                    const data = getData(dataCollections, plannerLookupTable, recordId);
                    result = {
                        id: data[`${plannerLookupTable}_guid`],
                        value: data[`${plannerLookupTable}_description`]
                    };
                } else {
                    const data = getData(dataCollections, FIELD_LOOKUP_VALUE_TABLE, recordId);

                    result = {
                        id: data[`${FIELD_LOOKUP_VALUE_TABLE}_guid`],
                        value: data[`${FIELD_LOOKUP_VALUE_TABLE}_value`]
                    };
                }
            } else {
                const descriptionFieldPrefix = 'description';
                const linkedTableName = getFieldTableName(fieldInfo, primaryTableName);
                const linkedDescriptionField = `${linkedTableName}_${descriptionFieldPrefix}`;

                if (recordId) {
                    const data = getData(dataCollections, linkedTableName, recordId);
                    const recordFoundInState = !('dummy' in data);

                    if (recordFoundInState) {
                        result = {
                            id: recordId,
                            value: data[linkedDescriptionField]
                        };
                    } else {
                        let suggestionIndex = -1;

                        if (fieldName in autoComplete) {
                            suggestionIndex = (autoComplete[fieldName].suggestions || []).findIndex(suggestion => {
                                return suggestion.id === recordId;
                            });
                        }

                        result = {
                            id: recordId
                        };

                        if (suggestionIndex > -1) {
                            result = {
                                ...result,
                                ...autoComplete[fieldName].suggestions[suggestionIndex]
                            };
                        }
                    }
                }
            }

            return result;
        }
    );
}

function createGetEntityLinkedData() {
    const getLinkedDataWrappedSelector = createGetLinkedDataWrappedSelector();
    const untrackedProps = {};

    return createSelector(
        state => {
            untrackedProps.rootState = state;

            return untrackedProps;
        },
        (untrackedProps) => (fieldName, value, tableName, moduleName) => {
            const tableStructure = getTableStructure(untrackedProps.rootState);

            const getLinkedData = getLinkedDataWrappedSelector({
                tableStructure,
                primaryTableName: tableName,
                autoComplete: {},
                dataCollections: moduleName
                    ? getPageTableDatasSelector(untrackedProps.rootState)
                    : getPageTableDatasSelector(untrackedProps.rootState)
            });

            const linkedDescriptionField = `${fieldName.substring(0, fieldName.lastIndexOf('_'))}_description`;

            return {
                fieldName: linkedDescriptionField,
                description: (getLinkedData(fieldName, value) || {}).value
            };
        }
    );
}


const roleGroupDetailsHasUnsavedChanges = createSelector(
    state => getAddedRolesSelector(state),
    state => getDeletedRolesSelector(state),
    state => getModifiedRolesSelector(state),
    (added, deleted, modified) => {
        return added.length > 0 || deleted.length > 0 || modified.length > 0;
    }
);

function createGetUiEntitySelector(getUIEntityFunc) {
    return createSelector(
        (window) => window.tableName,
        (window) => window.uiEntity,
        (window) => window.entity,
        (window) => window.entityId,
        (window, getData) => getData,
        (window, getData, state) => window.tableName === TABLE_NAMES.ROLEREQUEST ? getRootStateAssigneesDataSelector(state) : () => [],
        (tableName, uiEntity, entity, entityId, getData, getRoleAssigneesResourcesData) => getUIEntityFunc(tableName, uiEntity, entity, getData, entityId, getRoleAssigneesResourcesData)
    );
}

const getEntityWindowSectionMessages = (sections = [], sectionKey) =>
    (sections.find(({ key }) => key === sectionKey) || {}).messages;

function createGetEntityWindowSectionActiveMessagesSelector() {
    return createSelector(
        state => state.entityWindow.messages,
        state => state.sections,
        (activeMessages = [], sections = []) =>
            (sectionKey) => {
                const configMessages = getEntityWindowSectionMessages(sections, sectionKey) || [];

                return activeMessages.filter(({ id }) => configMessages.find(message => message.id === id));
            }
    );
}

const getEntityWindowErrorSelector = createSelector(
    state => state.entityWindow,
    state => state.roleAssigneesState,
    resourceSkillsChangesHasErrorsSelector,
    (entityWindow, roleAssigneesState, resourceSkillsChangesHasErrors) =>
        (moduleName) => {
            const window = (entityWindow.window || {})[moduleName] || {};
            const { tableName, entityId } = window;
            let error = window.error;
            const batchEntities = window.windows;

            if (batchEntities) {
                let batchEntitiesErrors = Object.keys(batchEntities).reduce((accumulator, window) => {
                    const hasErrors = batchEntities[window].errors;

                    hasErrors && accumulator.push(window);

                    return accumulator;
                }, []);

                error = batchEntitiesErrors.length > 0 ? { code: 1 } : error;
            }

            const { ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL } = ENTITY_WINDOW_MODULES;

            if (TABLE_NAMES.RESOURCE === tableName && (!error || error.code == null)) {
                error = resourceSkillsChangesHasErrors(entityId) ? { code: 0 } : error;
            }

            if ([ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL].includes(moduleName)) {
                const errorsSelectorState = {
                    entityWindow: { window },
                    roleAssigneesState
                };

                const assigneesErrors = getAssigneesBudgetSectionErrorrs(errorsSelectorState);

                error = assigneesErrors.length > 0 ? { code: 0 } : error;
            }

            return error;
        }
);

function createGetMultiValueLinkedFieldListItemWrappedSelector() {
    return createSelector(
        state => state.tableStructure,
        state => state.dataCollections,
        state => state.primaryTableName,
        (tableStructure, dataCollections, primaryTableName) => (actualFieldName, tableName, id) => {
            if (id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID && TABLE_NAMES.BOOKING) {
                return UNASSIGNED_RESOURCE_OPTION;
            }

            const { ID, VALUE, ADDITIONAL_INFO } = ENTITY_OPTION_FIELDS;
            let item = {
                [ID]: null,
                [VALUE]: null,
                [ADDITIONAL_INFO]: null
            };

            const fieldInfo = getFieldInfo(tableStructure, primaryTableName, actualFieldName);
            const fieldTableName = getFieldTableName(fieldInfo, tableName);
            const result = getData(dataCollections, fieldTableName, id);
            const resultFoundInState = !('dummy' in result);

            if (resultFoundInState) {
                const optionFields = entityOptionFieldsMap[fieldTableName];
                item = {
                    [ID]: result[optionFields[ID]],
                    [VALUE]: result[optionFields[VALUE]],
                    [ADDITIONAL_INFO]: result[optionFields[ADDITIONAL_INFO]],
                    [CME_PERCENTAGES]: {
                        [CME_RED]: result[RESOURCE_CMERED],
                        [CME_GREEN]: result[RESOURCE_CMEGREEN],
                        [CME_BLUE]: result[RESOURCE_CMEBLUE],
                        [CME_YELLOW]: result[RESOURCE_CMEYELLOW]
                    }
                };
            }

            return item;
        }
    );
}

const getEntityOperationTitlePrefixSelector = createSelector(
    state => state.internationalization,
    (internationalization) => (operation, title) => {
        const key = operation === ENTITY_WINDOW_OPERATIONS.CREATE ? 'createEntityTitle' : 'editEntityTitle';

        const operationTitle = getTranslationsSelector(
            { internationalization },
            {
                sectionName: 'entityWindow',
                idsArray: ['createEntityTitle', 'editEntityTitle']
            }
        );

        const newTitle = populateStringTemplates(operationTitle, { entityTitleAlias: title });

        return newTitle[key];
    }
);

const getEntityDependantTitleSelector = createSelector(
    state => state.internationalization,
    (internationalization) => (entityAlias, entityWindow) => {
        const { tableName, moduleName, isBatch = false } = entityWindow;
        let title = entityAlias;

        if (tableName === TABLE_NAMES.ROLEREQUEST || tableName === TABLE_NAMES.ROLEMARKETPLACE) {
            const headerLabels = getTranslationsSelector(
                { internationalization },
                {
                    sectionName: 'entityWindow',
                    idsArray: ['roleByNameWindowTitle', 'roleByRequirementWindowTitle', 'manageRoleTemplatesWindowTitle', 'roleTemplateWindowTitle', 'rolePublicationWindowTitle']
                }
            );
            const {
                roleByNameWindowTitle,
                roleByRequirementWindowTitle,
                manageRoleTemplatesWindowTitle,
                roleTemplateWindowTitle,
                rolePublicationWindowTitle
            } = populateStringTemplates(headerLabels, { rolerequestCapitalEntityAlias: entityAlias });

            const customTitleBatchEntityWindows = [ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM, ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL];

            if (isBatch && !customTitleBatchEntityWindows.includes(moduleName)) {
                title = getBatchRolesWindowDependantTitle(entityWindow, roleByNameWindowTitle, roleByRequirementWindowTitle, entityAlias);
            } else if (moduleName === ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL) {
                title = manageRoleTemplatesWindowTitle;
            } else if (moduleName === ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL) {
                title = roleTemplateWindowTitle;
            } else if (moduleName === ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL) {
                title = rolePublicationWindowTitle;
            } else {
                let singleEntityWindow = entityWindow;

                if (moduleName === ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM) {
                    const getActiveWindowSelector = createGetActiveWindowSelector();
                    singleEntityWindow = getActiveWindowSelector({ entityWindow });
                }

                title = getSingleRoleWindowDependantTitle(singleEntityWindow, roleByNameWindowTitle, roleByRequirementWindowTitle);
            }
        }

        return title;
    }
);

const getIsRolegroupNameUnchanged = (pageParams = {}) => {
    const { changedRolegroupName, rolegroupName } = pageParams;

    return (changedRolegroupName === undefined || changedRolegroupName === rolegroupName);
};

const getHasRolegroupErrorsSelector = createSelector(
    state => state.entityWindow,
    state => state.roleRequirementsState,
    state => state.roleAssigneesState,
    (entityWindow, roleRequirementsState, roleAssigneesState) => {
        const rolesWithErrors = getRolesWithErrors(entityWindow, roleRequirementsState, roleAssigneesState);
        const isEmptyState = rolesWithErrors.includes(EMPTY_STATE);

        return rolesWithErrors.length > 0 && !isEmptyState;
    }
);

const getIsRolerequestSaveButtonDisabled = createSelector(
    state => state.entityWindow,
    state => state.roleRequirementsState,
    state => state.roleAssigneesState,
    state => getHasRolegroupErrorsSelector(state),
    state => getIsRolegroupNameUnchanged(state.pageParams),
    (entityWindow, roleRequirementsState, roleAssigneesState, hasRolegroupErrors, isRolegroupNameUnchanged) => {
        const { windows } = entityWindow;
        const hasRolegroupUnsavedChanges = roleGroupDetailsHasUnsavedChanges({ windows, roleRequirementsState, roleAssigneesState });

        return hasRolegroupErrors || (!hasRolegroupUnsavedChanges && isRolegroupNameUnchanged);
    }
);

export const getEntitySpecificProps = (context) => {
    const { tableName, entity, entityId, entityWindow } = context;
    let entitySpecificProps = {};

    if (tableName === TABLE_NAMES.ROLEREQUEST && getIsCriteriaRole(entity) && entityId) {
        const { state, entityId, entity, staticLabels, getEntityInfo } = context;
        const requirementsSectionInReadOnlyMode = getRequirementsSectionIsReadOnlyMode({
            state,
            entityId,
            statusId: entity[ROLEREQUEST_FIELDS.STATUS_GUID],
            entityWindow
        });

        const getCriteriaControlPlaceholderText = getCriteriaControlPlaceholderTextSelector({
            staticLabels,
            getEntityInfo
        });

        const criteriaFieldsState = getCriteriaSectionsFieldsSelector(state, entityId);
        const assigneesState = getCriteriaRoleAssignedResourcesSelector(state)(entityId);

        entitySpecificProps = {
            requirementsSectionInReadOnlyMode,
            getCriteriaControlPlaceholderText,
            criteriaFieldsState,
            assigneesState
        };
    }

    return entitySpecificProps;
};

const getEntityWindowByModuleNameSelector = createSelector(
    state => state.entityWindow.window,
    (window = {}) => (moduleName) => {
        const { windows, batchIds } = window[moduleName] || {};

        return {
            windows, batchIds
        };
    }
);

const getEntityWindowModule = createSelector(
    state => getCurrentPageAliasSelector(state),
    (pageAlias) => {
        const { JOBS_PAGE_MODAL, ROLE_INBOX_PAGE_MODAL, PLANNER_PAGE_MODAL, MARKETPLACE_PAGE_MODAL, RESOURCES_PAGE_MODAL } = ENTITY_WINDOW_MODULES;
        let entityWindowModuleName;

        switch (pageAlias) {
            case ROLE_GROUP_DETAILS_PAGE: {
                entityWindowModuleName = JOBS_PAGE_MODAL;
                break;
            }
            case RESOURCES_PAGE_ALIAS: {
                entityWindowModuleName = RESOURCES_PAGE_MODAL;
                break;
            }
            case ROLE_INBOX_PAGE_ALIAS: {
                entityWindowModuleName = ROLE_INBOX_PAGE_MODAL;
                break;
            }
            case PLANNER_PAGE_ALIAS: {
                entityWindowModuleName = PLANNER_PAGE_MODAL;
                break;
            }
            case MARKETPLACE_PAGE_ALIAS: {
                entityWindowModuleName = MARKETPLACE_PAGE_MODAL;
                break;
            }
            default:
                entityWindowModuleName = '';
                break;
        }

        return entityWindowModuleName;
    }
);

const getEntityWindowDetailsPaneModule = createSelector(
    state => getCurrentPageAliasSelector(state),
    (pageAlias) => {
        const { PLANNER_PAGE_DETAILS_PANE, ROLE_INBOX_PAGE_DETAILS_PANE, MARKETPLACE_DETAILS_PANE } = ENTITY_WINDOW_MODULES;
        let entityWindowModuleName;

        switch (pageAlias) {
            case ROLE_INBOX_PAGE_ALIAS: {
                entityWindowModuleName = ROLE_INBOX_PAGE_DETAILS_PANE;
                break;
            }
            case PLANNER_PAGE_ALIAS: {
                entityWindowModuleName = PLANNER_PAGE_DETAILS_PANE;
                break;
            }
            case MARKETPLACE_PAGE_ALIAS: {
                entityWindowModuleName = MARKETPLACE_DETAILS_PANE;
                break;
            }
            default:
                entityWindowModuleName = '';
                break;
        }

        return entityWindowModuleName;
    }
);

function createShouldRenderSectionSelector() {
    return createSelector(
        state => getCurrentPageAliasSelector(state),
        state => getSelectedDetailsPaneTabKey(state),
        state => getIsMultipleAssigneesEnabled(state),
        state => entityWindowWorkHistoryPagedDataSelector(state),
        state => getFieldInfoSelector(state),
        state => getRepeatBookingDialogDataSelector(state),
        state => state,
        (currentPageAlias, activetabKey, useMultipleAssignees, wrappedRecentWorkHistory, getFieldInfo, repeatBookingData, state) => (sectionConfig, operation, attachmentIds, isReadModeOnly, moduleName, entity, sectionContent) => {
            const { fields = [], excludeSectionModules = {}, excludeSectionCondition = {}, excludeSectionFields = false } = sectionConfig;
            const aditionalCondition = disableSectionConditionsMap[excludeSectionCondition.type];
            const includeSectionModule = !isExcludedSectionModule(moduleName, operation, excludeSectionModules);
            const getFeatureFlag = (flag) => getFeatureFlagSelector(flag)(state);
            let shouldRenderIncludedSection = !excludeSectionFields && fields.length > 0 && includeSectionModule;
            const applicationUser = state.applicationUser;
            if (aditionalCondition) {
                const { requiredProps: sectionRequiredProps, conditionCheck } = aditionalCondition;
                const context = {
                    ...sectionConfig,
                    currentPageAlias,
                    activetabKey,
                    operation,
                    attachmentIds,
                    isReadModeOnly,
                    moduleName,
                    entity,
                    includeSectionModule,
                    useMultipleAssignees,
                    sectionContent,
                    wrappedRecentWorkHistory,
                    getFieldInfo,
                    repeatBookingData,
                    getFeatureFlag,
                    applicationUser
                };
                const requiredProps = extractProps(context, sectionRequiredProps);
                shouldRenderIncludedSection = shouldRenderIncludedSection || conditionCheck(requiredProps);
            }

            return shouldRenderIncludedSection;
        }
    );
}

const getEntityWindowCriteriaRoleIds = createSelector(
    state => getEntityWindowByModuleNameSelector(state),
    (getWindowByModuleName) => (moduleName) => {
        const { windows, batchIds } = getWindowByModuleName(moduleName);

        return (batchIds || []).filter(id => {
            const { entity } = windows[id];

            return id !== EMPTY_STATE && getIsCriteriaRole(entity);
        });
    }
);

const getIsActiveEntityLazyLoaded = createSelector(
    state => state.entityWindow.window,
    (entityWindow) => (moduleName) => {
        const { activeEntity, windows, isBatch } = entityWindow[moduleName];

        return isBatch ? windows[activeEntity].entityLoaded : false;
    }
);

const getHasWindowInContextualEditSelector = createSelector(
    state => state.entityWindow.window,
    (entityWindow) => (moduleName, fieldName) => {
        const { windows: entities } = entityWindow[moduleName];

        return Object.values(entities).some(currentEntity => {
            const fieldInfo = ((currentEntity || {}).uiEntity || {})[fieldName] || {};
            const { isContextuallyEditing = false, displayValue: isNameChanged = '' } = fieldInfo;

            return isContextuallyEditing && isNameChanged;
        });
    }
);

const getEntityWindowCustomSettings = createSelector(
    (entityWindow, moduleName) => getEntityWindowSettings(entityWindow, moduleName) || {},
    (settings) => settings.customSettings || {}
);

const getShareLink = (id) => {
    const {
        protocol,
        hostname,
        port = ''
    } = window.location;
    const client = getServerinfo().Client;
    const formattedPort = port ? `:${port}` : '';
    const formattedClient = client ? `/${client}` : '';
    const link = `${protocol}//${hostname}${formattedPort}${formattedClient}/rolesboard/rolepreview/${id}`;

    return link;
};


const getModuleSpecificProps = createSelector(
    state => state,
    state => state.internationalization,
    state => getEntitySingularAliasSelector(state),
    (state, internationalization, getEntityAlias) => (moduleName, entity = {}) => {
        let result = {};

        switch (moduleName) {
            case ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL: {
                const shareLink = getShareLink(entity.rolerequest_surrogate_id);
                const staticMessages = getPreviewEntityPageStaticMessages({ internationalization: internationalization });
                const roleAlias = getEntityAlias(TABLE_NAMES.ROLEREQUEST, { singularForm: true, capitalized: false, fallbackValue: 'role' });
                const { sharePopoverTitle } = populateStringTemplates(staticMessages, { roleAlias });

                result = {
                    shouldAlwaysShowJumpToSection: false,
                    isShareable: true,
                    shareLink: shareLink,
                    shareButtonType: 'tertiary',
                    sharePopoverTitle: sharePopoverTitle
                };

                break;
            }
            case ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL: {
                const shouldStopProppagation = getHasWindowInContextualEditSelector(state)(moduleName, ROLEREQUEST_FIELDS.DESCRIPTION);
                result = {
                    shouldStopProppagation
                };

                break;
            }
        }

        return result;
    }
);

const getEntityWindowEmptyConfigSelector = createSelector(
    state => state.internationalization.translation.entityWindow.emptyState || {},
    state => buildCommonEntityAliasesPlaceholdersSelector(state),
    (emptyStateMessages, commonEntityAliases) => (emptyStateConfig) => {
        const { noRoleGroupsCoincidenceState = {} } = emptyStateConfig;

        const translatedEmptyStateConfigNoItemsCoincidence = translateConfig(noRoleGroupsCoincidenceState.props, noRoleGroupsCoincidenceState.props, emptyStateMessages);
        const noItemsCoincidenceStaticLabels = populateStringTemplates(translatedEmptyStateConfigNoItemsCoincidence, commonEntityAliases);

        const translatedEmptyStateConfig = {
            ...emptyStateConfig,
            noRoleGroupsCoincidenceState: {
                ...noRoleGroupsCoincidenceState,
                props: noItemsCoincidenceStaticLabels
            }
        };

        return translatedEmptyStateConfig;
    }
);

const getDetailsPaneEmptyStatePropsSelector = createSelector(
    (state) => getRoleGroupIdsFromSelectedRolesSelector(state) || [],
    (state) => getEntityWindowEmptyConfigSelector(state),
    (roleGroupIdsFromSelectedRoles, getEntityWindowEmptyConfig) => (selectedTabKey) => {
        let emptyStateProps = {};

        if (selectedTabKey === DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY) {
            const hasMultipleRoleGroupsSelected = getArrayHasSameValues(roleGroupIdsFromSelectedRoles);

            if (!hasMultipleRoleGroupsSelected) {
                emptyStateProps = {
                    emptyStateConfig: getEntityWindowEmptyConfig(getEmptyStateByTabConfig(DETAILS_PANE_TAB_KEYS.ROLE_GROUP_KEY)),
                    emptyStateConfigKey: DETAILS_PANE_NO_ROLE_GROUPS_COINCIDENCE_STATE
                };
            }
        }

        return emptyStateProps;
    }
);

const getExternalTableDependantIdsSelector = createSelector(
    state => getRolesForRoleRequestGroupSelector(state),
    (getRolesForRoleRequestGroup) => (entityId) => {
        // Should be extended if tableDependancy is needed in other actions
        return getRolesForRoleRequestGroup(entityId);
    }
);

const getEntityWindowSelectedTabKey = createSelector(
    (state) => state.entityWindow.window,
    (entityWindow) => (moduleName) => {
        const window = entityWindow[moduleName];

        const { activeEntity, windows, isBatch } = window;
        const { activeTab } = isBatch ? windows[activeEntity] : window;

        return activeTab || DEFAULT_ACTIVE_EW_TAB_KEY;
    }
);

const getEntityWindowFieldValueCaptionSelector = createSelector(
    (state) => getTranslationsSelector(state, { sectionName: 'entityWindow' }),
    (entityWindowTranslations) =>
        (percentage, translation, getFieldCaption) =>
            getFieldCaption(percentage, entityWindowTranslations.fieldValueCaption[translation])
);

const getFieldMessagesSelector = createSelector(
    state => state.entityWindow.fieldValueMessages,
    (fieldValueMessages = {}) => (tableName, fieldName) => {
        if (!fieldValueMessages[tableName] || !fieldValueMessages[tableName][fieldName]) {
            return [];
        }

        return Object.values(fieldValueMessages[tableName][fieldName]);
    }
);

/**
 * The selector is used to get the review settings translation
 */
const getEntityWindowReviewSettingsSelector = createSelector(
    (state) => getTranslationsSelector(state, { sectionName: 'entityWindow' }),
    (entityWindowTranslations) => {
        return entityWindowTranslations.reviewSettings;
    }
);

/**
 * @typedef {Object} ReviewSectionTranslations
 * @property {string} skillReviewLabel - Label for skill reviews.
 * @property {string} resourceReviewedLabel - Label showing resource reviewed with placeholder.
 * @property {string} submitReviewButtonLabel - Label for the submit review button.
 */

/**
 * Selector to get the 'reviewSection' translations from the 'entityWindow' section of the translations state.
 *
 * @type {import('reselect').Selector<Object, ReviewSectionTranslations>}
 */
const getEntityWindowReviewSectionsSelector = createSelector(
    /**
   * @param {Object} state - The Redux state
   * @returns {Object} entityWindowTranslations
   */
    (state) => getTranslationsSelector(state, { sectionName: 'entityWindow' }),

    /**
   * @param {Object} entityWindow - Translations inside 'entityWindow' section
   * @returns {ReviewSectionTranslations}
   */
    (entityWindow) => /** @type {ReviewSectionTranslations} */ (
        entityWindow.reviewSection
    )
);

export {
    getEntityWindowTitle,
    getEntityWindowSections,
    getEntityWindowActions,
    getEntityWindowAvatarConfig,
    getEntityWindowEntity,
    getEntityWindowEntityById,
    getEntityWindowTableName,
    getEntityWindowAllComments,
    getEntityWindowComments,
    getEntityWindowPagedComments,
    entityWindowHasSection,
    getSectionLicenseValueSelector,
    getEWFieldInfoSelector,
    getCommentsStaticMessagesSelector,
    createGetEntityWindowDynamicConfigSelector,
    createGetSelectedEditAllKeysSelector,
    getDisabledDatesSelector,
    getEntityWindowVisibleModules,
    getEntityWindowSectionByKey,
    isEntityWindowSectionContentHidden,
    getEntityWindowOperation,
    getResourceSurrogateId,
    getEntityWindowTemplateSelector,
    getEntityWindowButtonLabelsSelector,
    getActiveEntityID,
    getCurrentDetailsPaneForPage,
    getSelectedDetailsPaneTabKey,
    getShowSubTabSelector,
    getModuleNameForPageSelector,
    getDetailsPaneState,
    getSingleDetailsPaneOpenAction,
    getBatchDetailsPaneOpenAction,
    createGetActiveWindowSelector,
    createGetLinkedDataWrappedSelector,
    createGetEntityLinkedData,
    roleGroupDetailsHasUnsavedChanges,
    createGetUiEntitySelector,
    createGetEntityWindowSectionActiveMessagesSelector,
    getEntityWindowErrorSelector,
    createGetMultiValueLinkedFieldListItemWrappedSelector,
    getEntityDependantTitleSelector,
    getEntityWindow,
    getIsRolegroupNameUnchanged,
    getHasRolegroupErrorsSelector,
    getIsRolerequestSaveButtonDisabled,
    getEntityWindowModule,
    getEntityWindowDetailsPaneModule,
    getEntityWindowByModuleNameSelector,
    createShouldRenderSectionSelector,
    getHasWindowInContextualEditSelector,
    getEntityWindowCustomSettings,
    getEntityWindowCriteriaRoleIds,
    getIsActiveEntityLazyLoaded,
    getModuleSpecificProps,
    getEntityOperationTitlePrefixSelector,
    getDetailsPaneEmptyStatePropsSelector,
    getExternalTableDependantIdsSelector,
    getRolerequestgroupSurrogateIdSelector,
    getEntityWindowSelectedTabKey,
    getEntityWindowFieldValueCaptionSelector,
    getFieldMessagesSelector,
    getEntityWindowReviewSettingsSelector,
    getEntityWindowReviewSectionsSelector
};