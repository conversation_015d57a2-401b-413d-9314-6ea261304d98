import { HelpPagesDefaultPage } from '../constants/helpPagesConsts';
import { APPLICATION_HELP } from '../actions/actionTypes';
import { setColourSchemeLegendVisibility } from '../actions/colourSchemeLegendActions';
import GLOBAL_HOT_KEYS_ACTION_TYPES from '../state/hotKeys/global/actionTypes';
import PLANNER_HOT_KEYS_ACTION_TYPES from '../state/hotKeys/plannerPage/actionTypes';
import { getPrimaryHotKeyDescription } from '../utils/hotKeys';
import { globalHotKeysConfig } from '../state/hotKeys/global/hotKeysConfig';
import { plannerPageHotKeysConfig } from '../state/hotKeys/plannerPage/hotKeysConfig';
//Due to this props being static, we will not create a reducer logic for them

const HELP_LINES = {
    HELP_PAGE_LINK: 'helpPage',
    KEYBOARD_SHORTCUTS: 'keyboardShortcutsHelp',
    COLOUR_SCHEME_LEGEND: 'plannerColourSchemeLegend'
}

const helpLinesPerPageMap = {
    default: [HELP_LINES.HELP_PAGE_LINK],
    adminSettings: {
        skills: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
        securityprofiles: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
        chargerates: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
        daytypes: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
        workpatterns: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS]
    },
    jobsPage: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
    listPage: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
    plannerPage: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS, HELP_LINES.COLOUR_SCHEME_LEGEND],
    talentProfilePage: [HELP_LINES.HELP_PAGE_LINK],
    report: [HELP_LINES.HELP_PAGE_LINK],
    notificationsPage: [HELP_LINES.HELP_PAGE_LINK],
    timesheetsPage: [HELP_LINES.HELP_PAGE_LINK],
    roleInboxPage: [HELP_LINES.HELP_PAGE_LINK, HELP_LINES.KEYBOARD_SHORTCUTS],
    marketplacePage: [HELP_LINES.HELP_PAGE_LINK],
    tableViewPage: [HELP_LINES.HELP_PAGE_LINK],
    summaryPage: [HELP_LINES.HELP_PAGE_LINK],
};

const getHelpLinesMap = (translations = {}) => {
    const { helpPageLink, keyboardShortcuts, legend} = translations;
    return {
        [HELP_LINES.HELP_PAGE_LINK]: {
            text: helpPageLink ? helpPageLink : 'Help documentation',
            shortcut: getPrimaryHotKeyDescription(globalHotKeysConfig[GLOBAL_HOT_KEYS_ACTION_TYPES.SHOW_HELP_DOCUMENTATION]),
            //are we sure that we don't want to follow the same pattern here as the one for the other actions (handle the action in epic instead of using links)?
        },
        [HELP_LINES.KEYBOARD_SHORTCUTS]: {
            text: keyboardShortcuts ? keyboardShortcuts : 'Keyboard shortcuts',
            shortcut: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[PLANNER_HOT_KEYS_ACTION_TYPES.SHOW_HELP]),
            action: {
                type: APPLICATION_HELP.OPEN_KEYBOARD_SHORTCUTS_HELP
            }
        },
        [HELP_LINES.COLOUR_SCHEME_LEGEND]: {
            text: legend ? legend :'Legend',
            shortcut: getPrimaryHotKeyDescription(plannerPageHotKeysConfig[PLANNER_HOT_KEYS_ACTION_TYPES.TOGGLE_COLOUR_SCHEME_LEGEND]),
            action: setColourSchemeLegendVisibility(true)
        }
    }
}

let helpPopupProps = {
    shortcut: '/',
    linesLinks: {
        [HELP_LINES.HELP_PAGE_LINK]: ''
    },
    helpLines: helpLinesPerPageMap.plannerPage,
    helpLinesMap: getHelpLinesMap()
}

export const getAppPageHelpPageUrl = (appPage, subPage) => {
    let helpPageUrl = HelpPagesDefaultPage;
    const appPageHelpPageUrl = appPage.helpPageUrl;

    if(subPage && appPageHelpPageUrl[subPage])
        helpPageUrl = appPageHelpPageUrl[subPage];
    else if(typeof appPageHelpPageUrl !== 'object' && appPageHelpPageUrl !== null && !subPage)
        helpPageUrl = appPageHelpPageUrl;
    
    return helpPageUrl;
}

export const getAppPageHelpLines = (appPage, subPage) => {
    const pageHelpLines = helpLinesPerPageMap[appPage.name];
    const isParentPage = typeof pageHelpLines === 'object' && !Array.isArray(pageHelpLines);

    let result = pageHelpLines;

    if(isParentPage){
        const helpLines = helpLinesPerPageMap[appPage.name][subPage];
        result = helpLines || helpLinesPerPageMap.default;
    }

    return result
}

export const getHelpPopupProps = (appPage = {helpPageUrl: ''}, subPage, translations = {}) => {
    let helpPageUrl = getAppPageHelpPageUrl(appPage, subPage);
    const { linesLinks } = helpPopupProps;

    helpPopupProps = {
        ...helpPopupProps,
        title: translations.title ? translations.title : 'Help',
        helpLinesMap: getHelpLinesMap(translations),
        support: translations.contactSupport
    }

    if(linesLinks.helpPage !== helpPageUrl)
        helpPopupProps = {
            ...helpPopupProps,
            linesLinks: {
                [HELP_LINES.HELP_PAGE_LINK]: helpPageUrl
            }
        }

    helpPopupProps = {
        ...helpPopupProps,
        helpLines: getAppPageHelpLines(appPage, subPage)
    }

    return helpPopupProps;
}