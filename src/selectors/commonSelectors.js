import { createSelector } from "reselect";
import { getBarGroupsGuidByTable, getSelectedWorkspaceSettings } from '../selectors/workspaceSelectors';
import { getData } from '../utils/commonUtils';
import { PLANNER_PAGE_ALIAS } from '../constants/plannerConsts';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import { getDataGridSelectedEntities } from '../utils/dataGridUtils';
import { TABLE_NAMES } from '../constants';
import { getCollectionAliasForTableName, getCurrentEditsForTableName, getCurrentPlannerBarGroups, getCurrentPlannerPagedData, getCurrentPlannerSubRecData, getPlannerPageDataCollections } from "./plannerPageSelectors";
import { ROLE_INBOX_PAGE_ALIAS } from "../constants/roleInboxPageConsts";
import { getCurrentPageAliasSelector } from './navigationSelectors';
import { RESOURCE_USERSTATUS } from "../constants/fieldConsts";
import { getRoleInboxSelectedRoleIdsSelector } from "./roleInboxSelectors";
import { PEOPLE_FINDER_DIALOG_ALIAS } from "../constants/peopleFinderConst";
import { getPeopleFinderCurrentEditsSelector } from "./peopleFinderSelectors";
import { MARKETPLACE_PAGE_ALIAS } from "../constants/marketplacePageConsts";
import { TABLE_VIEW_PAGE_ALIAS } from "../constants/tableViewPageConsts";
import { isEmpty } from "lodash";
import { getFieldInfoSelector } from "./tableStructureSelectors";
import { getValidFiltersSelection } from "../utils/filtersUtils";
import { getTableViewPageDataCollections } from "./tableViewSelectors";
import { DEFAULT_PAGE_SIZE } from "../constants/paginationConsts";
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from "../utils/translationUtils";
import { getTranslationsSelector } from "./internationalizationSelectors";
import { RESOURCES_PAGE_ALIAS } from "../constants/resourcesPageConsts";

const translationProps = {
    sectionName: 'common',
    idsArray: [
        'selectRowTextAriaLabel',
        'cancelChangesLabel',
        'saveChangesLabel',
        'deleteLabelItemText'
    ]
};

export const getSelectedEntitiesSelector = createSelector(
    (state, pageAlias) => state[pageAlias],
    (state, pageAlias) => pageAlias,
    (page, pageAlias) => {
        let selectedEntities;
        switch (pageAlias) {
            case PLANNER_PAGE_ALIAS: {
                selectedEntities = getPlannerSelectedEntitiesSelector(page)(TABLE_NAMES.BOOKING);
                break;
            }
            case JOBS_PAGE_ALIAS: {
                selectedEntities = getDataGridSelectedEntities(page);
                break;
            }
            case RESOURCES_PAGE_ALIAS: {
                selectedEntities = getDataGridSelectedEntities(page);
                break;
            }
            case ROLE_INBOX_PAGE_ALIAS: {
                selectedEntities = {
                    tableName: TABLE_NAMES.ROLEREQUEST,
                    ids: getRoleInboxSelectedRoleIdsSelector({ roleInboxPage: page })
                };
                break;
            }
            case PEOPLE_FINDER_DIALOG_ALIAS: {
                selectedEntities = {
                    tableName: TABLE_NAMES.RESOURCE,
                    ids: getPeopleFinderCurrentEditsSelector(page)
                };
                break;
            }
            case MARKETPLACE_PAGE_ALIAS: {
                selectedEntities = {
                    tableName: TABLE_NAMES.ROLEREQUEST,
                    ids: [] //TODO: Incorporate with the spec. A selector need to be created!!!
                };
                break;
            }
            case TABLE_VIEW_PAGE_ALIAS: {
                selectedEntities = {
                    tableName: TABLE_NAMES.BOOKINGBYWEEKVIEW,
                    ids: []
                };
                break;
            }
            default: {
                selectedEntities = {};
            }
        }

        return selectedEntities;
    }
);

export const getPlannerSelectedEntitiesSelector = createSelector(
    plannerPage => getCurrentEditsForTableName({ plannerPage }),
    plannerPage => getCurrentPlannerPagedData(plannerPage),
    plannerPage => getCurrentPlannerSubRecData(plannerPage),
    plannerPage => getCurrentPlannerBarGroups(plannerPage),
    plannerPage => getSelectedWorkspaceSettings(plannerPage.workspaces),
    (getEditsForTable ,pagedData, subRecData, getPlannerGroups, workspaceSettings) => (tableName, predefinedSelectedEntitiesIds = []) => {
        const { pagedMasterRecPlannerDataGuid, subRecPlannerDataGuid } = workspaceSettings;

        const selectedEntitiesIds = predefinedSelectedEntitiesIds.length === 0
            ? getEditsForTable(tableName)
            : predefinedSelectedEntitiesIds;

        let entities = [];
        let entitiesData = {};
        let entitiesGuid, collectionAlias;
        const { tableName: masterRecTableName } = pagedData;
        const { tableName: subRecTableName } = subRecData;
        let selectedEntities = { ids: selectedEntitiesIds, entities, entitiesGuid, collectionAlias };

        switch (tableName) {
            case masterRecTableName: {
                entitiesData = pagedData;
                entitiesGuid = pagedMasterRecPlannerDataGuid;
                break;
            }
            case subRecTableName: {
                entitiesData = subRecData;
                entitiesGuid = subRecPlannerDataGuid;
                break;
            }
            case TABLE_NAMES.BOOKING:
            case TABLE_NAMES.ROLEREQUEST: {
                const barsGroupsGuid = getBarGroupsGuidByTable(workspaceSettings, tableName);
                const barsGroups = getPlannerGroups[tableName];

                entitiesData = barsGroups;
                entitiesGuid = barsGroupsGuid;
                break;
            }
        }

        if (selectedEntitiesIds.length > 0) {

            for (let i = 0; i < selectedEntitiesIds.length; i++) {
                entities.push(getData([entitiesData], tableName, selectedEntitiesIds[i]));
            }

            selectedEntities = {
                ids: selectedEntitiesIds,
                entities,
                tableName,
                entitiesGuid,
                collectionAlias: getCollectionAliasForTableName(workspaceSettings, tableName)
            };
        }

        return selectedEntities;
    }
);

export const getPageTableDatas = createSelector(
    (state, pageAlias) => state[pageAlias],
    (state, pageAlias) => pageAlias,
    (page, pageAlias) => {
        let tableDatas;

        switch (pageAlias) {
            case PLANNER_PAGE_ALIAS:
                tableDatas = page.plannerTableDatas;
                break;
            case JOBS_PAGE_ALIAS:
            case ROLE_GROUP_LIST_PAGE:
            case ROLE_GROUP_DETAILS_PAGE:
                tableDatas = page.tableDatas;
                break;
            default:
                tableDatas = {};
                break;
        }

        return tableDatas;
    }
);

export const getLicenseValuesByKeySelector = createSelector(
    state => ((state.adminSetting || {}).adminSettingCommon || {}).adminSettingsLicenseInfo,
    (licenseInfoMap = {}) => (licenseAlias) => {
        return licenseInfoMap[licenseAlias];
    }
);

export const getLoadedLicensesSelector = createSelector(
    state => ((state.adminSetting || {}).adminSettingCommon || {}).adminSettingsLicenseInfo,
    (licenseInfoMap) => {
        return licenseInfoMap;
    }
);

export const getPageIsUserActiveSelector = createSelector(
    state => getPlannerPageDataCollections(state),
    state => ((state[ROLE_INBOX_PAGE_ALIAS] || {}).tableDatas || {})[TABLE_NAMES.RESOURCE],
    state => ((state[ROLE_GROUP_DETAILS_PAGE] || {}).tableDatas || {})[TABLE_NAMES.RESOURCE],
    state => getTableViewPageDataCollections(state),
    state => getCurrentPageAliasSelector(state),
    (plannerDataCollections, roleInboxTableData, roleGroupDetailsPageTableData, tableViewDataCollection, pageAlias) => (resourceGuid) => {
        let resourceDataCollections;

        switch (pageAlias) {
            case PLANNER_PAGE_ALIAS:
                resourceDataCollections = plannerDataCollections;
                break;
            case ROLE_INBOX_PAGE_ALIAS:
                resourceDataCollections = [roleInboxTableData];
                break;
            case ROLE_GROUP_DETAILS_PAGE:
                resourceDataCollections = [roleGroupDetailsPageTableData];
                break;
            case TABLE_VIEW_PAGE_ALIAS:
                resourceDataCollections = tableViewDataCollection;
                break;
            default:
                resourceDataCollections = [{ data: [], byId: {} }];
                break;
        }

        const resourceData = getData(resourceDataCollections, TABLE_NAMES.RESOURCE, resourceGuid);

        const userStatus = resourceData[RESOURCE_USERSTATUS];

        return true === userStatus;
    }
);

export const getPageSelectedViewFiltersSelector = createSelector(
    (state, pageAlias) => getSelectedWorkspaceSettings(state[pageAlias].workspaces),
    (state, pageAlias) => state[pageAlias].filters,
    getFieldInfoSelector,
    (selectedWorkspace, filters, getFieldInfo) => {
        if (isEmpty(selectedWorkspace)) {
            return {};
        }

        const { filtersGuid } = selectedWorkspace;
        
        if (!filters[filtersGuid]) return {};

        const { views, selectedView } = filters[filtersGuid];
        const selectedViewFilterSettings = views[selectedView];
        const updatedSelection = getValidFiltersSelection(selectedViewFilterSettings.selection, getFieldInfo);

        return {
            ...selectedViewFilterSettings,
            guid: filtersGuid,
            selection: updatedSelection
        };
    }
);

export const getPageLicenseSize = createSelector(
    (state) => getLicenseValuesByKeySelector(state),
    (getLicenseValuesByKey) => (licenseName) => {
        return (getLicenseValuesByKey(licenseName) || {}).subscribedCount || DEFAULT_PAGE_SIZE;
    }
);

export const getStaticTranslatedMessages = createSelector(
    state => buildCommonEntityAliasesPlaceholdersSelector(state),
    (state, sectionName) => getTranslationsSelector(state, { sectionName }),
    (commonEntityAliases, staticTranslationMessages) => populateStringTemplates(staticTranslationMessages, commonEntityAliases)
);

export const getRowAriaLabelSelector = createSelector(
    (state) => getTranslationsSelector(state, translationProps),
    (selectRowTextAriaLabel) => (fieldLabel) => {
        const { selectRowTextAriaLabel: selectRowAriaLabel } = populateStringTemplates(selectRowTextAriaLabel, { name: fieldLabel });

        return selectRowAriaLabel;
    }
);