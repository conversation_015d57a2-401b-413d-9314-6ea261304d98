import { getFilterPaneCollapsed } from './filterPaneSelectors';
import { createSelector } from 'reselect';
import { JOBS_PAGE, ROLEGROUPLISTPAGE } from '../pages/pages';
import { DATA_GRID_DENSITY_KEYS, DETAILS_PANE_TAB_KEYS, J<PERSON>BS_PAGE_ALIAS, PAGE_NAMES, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE, TABLE_NAMES, UNASSIGNED_BOOKINGS_RESOURCE } from '../constants';
import { getEntitySingularAliasSelector } from './entityStructureSelectors';
import { getTranslationsSelector } from './internationalizationSelectors';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { getCurrentPageAliasSelector, getNavPageTitleSelector } from './navigationSelectors';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from '../utils/translationUtils';
import { AVATAR_SIZES, CRITERIA_ROLE_AVATAR_ICON, OUTLINED_USER_AVATAR_ICON, UNASSIGNED_AVATAR_ICON } from '../constants/avatarConsts';
import { getAvatarAltValueSelector, getAvatarImageURL } from './avatarSelectors';
import { getIsCriteriaRole, getIsProgresableStatus } from '../utils/roleRequestsUtils';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { getData } from '../utils/commonUtils';
import { JOB_DESCRIPTION, JOB_TOTALROLEGROUPS, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { getSelectedRolesEntitiesSelector } from './roleRequestsSelector';
import { getPageRoleRequestStatusDescriptionSelector } from '../selectors/roleRequestStatusSelectors';
import { PEOPLE_FINDER_DIALOG_ALIAS } from '../constants/peopleFinderConst';
import { getIsMultipleAssigneesEnabled } from './functionalityConfigurationSelectors';
import { MARKETPLACE_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { getRoleAssigneesDataByPageSelector } from './roleAssigneesSelectors';
import { JOB_FILTER_DIALOG_ALIAS } from '../constants/jobFilterDialogConsts';
import { MASS_DUPLICATE_JOBS_ALIAS } from '../constants/massDuplicateJobsConsts';

const { RESOURCE_KEY, SUGGESTIONS_KEY, ROLE_GROUP_KEY } = DETAILS_PANE_TAB_KEYS;

const getJobsPageFiltersGuid = () => 'job';

const getRoleInboxPageFiltersGuid = () => 'rolerequest';

const getMarketPlacePageFiltersGuid = () => 'rolerequest';

const getPeopleFinderFilterGuid = () => 'resource';

const getJobsPageFilters = (state) => state.jobsPage.filters[state.jobsPage.tableName];

const getResourcesPageFilters = (state) => state.resourcesPage.filters[state.resourcesPage.tableName];

const getRoleInboxPageFilters = createSelector(
    state => state.roleInboxPage,
    (roleInboxPage) => roleInboxPage.filters[roleInboxPage.tableName]
);

const getMarketplacePageFilters = createSelector(
    state => state.marketplacePage,
    (marketplacePage) => marketplacePage.filters[marketplacePage.tableName]
);

const getJobsPageFilterPaneCollapsed = (state) => getFilterPaneCollapsed(getJobsPageFilters(state));

const getResourcesPageFilterPaneCollapsed = (state) => getFilterPaneCollapsed(getResourcesPageFilters(state));

const getRoleInboxPagePageFilterPaneCollapsed = (state) => getFilterPaneCollapsed(getRoleInboxPageFilters(state));

const getMarketplacePageFilterPaneCollapsed = (state) => getFilterPaneCollapsed(getMarketplacePageFilters(state));

const getJobsPageTableDatasSelector = createSelector(
    jobsPage => jobsPage.tableDatas,
    (tableDatas) => {
        return Object.keys(tableDatas)
            .filter(currentDataKey => typeof tableDatas[currentDataKey] === "object")
            .map(currentDataKey => tableDatas[currentDataKey]);
    }
);

const getJobsPagesJobsDetailsPaneDataSelector = createSelector(
    state => getCurrentPageAliasSelector(state),
    state => getJobsPageCollections(state),
    state => getRoleGroupListPageCollections(state),
    state => getRoleGroupDetailsPageCollections(state),
    (pageAlias, jobsPageDataCollections, roleGroupListPageCollections, roleGroupDetailsPageCollections) => (tableName, id) => {
        let dataCollections = [];

        switch (pageAlias) {
            case JOBS_PAGE_ALIAS:
                dataCollections = jobsPageDataCollections;
                break;
            case ROLE_GROUP_LIST_PAGE:
                dataCollections = roleGroupListPageCollections;
                break;
            case ROLE_GROUP_DETAILS_PAGE:
                dataCollections = roleGroupDetailsPageCollections;
                break;
        }

        return getData(dataCollections, tableName, id);
    }
);

const getJobName = createSelector(
    state => state.rolegroupListPage.pageState,
    (pageState) => {
        return ((pageState || {}).params || {}).jobName;
    }
);

export const getDataGridPagedData = (pageState = {}) => {
    const { pagedData = {} } = pageState;

    return pagedData;
};

export const getDataGridPageCollections = (pageState = {}) => {
    const { pagedData = {}, tableDatas = {} } = pageState;

    return [
        ...Object.keys(pagedData).map(key => pagedData[key]),
        ...Object.keys(tableDatas).map(key => tableDatas[key])
    ];
};

const getJobsPageCollections = createSelector(
    state => state[JOBS_PAGE_ALIAS],
    (jobsPage) => getDataGridPageCollections(jobsPage)
);

const getRoleGroupListPageCollections = createSelector(
    state => state[ROLE_GROUP_LIST_PAGE],
    (roleGroupListPage) => getDataGridPageCollections(roleGroupListPage)
);

const getRoleGroupDetailsPageCollections = createSelector(
    state => state[ROLE_GROUP_DETAILS_PAGE],
    (roleGroupDetailsPage) => getDataGridPageCollections(roleGroupDetailsPage)
);

const getRoleInboxPageCollections = createSelector(
    state => state[ROLE_INBOX_PAGE_ALIAS],
    (roleInboxPage) => getDataGridPageCollections(roleInboxPage)
);

const getRoleInboxPageData = createSelector(
    state => state[ROLE_INBOX_PAGE_ALIAS],
    roleInboxPage => (roleInboxPage.pagedData.rolerequest || {}).data || [],
);

const getNotificationPageCollections = createSelector(
    state => state[NOTIFICATIONS_PAGE_ALIAS],
    (notificationsPage) => getDataGridPageCollections(notificationsPage)
);

const getPeopleFinderDialogCollections = createSelector(
    state => state[PEOPLE_FINDER_DIALOG_ALIAS],
    (peopleFinderDialog) => getDataGridPageCollections(peopleFinderDialog)
);

const getJobFilterDialogCollections = createSelector(
    state => state[JOB_FILTER_DIALOG_ALIAS],
    (jobFilterDialog) => getDataGridPageCollections(jobFilterDialog)
);

const getMassDuplicateJobsCollections = createSelector(
    state => state[MASS_DUPLICATE_JOBS_ALIAS],
    (massDuplicateJobs) => getDataGridPageCollections(massDuplicateJobs)
);

const getMarketplacePageCollections = createSelector(
    state => state[MARKETPLACE_PAGE_ALIAS],
    (marketplacePage) => getDataGridPageCollections(marketplacePage)
);

const getProfilePageCollections = createSelector(
    state => state[PROFILE_PAGE_ALIAS],
    (profilePage) => {
        const { tableDatas = {} } = profilePage;

        return [
            ...Object.values(tableDatas)
        ];
    }
);

const getRoleGroupSurrogateId = (state) => {
    const { rolegroupDetailsPage } = state;
    if (rolegroupDetailsPage.pageState.params) {
        return (rolegroupDetailsPage.pageState.params || {});
    }
};

const getRolegroupListPageParams = (state) => {
    const { rolegroupListPage } = state;
    let result = {};
    if (rolegroupListPage.pageState.params) {
        result = (rolegroupListPage.pageState.params || {});
    }

    return result;
};

const getJobEntityFieldValueSelector = createSelector(
    state => state[JOBS_PAGE_ALIAS].pagedData,
    (pagedData) => (entityId, fieldName) => {
        const { byId = {}, data = [] } = (pagedData[TABLE_NAMES.JOB] || {});

        return (entityId in byId) ? data[byId[entityId]][fieldName] : null;
    }
);

const getOpenRoleGroupListPagePath = (surrogateId) => {
    const { navigationLink: navigationLinkRGListPage } = ROLEGROUPLISTPAGE;
    const { navigationLink: navigationLinkJobsPage } = JOBS_PAGE;

    return { pathname: `${navigationLinkJobsPage}/${navigationLinkRGListPage}/${surrogateId}` };
};

const getHasJobActionRequired = (entityIds = [], data = []) => {
    return data.filter(item => entityIds.includes(item[`${TABLE_NAMES.JOB}_guid`])).some(job => job[`${TABLE_NAMES.JOB}_totalactionablerequests`] > 0);
};

const getHasResourcesActionRequired = (entityIds = [], data = []) => {
    return data.filter(item => entityIds.includes(item[`${TABLE_NAMES.RESOURCE}_guid`])).some(resource => resource[`${TABLE_NAMES.RESOURCE}_totalactionablerequests`] > 0);
};

const getCommonDataGridStaticMessagesSelector = createSelector(
    state => {
        const translationIds = ['pageOptionSuffix', 'sortAscending', 'sortDescending'];
        return getTranslationsSelector(state, { sectionName: 'dataGrid', idsArray: translationIds });
    },
    state => getEntitySingularAliasSelector(state),
    (messages, getEntityAlias) => (tableName) => {
        const entityPluralLowerAlias = getEntityAlias(tableName, { singularForm: false, capitalized: false, fallbackValue: tableName });

        return {
            ...messages,
            pageOptionSuffix: `${entityPluralLowerAlias}${messages.pageOptionSuffix}`
        };
    }
);

const getContextualDropdownStaticMessagesSelector = createSelector(
    (state) => getTranslationsSelector(state, { sectionName: 'contextualDropdown' }),
    (state) => buildCommonEntityAliasesPlaceholdersSelector(state),
    (state) => getNavPageTitleSelector(state),
    (messages, commonEntityAliasesPlaceholders, getNavPageTitle) => {
        const placeholderValues = {
            ...commonEntityAliasesPlaceholders,
            marketplaceAlias: getNavPageTitle(MARKETPLACE_PAGE_ALIAS, PAGE_NAMES.MARKETPLACE_PAGE)
        };

        return populateStringTemplates(messages, placeholderValues) || {};
    }
);

const getColumnAvatarConfig = createSelector(
    state => state.avatars,
    state => getAvatarAltValueSelector(state),
    (avatars, getAvatarAltValue) => {
        return {
            getAvatarImageURL: (resourceId) => getAvatarImageURL(avatars, resourceId, AVATAR_SIZES.TINY.label),
            getAvatarIcon: (entity) => getIsCriteriaRole(entity) ? CRITERIA_ROLE_AVATAR_ICON : UNASSIGNED_AVATAR_ICON,
            getAvatarAltText: (resourseName) => getAvatarAltValue(resourseName || UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_DESCRIPTION),
            avatarProps: { size: AVATAR_SIZES.TINY.value },
            defaultAvatarIcon: OUTLINED_USER_AVATAR_ICON
        };
    }
);

const getRoleInboxPageBaseFilterAppliedValuesSelector = createSelector(
    state => ((state.roleInboxPage.filters[TABLE_NAMES.ROLEREQUEST].baseFilter || {}).selection || {})[TABLE_NAMES.ROLEREQUEST] || [],
    (baseFilterSelection) => (filterField) => {
        const filterFieldSelection = baseFilterSelection.find(filter => filter.field === filterField) || {};

        return filterFieldSelection.value || [];
    }
);

export const shouldDisableTab = (currentTab, selectedRoles, getRoleStatusDescription, getCriteriaRoleAssignedResources, useMultipleAssignees) => {
    const { key } = currentTab;
    let disabled = false;

    switch (key) {
        case RESOURCE_KEY: {
            disabled = !selectedRoles.some((role) => {

                const assignedResources = (useMultipleAssignees && getIsCriteriaRole(role))
                    ? getCriteriaRoleAssignedResources(role[ROLEREQUEST_FIELDS.GUID], role)
                    : role[ROLEREQUEST_FIELDS.RESOURCE_GUID] ? [role[ROLEREQUEST_FIELDS.RESOURCE_GUID]] : [];

                return assignedResources.length;
            });

            break;
        }
        case SUGGESTIONS_KEY: {
            disabled = !selectedRoles.some(role => {
                const roleStatusDescription = getRoleStatusDescription(role[ROLEREQUEST_FIELDS.STATUS_GUID]);

                return getIsCriteriaRole(role) && getIsProgresableStatus(roleStatusDescription);
            });

            break;
        }
        case ROLE_GROUP_KEY:
            disabled = !selectedRoles.some(entity => entity[ROLEREQUEST_FIELDS.ROLE_GROUP_GUID] !== null);
            break;
    }

    return disabled;
};

const getRoleInboxDetailsPaneTabsCreateSelector = createSelector(
    (state) => getSelectedRolesEntitiesSelector(state),
    (state) => getPageRoleRequestStatusDescriptionSelector(state),
    (state) => getRoleAssigneesDataByPageSelector(state),
    (state) => getIsMultipleAssigneesEnabled(state),
    (selectedRoles, getRoleStatusDescription, getCriteriaRoleAssignedResources, useMultipleAssignees) => (tabsConfig = {}) => {
        const tabsKeys = Object.keys(tabsConfig);

        return tabsKeys.reduce((acc, curTabKey) => {
            const currentTab = tabsConfig[curTabKey];

            return {
                ...acc,
                [curTabKey]: {
                    ...currentTab,
                    disabled: shouldDisableTab(currentTab, selectedRoles, getRoleStatusDescription, getCriteriaRoleAssignedResources, useMultipleAssignees)
                }
            };
        }, {});
    }
);

const getDataGridPageSelectedEntitiesIdsSelector = createSelector(
    state => getCurrentPageAliasSelector(state),
    state => state[ROLE_INBOX_PAGE_ALIAS] || {},
    (pageAlias, roleInboxPage) => {
        let currentPageState = {};

        switch (pageAlias) {
            case ROLE_INBOX_PAGE_ALIAS: {
                currentPageState = roleInboxPage;
                break;
            }
        }

        const { pagedData = {}, tableName } = currentPageState;

        return (pagedData[tableName] || {}).currentEdits || [];
    }
);

const getDataGridPageGetDataWrappedSelector = createSelector(
    (state) => getCurrentPageAliasSelector(state),
    (state) => state[ROLE_INBOX_PAGE_ALIAS],
    (state) => state[NOTIFICATIONS_PAGE_ALIAS],
    (pageAlias, roleInboxPage, notificationsPage) => (tableName, id) => {
        let dataCollections = [];

        switch (pageAlias) {
            case ROLE_INBOX_PAGE_ALIAS: {
                dataCollections = getDataGridPageCollections(roleInboxPage);
                break;
            }
            case NOTIFICATIONS_PAGE_ALIAS: {
                dataCollections = getDataGridPageCollections(notificationsPage);
                break;
            }
        }

        const result = getData(dataCollections, tableName, id);

        return result;
    }
);

const getIsDataGridInCompactMode = createSelector(
    state => getCurrentPageAliasSelector(state),
    state => state,
    (pageAlias, state) => {
        const { density } = (state[pageAlias] || {}).uiOptions || {};

        return density === DATA_GRID_DENSITY_KEYS.COMPACT;
    }
);

const getCommonDataGridPropsSelector = createSelector(
    state => state.pagedData,
    state => state.uiOptions,
    state => state.tableName,
    state => state.linkFields,
    state => state.dynamicComponentFields,
    state => state.sortableFields,
    state => state.dateFields,
    (pagedData = {}, uiOptions = {}, dataGridTableName, linkFields = [], dynamicComponentFields = [], sortableFields = [], dateFields = []) => {
        const { pageSize, loading, rowCount, loadedPagesMap, data, currentEdits } = pagedData[dataGridTableName] || {};

        return {
            uiOptions,
            dataGridTableName,
            pageSize,
            loading,
            rowCount,
            loadedPagesMap,
            data,
            linkFields,
            dynamicComponentFields,
            selectedRowKeys: currentEdits,
            sortableFields,
            dateFields
        };
    }
);

const getJobRoleGroupListPaneEntityDataSelector = createSelector(
    (state) => state[JOBS_PAGE_ALIAS].pagedData[TABLE_NAMES.JOB],
    (pagedData) => (jobId) => {
        const { data, byId } = pagedData || {};

        const entity = (data || [])[(byId || {})[jobId]] || {};

        return {
            roleGroupCount: entity[JOB_TOTALROLEGROUPS] || 0,
            jobName: entity[JOB_DESCRIPTION]
        };
    }
);

const getJobRoleGroupListPaneStaticMessagesSelector = createSelector(
    (state) => getTranslationsSelector(state, { sectionName: 'entityWindow' }, ['roleGroupCountLabel']),
    (state) => buildCommonEntityAliasesPlaceholdersSelector(state),
    (state) => getJobRoleGroupListPaneEntityDataSelector(state),
    (messages, commonEntityAliasesPlaceholders, getRoleGroupEntity) => (entityId) => {
        const { roleGroupCount } = getRoleGroupEntity(entityId);

        const placeholderValues = {
            ...commonEntityAliasesPlaceholders,
            roleGroupCount
        };

        const { rolerequestgroupPluralCapitalAlias } = commonEntityAliasesPlaceholders;
        const { roleGroupCountLabel } = populateStringTemplates(messages, placeholderValues) || {};

        return { title: roleGroupCountLabel, titleIconDescription: rolerequestgroupPluralCapitalAlias };
    }
);

export {
    getJobsPageFiltersGuid,
    getJobsPageFilters,
    getJobsPageFilterPaneCollapsed,
    getJobsPageTableDatasSelector,
    getJobsPagesJobsDetailsPaneDataSelector,
    getJobsPageCollections,
    getRoleInboxPageCollections,
    getMarketplacePageCollections,
    getRolegroupListPageParams,
    getRoleGroupSurrogateId,
    getJobName,
    getOpenRoleGroupListPagePath,
    getJobEntityFieldValueSelector,
    getHasJobActionRequired,
    getHasResourcesActionRequired,
    getRoleInboxPageFilters,
    getRoleInboxPageFiltersGuid,
    getRoleInboxPagePageFilterPaneCollapsed,
    getCommonDataGridStaticMessagesSelector,
    getContextualDropdownStaticMessagesSelector,
    getColumnAvatarConfig,
    getRoleInboxPageBaseFilterAppliedValuesSelector,
    getRoleInboxDetailsPaneTabsCreateSelector,
    getNotificationPageCollections,
    getDataGridPageSelectedEntitiesIdsSelector,
    getDataGridPageGetDataWrappedSelector,
    getPeopleFinderFilterGuid,
    getPeopleFinderDialogCollections,
    getJobFilterDialogCollections,
    getMassDuplicateJobsCollections,
    getIsDataGridInCompactMode,
    getRoleInboxPageData,
    getMarketplacePageFilters,
    getMarketplacePageFilterPaneCollapsed,
    getMarketPlacePageFiltersGuid,
    getCommonDataGridPropsSelector,
    getProfilePageCollections,
    getJobRoleGroupListPaneEntityDataSelector,
    getJobRoleGroupListPaneStaticMessagesSelector,
    getResourcesPageFilters,
    getResourcesPageFilterPaneCollapsed
};
