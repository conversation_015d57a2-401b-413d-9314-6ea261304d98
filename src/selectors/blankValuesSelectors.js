import { createSelector } from 'reselect';
import { getEntitySingularAliasSelector } from './entityStructureSelectors';
import { TABLE_NAMES, JOBS_PAGE_ALIAS } from '../constants';
import { BOOKING_BAR_FIELDS_BLANK_VALUES_MAP } from '../constants/plannerConsts';
import { ENTITY_WINDOW_FIELDS_BLANK_VALUES_MAP } from '../state/entityWindow/fieldsConfig';
import { DATA_GRID_FIELDS_BLANK_VALUES } from '../constants/dataGridConsts';
import { getTranslationsSectionSelector } from '../selectors/internationalizationSelectors';
import { ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from '../utils/translationUtils';
import { ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { MARKETPLACE_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

const { BOOKING, JOB, RESOURCE, CLIENT, ROLEREQUEST } = TABLE_NAMES;

const getFieldBlankValue = (blankValuesMap = {}, fieldName, defaultBlankValue = '') => {
    return blankValuesMap[fieldName] ? blankValuesMap[fieldName] : defaultBlankValue;
};

export const getTranslatedBlankValuesSelector = createSelector(
    state => state.internationalization,
    (internationalization) => {
        return getTranslationsSectionSelector({ internationalization }, 'blankValues');
    }
);

const getDefaultBlankValue = createSelector(
    getTranslatedBlankValuesSelector,
    (blankValuesSectionTranslations) => (fieldAlias) => {
        const { noString = 'No', setString = 'set'} = blankValuesSectionTranslations;
        return fieldAlias ? `${noString} ${fieldAlias} ${setString}` : '';
    }
);

const getDefaultBlankValueForPlannerField = createSelector(
    getTranslatedBlankValuesSelector,
    (blankValuesSectionTranslations) => (fieldAlias) => {
        const { unspecifiedString = 'unspecified'} = blankValuesSectionTranslations;
        return fieldAlias ? `${fieldAlias} ${unspecifiedString}` : '';
    }
);

const getCommonFieldsBlankValuesMapSelector = createSelector(
    getEntitySingularAliasSelector,
    getTranslatedBlankValuesSelector,
    (getEntityAlias, translations) => {
        const resourceEntityAlias = getEntityAlias(RESOURCE, { singularForm: true, capitalized: true });
        return getCommonFieldsBlankValuesMap(resourceEntityAlias, translations);
    }
);

export const getPlannerFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValueForPlannerField,
    (commonFieldsBlankValuesMap, getDefaultBlankValue) => (fieldName, fieldAlias) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...BOOKING_BAR_FIELDS_BLANK_VALUES_MAP
        };
        return getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getEntityWindowFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    (commonFieldsBlankValuesMap, getDefaultBlankValue) => (fieldName, fieldAlias) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...ENTITY_WINDOW_FIELDS_BLANK_VALUES_MAP
        };
        return getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getJobsDatagridFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    (commonFieldsBlankValuesMap, getDefaultBlankValue)=> (fieldName, fieldAlias) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...DATA_GRID_FIELDS_BLANK_VALUES[JOBS_PAGE_ALIAS]
        };
        return getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getResourcesDatagridFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    (commonFieldsBlankValuesMap, getDefaultBlankValue)=> (fieldName, fieldAlias) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...DATA_GRID_FIELDS_BLANK_VALUES[RESOURCES_PAGE_ALIAS]
        };
        return getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getRoleInboxDatagridFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    state => buildCommonEntityAliasesPlaceholdersSelector(state),
    (commonFieldsBlankValuesMap, getDefaultBlankValue, commonEntityAliasesPlaceholders)=> (fieldName, fieldAlias, isNestedField = false, shouldHideValue = false) => {
        const roleInboxBlankValues = DATA_GRID_FIELDS_BLANK_VALUES[ROLE_INBOX_PAGE_ALIAS];
        const populatedBlankValues = populateStringTemplates(roleInboxBlankValues, commonEntityAliasesPlaceholders);

        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...populatedBlankValues
        };

        return isNestedField || shouldHideValue ? '' : getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getRoleGroupsDatagridFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    (commonFieldsBlankValuesMap)=> (fieldName) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...DATA_GRID_FIELDS_BLANK_VALUES[ROLE_GROUP_LIST_PAGE]
        };
        return getFieldBlankValue(blankValuesMap, fieldName, null);
    }
);

export const getTalentProfileFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    (commonFieldsBlankValuesMap, getDefaultBlankValue)=> (fieldName, fieldAlias) => {
        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            //...TALENT_PROFILE_FIELDS_BLANK_VALUES    - TP context blank values to be added here 
        };
        return getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

const getCommonFieldsBlankValuesMap = (resourceEntityAlias, translations) => {
    return {
        // [BOOKING]: {
            ...getSystemFieldsBlankValues(BOOKING, resourceEntityAlias, translations),
        // },
        // [JOB]: {
            ...getSystemFieldsBlankValues(JOB, resourceEntityAlias, translations),
        // },
        // [RESOURCE]: {
            ...getSystemFieldsBlankValues(RESOURCE, resourceEntityAlias, translations),
        // },
        // [CLIENT]: {
            ...getSystemFieldsBlankValues(CLIENT, resourceEntityAlias, translations),
        // },
        // [ROLEREQUEST]: {
            ...getSystemFieldsBlankValues(ROLEREQUEST, resourceEntityAlias, translations),
        // }
        [ROLEREQUEST_FIELDS.DESCRIPTION]: 'New Role'
    };
};

const getSystemFieldsBlankValues = (tableName, resourceEntityAlias, translations) => {
    const { notFoundString, dateNotFoundString, noChargeTypeSetString } = translations;
    const blankResourceEntityValue = `${resourceEntityAlias} ${notFoundString ? notFoundString : 'not found'}`;
    const blankDateTimeEntityValue = dateNotFoundString ? dateNotFoundString : 'Date not found';
    const blankChargetypeEntityValue = noChargeTypeSetString ? noChargeTypeSetString : 'No charge type set';

    return {
        [`${tableName}_updatedon`]: blankDateTimeEntityValue,
        [`${tableName}_createdon`]: blankDateTimeEntityValue,
        [`${tableName}_updatedby_resource_guid`]: blankResourceEntityValue,
        [`${tableName}_createdby_resource_guid`]: blankResourceEntityValue,
        [`${tableName}_chargetype_guid`]: blankChargetypeEntityValue
    }
};

export const getMarketplaceDatagridFieldBlankValueSelector = createSelector(
    getCommonFieldsBlankValuesMapSelector,
    getDefaultBlankValue,
    state => buildCommonEntityAliasesPlaceholdersSelector(state),
    (commonFieldsBlankValuesMap, getDefaultBlankValue, commonEntityAliasesPlaceholders)=> (fieldName, fieldAlias, isNestedField = false, shouldHideValue = false) => {
        const marketplaceBlankValues = DATA_GRID_FIELDS_BLANK_VALUES[MARKETPLACE_PAGE_ALIAS];
        const populatedBlankValues = populateStringTemplates(marketplaceBlankValues, commonEntityAliasesPlaceholders);

        const blankValuesMap = {
            ...commonFieldsBlankValuesMap,
            ...populatedBlankValues
        };

        return isNestedField || shouldHideValue ? '' : getFieldBlankValue(blankValuesMap, fieldName, getDefaultBlankValue(fieldAlias));
    }
);

export const getEducationSectionDefaultBlankValueSelector = createSelector(
    state => getDefaultBlankValue(state),
    (getDefaultBlackValue) => (fieldInfo) => {
        const { alias } = fieldInfo;

        return getDefaultBlackValue(alias.toLowerCase());
    }
);

export const getExperienceSectionDefaultBlankValueSelector = createSelector(
    state => getDefaultBlankValue(state),
    (getDefaultBlanckValue) => (fieldInfo) => {
        const { alias } = fieldInfo;

        return getDefaultBlanckValue(alias.toLowerCase());
    }
);