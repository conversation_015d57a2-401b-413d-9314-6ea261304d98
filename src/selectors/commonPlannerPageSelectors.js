import { isEqual } from 'lodash';
import { TABLE_NAMES } from '../constants';
import { calcFields, FIELD_LOOKUP_VALUE_TABLE, RESOURCE_LAST_LOGIN } from '../constants/fieldConsts';
import { getDisplayLongDateFormat } from '../utils/dateUtils';
import { formatFieldValue, isBlankFieldValue } from '../utils/fieldControlUtils';
import { getIsCustomPlanningDataField, getIsSortableField, getIsSystemDateField, mapDetailField } from '../utils/fieldUtils';
import { getFieldTableName, isCalculatedFieldLinked, isCustomLookupOrPlanningDataField, isTableFieldLinked } from '../utils/tableStructureUtils';
import { excludeRoleField, getFieldInfoSelector, getIsValidNotHiddenFieldInfo } from './tableStructureSelectors';
import { getWorkspaceRecordsListConfig, getWorkspaceRecordsListDetailFields } from './workspaceSelectors';

export const createDataFn = (
    data,
    byId,
    loading,
    tableName,
    tableNames,
    count,
    guid,
    startElement,
    scrollKey,
    loadedPages,
    loadedPagesMap,
    pageSize,
    rowCount,
    key
) => {
    return {
        data,
        byId,
        loading,
        tableName,
        tableNames,
        count,
        guid,
        startElement,
        scrollKey,
        loadedPages,
        loadedPagesMap,
        pageSize,
        rowCount,
        key
    };
};

export const getPagedDataObjectBase = (pagedMasterRecPlannerData, pagedMasterRecDataGuid) => {
    return (pagedMasterRecPlannerData || {})[pagedMasterRecDataGuid] || {};
};

export const getSubRecDataBase = (subRecData = {}, subRecDataGuid) => {
    return subRecData[subRecDataGuid] || {};
};

export const getRoleGroupsObjectBase = (roleGroups, roleGroupsGuid) => {
    return roleGroups[roleGroupsGuid] || {};
};

export const getIsValidDetailField = getFieldInfo => detailField => {
    const { table, field } = detailField;

    const fieldInfo = getFieldInfo(table, field);

    return getIsValidNotHiddenFieldInfo(fieldInfo);
};

export const getRlConfigBase = (workspaceSettings, getFieldInfo, sortCalcFieldFlag) => {
    const rlConfig = getWorkspaceRecordsListConfig(workspaceSettings);

    const {
        config: {
            parentDisplayFields: { headerField }
        }
    } = rlConfig;

    const headerFieldInfo = getFieldInfo(headerField.table, headerField.field);


    const customMapDetailField = (getFieldInfo) => (fieldObj) => {
        const fieldInfo = getFieldInfo(fieldObj.table, fieldObj.field);
        const isCalcField = calcFields.includes(fieldInfo?.name?.toLowerCase());

        return {
            ...fieldObj,
            sortable: isCalcField
                ? sortCalcFieldFlag
                : getIsSortableField(fieldInfo)
        };
    };

    return {
        ...rlConfig,
        config: {
            ...rlConfig.config,
            childDisplayFields: {
                ...rlConfig.config.childDisplayFields,
                detailFields: rlConfig.config.childDisplayFields
                    .detailFields
                    .filter((key) => !excludeRoleField(key.field) && getIsValidDetailField(getFieldInfo))
                    .map(customMapDetailField(getFieldInfo))
            },
            parentDisplayFields: {
                ...rlConfig.config.parentDisplayFields,
                headerField: {
                    ...rlConfig.config.parentDisplayFields.headerField,
                    sortable: getIsSortableField(headerFieldInfo)
                },
                detailFields: rlConfig.config.parentDisplayFields
                    .detailFields
                    .filter(getIsValidDetailField(getFieldInfo))
                    .map(customMapDetailField(getFieldInfo))
            }
        }
    };
};

export const getRlDisplayDataBase = (getEntryData, getFieldInfo, getFieldBlankValue, staticMessages) => {
    const cache = {};
    const tableDatasCache = {};

    return (row = {}) => {
        const { pastLabel = 'past' } = staticMessages || {};
        const { id, tableName, isHistoric = false } = row;

        const record = getEntryData(tableName, id);
        let recordUpdated = !isEqual(tableDatasCache[`${tableName}_${id}`], record);

        if (recordUpdated) {
            tableDatasCache[`${tableName}_${id}`] = record;
        }

        recordUpdated = recordUpdated || Object
            .keys(record)
            .filter(dataKey => {
                const fieldInfo = getFieldInfo(tableName, dataKey);

                return isTableFieldLinked(tableName, fieldInfo) || isCalculatedFieldLinked(tableName, fieldInfo);
            })
            .some(dataKey => {
                const fieldInfo = getFieldInfo(tableName, dataKey);
                const fieldTableName = getFieldTableName(fieldInfo, tableName);

                const linkedRecordId = record[dataKey];
                const linkedRecord = getEntryData(fieldTableName, linkedRecordId);

                const isRecordOutdated = (tableDatasCache[`${fieldTableName}_${linkedRecordId}`] || {})[`${fieldTableName}_description`] !== (linkedRecord || {})[`${fieldTableName}_description`];

                if (isRecordOutdated) {
                    tableDatasCache[`${fieldTableName}_${linkedRecordId}`] = linkedRecord;
                }

                return isRecordOutdated;
            });

        if (!recordUpdated && (`${tableName}_${id}_${isHistoric}` in cache)) {
            return cache[`${tableName}_${id}_${isHistoric}`];
        }

        const result = {};

        Object
            .keys(record)
            .forEach(dataKey => {
                const fieldInfo = getFieldInfo(tableName, dataKey);
                const fieldTableName = getFieldTableName(fieldInfo, tableName);
                const fieldValue = record[dataKey];
                const options = { dataType: getDisplayLongDateFormat(), keepLocalTime: !getIsSystemDateField(fieldInfo) };
                if (isBlankFieldValue(fieldValue)) {
                    result[dataKey] = getFieldBlankValue(dataKey);
                } else {
                    if (isTableFieldLinked(tableName, fieldInfo) || isCalculatedFieldLinked(tableName, fieldInfo)) {
                        const linkedRecordId = fieldValue;
                        const linkedRecord = getEntryData(fieldTableName, linkedRecordId);
                        const linkedFieldInfo = getFieldInfo(fieldTableName, fieldTableName + '_description');

                        result[dataKey] = formatFieldValue(linkedRecord[`${fieldTableName}_description`], linkedFieldInfo);
                    } else if (isCustomLookupOrPlanningDataField(fieldInfo)) {
                        let dateTable = FIELD_LOOKUP_VALUE_TABLE;
                        let recordKey = `${FIELD_LOOKUP_VALUE_TABLE}_value`;

                        if (getIsCustomPlanningDataField(fieldInfo)) {
                            dateTable = fieldInfo.plannerLookupTable;
                            recordKey = `${fieldInfo.plannerLookupTable}_description`;
                        }

                        const linkedRecordId = fieldValue;
                        const linkedRecord = getEntryData(dateTable, linkedRecordId);
                        result[dataKey] = formatFieldValue(linkedRecord[recordKey], fieldInfo);
                    } else {
                        result[dataKey] = fieldValue;

                        if (dataKey === `${tableName}_description` && isHistoric) {
                            result[dataKey] = `${fieldValue} (${pastLabel})`;
                        } else if (dataKey === RESOURCE_LAST_LOGIN) {
                            result[dataKey] = fieldValue;
                        } else {
                            result[dataKey] = formatFieldValue(fieldValue, fieldInfo, options);
                        }
                    }
                }
            });

        cache[`${tableName}_${id}_${isHistoric}`] = result;

        return cache[`${tableName}_${id}_${isHistoric}`];
    };
};

export const getRlDisplayFieldsBase = (workspaceSettings, applicationSettings, calcFieldsEnabled) => (tableName) => {
    const displayFields = {
        resource: getWorkspaceRecordsListDetailFields(workspaceSettings, TABLE_NAMES.RESOURCE, getFieldInfoSelector({ applicationSettings }), calcFieldsEnabled),
        job: getWorkspaceRecordsListDetailFields(workspaceSettings, TABLE_NAMES.JOB, getFieldInfoSelector({ applicationSettings }), calcFieldsEnabled),
        rolerequest: getWorkspaceRecordsListDetailFields(workspaceSettings, TABLE_NAMES.RESOURCE, getFieldInfoSelector({ applicationSettings }), calcFieldsEnabled)
    };

    return (displayFields[tableName] || []);
};

export const isPlannerFieldLoaded = (fieldOptions, workspaceGuid, fieldName) => {
    const fieldOptionExists = (workspaceGuid in fieldOptions) &&
        (fieldName in fieldOptions[workspaceGuid]);

    return !fieldOptionExists || fieldOptions[workspaceGuid][fieldName].loaded;
};