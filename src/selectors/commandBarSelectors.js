import { createSelector } from 'reselect';
import { getNavPageTitleSelector } from './navigationSelectors';
import { cloneDeep } from 'lodash';
import { getTranslationsSectionSelector } from './internationalizationSelectors';
import { getEntityInfoSelector } from './entityStructureSelectors';
import { COMMAND_BAR_MENUS_SECTION_KEYS, JOBS_PAGE_ALIAS, PAGE_NAMES, PLANNER_PAGE_ALIAS, ROLE_INBOX_PAGE_ALIAS, TABLE_NAMES } from '../constants';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from '../utils/translationUtils';
import { getEntityAlias } from '../utils/entityStructureUtils';
import { getAccessibleEntitiesIdsSelector } from './userEntityAccessSelectors';
import { getFunctionalAccessSelector } from './applicationFnasSelectors';
import { getSelectedEntitiesSelector } from './commonSelectors';
import { getPlannerSelectionByBarTableSelector } from './plannerPageSelectors';
import { MARKETPLACE_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import { getFeatureFlagSelector } from './featureManagementSelectors';
import { COMMAND_BAR_ACTION_ELEMENT_TYPES, FEATURE_FLAGS } from '../constants/globalConsts';
import { PLANNER_ACTIONS } from '../constants/plannerConsts';

const getCommandBarConfigSelector = createSelector(
    (state, pageName) => state[pageName].commandBarConfig || {},
    (state, pageName) => pageName,
    (state, pageName) => getNavPageTitleSelector(state),
    (state, _) => getFeatureFlagSelector(FEATURE_FLAGS.PLANNER_UNASSIGNED_ROLES_TOGGLE)(state),
    (state, _) => getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state),
    (commandBarConfig, pageName, getNavPageTitle, plannerUnassignedRolesToggleFeatureEnabled, isListPageAndBulkUpdateEnabled) => {
        const expandedConfig = cloneDeep(commandBarConfig);
        const defaultPageTitle = expandedConfig.pageTitleSection.pageTitle;
        expandedConfig.pageTitleSection.pageTitle = getNavPageTitle(pageName, defaultPageTitle);

        if (expandedConfig && expandedConfig.actionsSection) {
            if (!plannerUnassignedRolesToggleFeatureEnabled) {
                // If this feature flag is turned off, remove Unassigned roles menu options from config.
                expandedConfig.actionsSection.forEach((actionSection) => {
                    if (actionSection.type === COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT) {
                        actionSection.items = actionSection.items.filter(item => {
                            if (item.onClickActionType === PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROLES 
                                || item.dividerFor === PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROLES) {
                                return false
                            }
                            return true;
                        });
                    }
                })
            }
        }

        if (expandedConfig && expandedConfig.menusSection) {
            expandedConfig.menusSection.forEach((menuSection) => {
                if (menuSection.items) {
                    menuSection.items = menuSection.items.filter(item => {
                        if (item.featureFlaged === FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE) {
                            return isListPageAndBulkUpdateEnabled;
                        }

                        return true;
                    });
                }
            });
        }
        
        if (!isListPageAndBulkUpdateEnabled) {
            if (pageName === JOBS_PAGE_ALIAS && expandedConfig && expandedConfig.menusSection) {
                expandedConfig.menusSection = expandedConfig.menusSection.filter(menuSection => menuSection.key !== COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);
            }
        }

        return expandedConfig;
    }
);

const getStaticMessagesSelector = createSelector(
    (state) => state.internationalization,
    (state, pageAlias) => pageAlias,
    (state) => getEntityInfoSelector(state),
    (state) => buildCommonEntityAliasesPlaceholdersSelector(state),
    (state) => getNavPageTitleSelector(state),
    (internationalization, pageAlias, getEntityInfo, commonEntityAliasesPlaceholders, getNavPageTitle) => {
        const pageLabels = getTranslationsSectionSelector ({ internationalization }, pageAlias);
        const commonMessages = getTranslationsSectionSelector({ internationalization }, 'common');
        const commandBarTranslations = pageLabels['commandBarConfig'];
        const bookingEntityInfo = getEntityInfo(TABLE_NAMES.BOOKING);
        const jobEntityInfo = getEntityInfo(TABLE_NAMES.JOB);
        const resourceEntityInfo = getEntityInfo(TABLE_NAMES.RESOURCE);
        const jobEntityAlias = getEntityAlias(jobEntityInfo, { singularForm: false, capitalized: false, fallbackValue: TABLE_NAMES.JOB });
        const bookingEntityAlias = getEntityAlias(bookingEntityInfo, { singularForm: false, capitalized: false, fallbackValue: TABLE_NAMES.BOOKING });
        const resourceEntityAlias = getEntityAlias(resourceEntityInfo, { singularForm: false, capitalized: false, fallbackValue: TABLE_NAMES.RESOURCE });
        const marketplaceAlias = getNavPageTitle(MARKETPLACE_PAGE_ALIAS, PAGE_NAMES.MARKETPLACE_PAGE);

        const populatedStringTemplates = populateStringTemplates(commandBarTranslations, {
            bookingEntityAlias,
            jobEntityAlias,
            resourceEntityAlias,
            marketplaceAlias,
            ...commonEntityAliasesPlaceholders
        });

        return {
            ...commandBarTranslations,
            shownLabel: commonMessages.shownLabel,
            hiddenLabel: commonMessages.hiddenLabel,
            ...populatedStringTemplates
        };
    }
);

const getMenuItemActionAccessibleIdsSelector = createSelector(
    state => getFunctionalAccessSelector(state),
    state => getAccessibleEntitiesIdsSelector(state),
    (getFunctionalAccess, getAccessibleEntitiesIds) => (actionRelatedEntitiesIds = [], menuItemActionConfig = {}) => {
        let accessibleIds = [];

        if (actionRelatedEntitiesIds.length > 0) {
            const { options = {}, functionalAccessName, entityAccess, workflowAccessType, actionKey } = menuItemActionConfig;
            const { tableName: actionTableName } = options;

            const hasAccess = getFunctionalAccess(functionalAccessName) || false;

            if (hasAccess) {
                accessibleIds = getAccessibleEntitiesIds(actionTableName, actionRelatedEntitiesIds, entityAccess, functionalAccessName, actionKey, workflowAccessType);
            }
        }

        return accessibleIds;
    }
);

const getMenuItemsAdditionalProps = (config = {}, selectionByTable = {}, getMenuItemActionAccessibleIds) => {
    let additionalPropsByItem = {};
    const hasSelection = Object.values(selectionByTable).some(selection => ((selection || {}).ids || []).length > 0);

    if (!hasSelection) {
        return additionalPropsByItem;
    }

    (config.menusSection || []).forEach(menuSection => {

        (menuSection.items || []).forEach(item => {
            const { actionKey, options = {} } = item || {};

            if (options.useLabelWithCounters) {
                const { ids: selectedEntitiesIds = [] } = selectionByTable[options.tableName] || {};
                const actionAccessibleIds = getMenuItemActionAccessibleIds(selectedEntitiesIds, item);

                additionalPropsByItem = {
                    ...additionalPropsByItem,
                    [actionKey]: {
                        totalSelectedIdsCount: selectedEntitiesIds.length,
                        itemIdsCount: actionAccessibleIds.length
                    }
                };
            }
        });
    });

    return additionalPropsByItem;
};

const getRoleInboxCBConfigItemsAdditionalPropsSelector = createSelector(
    state => getCommandBarConfigSelector(state, ROLE_INBOX_PAGE_ALIAS),
    state => getMenuItemActionAccessibleIdsSelector(state),
    state => getSelectedEntitiesSelector(state, ROLE_INBOX_PAGE_ALIAS),
    (config, getMenuItemActionAccessibleIds, selectedEntitiesProps) => {
        const { tableName } = selectedEntitiesProps;

        const selectionByTable = {
            [tableName]: selectedEntitiesProps
        };

        return getMenuItemsAdditionalProps(config, selectionByTable, getMenuItemActionAccessibleIds);
    }
);

const getPlannerCBConfigItemsAdditionalPropsSelector = createSelector(
    state => getCommandBarConfigSelector(state, PLANNER_PAGE_ALIAS),
    state => getMenuItemActionAccessibleIdsSelector(state),
    state => getPlannerSelectionByBarTableSelector(state),
    (config, getMenuItemActionAccessibleIds, selectionByBarTable) => {
        return getMenuItemsAdditionalProps(config, selectionByBarTable, getMenuItemActionAccessibleIds);
    }
);

const getMarketplacePageCBConfigItemsAdditionalPropsSelector = createSelector(
    state => getCommandBarConfigSelector(state, MARKETPLACE_PAGE_ALIAS),
    state => getMenuItemActionAccessibleIdsSelector(state),
    state => getSelectedEntitiesSelector(state, MARKETPLACE_PAGE_ALIAS),
    (config, getMenuItemActionAccessibleIds, selectedEntitiesProps) => {
        const { tableName } = selectedEntitiesProps;

        const selectionByTable = {
            [tableName]: selectedEntitiesProps
        };

        return getMenuItemsAdditionalProps(config, selectionByTable, getMenuItemActionAccessibleIds);
    }
);

const getTableViewCBConfigItemsAdditionalPropsSelector = createSelector(
    state => getCommandBarConfigSelector(state, TABLE_VIEW_PAGE_ALIAS),
    state => getMenuItemActionAccessibleIdsSelector(state),
    state => getSelectedEntitiesSelector(state),
    (config, getMenuItemActionAccessibleIds, selectionByBarTable) => {
        return getMenuItemsAdditionalProps(config, selectionByBarTable, getMenuItemActionAccessibleIds);
    }
);

export {
    getCommandBarConfigSelector,
    getStaticMessagesSelector,
    getMenuItemActionAccessibleIdsSelector,
    getRoleInboxCBConfigItemsAdditionalPropsSelector,
    getPlannerCBConfigItemsAdditionalPropsSelector,
    getMarketplacePageCBConfigItemsAdditionalPropsSelector,
    getTableViewCBConfigItemsAdditionalPropsSelector
};