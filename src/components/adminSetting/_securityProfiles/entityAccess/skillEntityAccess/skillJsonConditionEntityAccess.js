import React, { useState, useEffect } from 'react';
import { FormattedMessage } from 'react-intl';
import PropTypes from 'prop-types';
import ConfigurableSelect from '../../../../common-components/configurableSelect/configurableSelect';
import { Form } from '@ant-design/compatible';
import style from './skillEntityAccessStyles.less';
import { AlertRoleAriaMessage } from '../../../../common-components/alertRoleAriaMessage';
/**
 * Layout configuration for form items.
 * @type {Object}
 * @property {Object} labelCol - Layout for the label column.
 * @property {Object} wrapperCol - Layout for the wrapper column.
 */
const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 }
};

/**
 * SkillJsonConditionEntityAccess component.
 * This component allows users to select skill categories, departments, and service lines
 * using `ConfigurableSelect` components. The selected values are combined into a JSON string
 * and passed to the parent component via the `onChange` callback.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {string|null|undefined} props.value - JSON string representing the selected values. Can be an empty string, null, or undefined.
 * @param {Function} props.onChange - Callback function to notify the parent component of changes.
 * @returns {React.ReactElement} The rendered component.
 */
const SkillJsonConditionEntityAccess = ({ value, onChange, hasError, staticLabels }) => {
    /**
     * Parsed initial value from the `value` prop.
     * If `value` is an empty string, null, or undefined, defaults to an empty object.
     * @type {Object}
     * @property {Array} categories - Selected skill categories.
     * @property {Array} departments - Selected departments.
     * @property {Array} divisions - Selected divisions.
     * @property {Array} skillEntityTypes - Selected skillEntityTypes.
     */
    /**
     * Parse the initial value from the `value` prop.
     * If `value` is an empty string, null, or undefined, defaults to an empty object.
     */
    const parseValue = (value) => {
        try {
            return value && typeof value === 'string' ? JSON.parse(value) : {};
        } catch (error) {
            console.error('Failed to parse value:', error);

            return {};
        }
    };

    /**
     * State to store the selected values for each sourceId.
     */
    const [skillEntityJsonValue, setSkillEntityJsonValue] = useState({});
    /**
     * Effect to update the state when the `value` prop changes.
     * This ensures the component is reactive to changes in the `value` prop.
     */
    useEffect(() => {
        setSkillEntityJsonValue(parseValue(value));
    }, [value]);
    /**
     * Handles changes from the `ConfigurableSelect` components.
     * Updates the state with the new value for the given `sourceId`.
     * Handles changes to the skill entity JSON by updating the relevant key with the new value.
     * The updated JSON is passed to the onChange callback and stored in the state.
     *
     * @param {string} sourceId - The source ID of the `ConfigurableSelect` component.
     * @param {Array} value - The selected value(s) from the `ConfigurableSelect` component.
     */
    const handleSkillEntityJsonChange = (sourceId, value) => {
        setSkillEntityJsonValue((prev) => {
            const updatedSkillEntityJsonValue = {
                ...prev,
                [sourceId]: value
            };
            onChange(JSON.stringify(updatedSkillEntityJsonValue));

            return updatedSkillEntityJsonValue;
        });
    };

    const { emptyJsonConditionErrorMessage } = staticLabels;
    const helpText = hasError ? emptyJsonConditionErrorMessage : null;
    const validationValue = hasError ? 'error' : '';

    return (
        <div className={style.skillEntityAccess}>
            {/* Skill Categories Select */}
            <Form.Item
                name="skillCategory"
                className={style.label}
                label={<FormattedMessage id="skillCategoriesLabel" />}
                {...formItemLayout}
                validateStatus={validationValue}
            >
                <ConfigurableSelect
                    sourceId="categories"
                    isTypeahead={false}
                    placeholder="Select categories"
                    initialValue={skillEntityJsonValue.categories || []}
                    onChange={handleSkillEntityJsonChange}
                    mode="multiple"
                />
            </Form.Item>

            {/* Departments Select */}
            <Form.Item
                name="departments"
                className={style.label}
                label={<FormattedMessage id="departmentLabel" />}
                {...formItemLayout}
                validateStatus={validationValue}
            >
                <ConfigurableSelect
                    sourceId="departments"
                    isTypeahead={false}
                    placeholder="Select department"
                    initialValue={skillEntityJsonValue.departments || []}
                    onChange={handleSkillEntityJsonChange}
                    mode="multiple"
                />
            </Form.Item>

            {/* Division Select */}
            <Form.Item
                name="divisions"
                className={style.label}
                label={<FormattedMessage id="divisionLabel" />}
                {...formItemLayout}
                validateStatus={validationValue}
            >
                <ConfigurableSelect
                    sourceId="divisions"
                    isTypeahead={false}
                    placeholder="Select divisions"
                    initialValue={skillEntityJsonValue.divisions || []}
                    onChange={handleSkillEntityJsonChange}
                    mode="multiple"
                />
            </Form.Item>

            {/* SkillEntityType Select */}
            <Form.Item
                name="skillEntityTypes"
                className={style.label}
                label={<FormattedMessage id="skillEntityTypeLabel" />}
                {...formItemLayout}
                validateStatus={validationValue}
            >
                <ConfigurableSelect
                    sourceId="skillEntityTypes"
                    isTypeahead={false}
                    placeholder="Select skill entity types"
                    initialValue={skillEntityJsonValue.skillEntityTypes || []}
                    onChange={handleSkillEntityJsonChange}
                    mode="multiple"
                />
            </Form.Item>
            {helpText && <span className={style.errorMessage}><AlertRoleAriaMessage message={helpText} /></span>}
        </div>
    );
};

/**
 * PropTypes for type checking and documentation.
 */
SkillJsonConditionEntityAccess.propTypes = {
    /**
     * JSON string representing the selected values.
     * Can be an empty string, null, or undefined.
     * @type {string|null|undefined}
     */
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.oneOf([null, undefined])]),

    /**
     * Callback function to notify the parent component of changes.
     * @type {Function}
     */
    onChange: PropTypes.func,

    /**
     * Boolean which says whether value has error
     * @type {Boolean}
     */
    hasError:PropTypes.bool,

    /**
     * Object that contain messages
     * @type {Boolean}
     */
    staticLabels:PropTypes.object
};

/**
 * Default props for the component.
 */
SkillJsonConditionEntityAccess.defaultProps = {
    value: '',
    onChange: () => { },
    hasError:false,
    staticLabels:{}
};

export default SkillJsonConditionEntityAccess;