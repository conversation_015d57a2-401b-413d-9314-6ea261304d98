import { DATA_GRID_PAGED_DATA_SUFFIX, DATA_GRID_TABLE_DATAS_SUFFIX, DATA_GRID_FILTERS_SUFFIX } from '../../constants/dataGridConsts';
import { DEFAULT_JOBS_UI_OPTIONS, DEFAULT_JOB_GRID_FIELDS, JOBS_PAGE_COMMAND_BAR_ALIAS, JOBS_PAGE_DP_ALIAS } from '../../constants/jobsPageConsts';

import createPagedDataReducer from '../commonReducers/pagedDataReducer';
import { createTableDataReducer } from '../commonReducers/tableDataReducer';
import createFiltersPaneReducer from '../filtersReducers/filtersPaneReducer';
import createDataGridSelectionReducer from './dataGridSelectionReducer';
import createDataGridDisplayFieldsReducer from './dataGridDisplayFieldsReducer';
import createFieldOptionsReducer from './dataGridFieldOptionsReducer';
import createUiOptionsReducer from './dataGridUiOptionsReducer';
import createTranslationReducer from '../translationReducer';
import { keepUnmutatedState } from '../../utils/commonUtils';
import createPageStateReducer from '../commonReducers/pageStateReducer';
import detailsPane from '../plannerPageReducer/detailsPaneReducer';
import createRoleGroupCreationModalReducer from '../roleGroupListPageReducer/roleGroupCreationModalReducer';
import { createTransform, persistCombineReducers } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import store from '../../store/configureStore';
import { getApplicationUserSurrogateId } from '../../selectors/applicationUserSelectors';
import { getPersistItemProp, getUserKey } from '../../localStorage';
import { JOBS_PAGE_PERSIST_LOCAL_STORAGE_KEY } from '../../constants/localStorageConsts';
import commandBar from '../listPageReducer/commandBarReducer';

const filtersKey = 'filters';
const pageStateKey = 'pageState';
const displayFieldsKey = 'displayFields';
const uiOptionsKey = 'uiOptions';
const selectionKey = 'selection';

const buildInbountSelectionByKey = (state, key, userId) => {
    let selection;
    const itemKey = 'persist:jobsPagePersist';
    const currentPersistState = getPersistItemProp(itemKey, key);
    const userKey = getUserKey(userId);
    let persistStateObj = {};

    switch(key) {
        case filtersKey: {
            const contextGuid = 'job';
            const { selection: filterSelection, baseFilter } = state[contextGuid];
            persistStateObj = JSON.parse(currentPersistState);

            persistStateObj[userKey] = {
                selection: filterSelection,
                baseFilter,
                contextGuid
            }

            selection = persistStateObj;
            break;
        }
        case pageStateKey: {
            const { pagesize } = state.params || {};
            persistStateObj = JSON.parse(currentPersistState);

            persistStateObj[userKey] = {
                pagesize: pagesize
            }

            selection = persistStateObj;
            break;
        }
        case displayFieldsKey: {

            persistStateObj = JSON.parse(currentPersistState);
            const defaultState = {
                'default': DEFAULT_JOB_GRID_FIELDS
            };

            selection = {
                ...defaultState,
                ...persistStateObj,
                [userKey]: state.length > 0 ? state : defaultState.default
            };

            break;
        }
        case uiOptionsKey: {
            const { density } = state;
            persistStateObj = JSON.parse(currentPersistState);

            const defaultState = {
                'default': DEFAULT_JOBS_UI_OPTIONS.density
            };

            selection = {
                ...defaultState,
                ...persistStateObj,
                [userKey]: {
                    density
                }
            };

            break;
        }
        case selectionKey: {
            const { order } = state;
            persistStateObj = JSON.parse(currentPersistState);


            selection = {
                ...persistStateObj,
                [userKey]: {
                    order
                }
            };

            break;
        }
        default:
            selection = {};
    }

    return selection;
}

const getDefaultState = (key) => {
    let selection;
    const itemKey = 'persist:jobsPagePersist';
    const currentPersistState = getPersistItemProp(itemKey, key);
    const persistStateObj = Object.keys(currentPersistState).length > 0 ? JSON.parse(currentPersistState) : {};

    switch(key) {
        case filtersKey: 
        case pageStateKey: 
            selection = persistStateObj;
            break;
        case displayFieldsKey: {
            selection = {
                'default': DEFAULT_JOB_GRID_FIELDS,
                ...persistStateObj,
            };
            break;
        }
        case uiOptionsKey: {
            const { density } = DEFAULT_JOBS_UI_OPTIONS;
            selection = {
                'default': { density },
                ...persistStateObj
            };
            break;
        }
        case selectionKey: {
            selection = persistStateObj
            break;
        }
        default:
            selection = {};
    }

    return selection;
}

const setTransorm = createTransform(
    (inbountState, key) => {
        const surrogateId = getApplicationUserSurrogateId(store.getState());
        let selection = !!surrogateId ? buildInbountSelectionByKey(inbountState, key, surrogateId) : getDefaultState(key)

        return selection
    },
    (outboundState, key) => {
        let newState = {};

        Object.keys(outboundState).forEach(key => {
            if(key.indexOf('user') == -1){
                newState = {
                    ...newState,
                    ...outboundState[key]
                }
            }
        })

        return newState
    },
    {
        whitelist: [filtersKey, pageStateKey, displayFieldsKey, uiOptionsKey, selectionKey]
    }
)

export const createDataGridReducer = (alias, initialState) => {
    const pagedDataReducer = createPagedDataReducer(`${alias}_${DATA_GRID_PAGED_DATA_SUFFIX}`, initialState.pagedData);
    const tableDatasReducer = createTableDataReducer(`${alias}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, initialState.tableDatas);
    const fieldOptionsReducer = createFieldOptionsReducer(alias);
    const roleGroupCreationModalReducer = createRoleGroupCreationModalReducer(alias);
    const persistConfig = {
        key: JOBS_PAGE_PERSIST_LOCAL_STORAGE_KEY,
        storage: storage,
        transforms: [setTransorm],
        whitelist: [filtersKey, pageStateKey, displayFieldsKey, uiOptionsKey, selectionKey]
    }

    const combinedReducers = () => {
        const { filters, pageState } = initialState;

        return ({
            filters: createFiltersPaneReducer(`${alias}_${DATA_GRID_FILTERS_SUFFIX}`, filters),
            pageState: createPageStateReducer(alias, pageState),
            displayFields: createDataGridDisplayFieldsReducer(alias),
            uiOptions: createUiOptionsReducer(alias),
            selection: createDataGridSelectionReducer(alias)
            
        })
    };

    const persistedStateReducer = persistCombineReducers(persistConfig, combinedReducers());

    return (state = initialState, action) => {
        const reducers = {
            ...state,
            ...persistedStateReducer(state, action),
            pagedData: pagedDataReducer(state.pagedData, action),
            tableDatas: tableDatasReducer(state.tableDatas, action),
            fieldOptions: fieldOptionsReducer(state.fieldOptions, action),
            detailsPane: createTranslationReducer(
                detailsPane(JOBS_PAGE_DP_ALIAS),
                state.detailsPane,
                action,
                initialState.detailsPane,
                'detailsPane',
                undefined,
                undefined,
                JOBS_PAGE_DP_ALIAS
            ),
            commandBarConfig: createTranslationReducer(
                commandBar,
                state.commandBarConfig,
                action,
                initialState.commandBarConfig,
                'jobsPage/commandBarConfig'
            ),
            roleGroupCreationModal: roleGroupCreationModalReducer(state.roleGroupCreationModal, action)
        };

        return keepUnmutatedState(state, reducers);
    };
};