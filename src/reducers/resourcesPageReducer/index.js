import initialState from '../../state/initialState';
import { keepUnmutatedState } from '../../utils/commonUtils';
import { createCommonDataGridReducer } from '../dataGridReducer/commonDataGridReducer';
import { RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_COMMAND_BAR_ALIAS, RESOURCES_PAGE_DP_ALIAS } from '../../constants/resourcesPageConsts';

export default function resourcesPageReducer(state = initialState.resourcesPage, action) {
    const commonDataGridReducer = createCommonDataGridReducer(
        RESOURCES_PAGE_ALIAS,
        state,
        {
            detailsPaneAlias: RESOURCES_PAGE_DP_ALIAS,
            commandBarTranslationLocation: 'resourcesPage/commandBarConfig',
            commandBarAlias: RESOURCES_PAGE_COMMAND_BAR_ALIAS
        }
    );

    const reducedState = {
        ...state,
        ...commonDataGridReducer(state, action),
    };

    return keepUnmutatedState(state, reducedState);
}