import { ENTITY_WINDOW_MODULES } from '../../constants';
import _ from 'lodash';
import { createWindowReducer } from './windowReducer';
import { createBatchWindowReducer } from './batchWindowReducer';
import { createSettingsReducer } from './settingsReducer';
import createCommentsReducer from './commentsReducer';
import { autoCompleteReducer } from '../commonReducers';
import { createFieldValueExplanationsReducer } from '../fieldValueExplanationsReducer';

import initialState from '../../state/initialState';
import { createEntityWindowTranslationReducer } from '../../reducers/translationReducer';
import { keepUnmutatedState } from '../../utils/commonUtils';
import createWorkHistoryReducer from '../workHistoryReducers';
import { getWorkHistoryAlias } from '../../utils/workHistoryUtils';
import { fieldValueMessagesReducer } from '../fieldValueMessagesReducer';

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_DETAILS_PANE,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    PLANNER_PAGE_MODAL_SIMPLIFIED,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    JOBS_PAGE_BATCH_MODAL,
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_REQUEST_FORM,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    PLANNER_PAGE_MOVE_TO_PROMPT,
    NOTIFICATION_PAGE_MODAL,
    MARKETPLACE_DETAILS_PANE,
    PROFILE_PAGE_MODAL,
    PREVIEW_ENTITY_MODAL,
    MANAGE_ROLE_TEMPLATES_MODAL,
    CREATE_ROLE_TEMPLATE_MODAL,
    TABLE_VIEW_MODAL,
    TABLE_VIEW_MODAL_SIMPLIFIED,
    MARKETPLACE_PAGE_MODAL,
    GLOBAL_CREATE_MODAL,
    ROLE_GROUP_MODAL,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_BATCH_MODAL
} = ENTITY_WINDOW_MODULES;

export const addTranslationWrapper = (reducerCreator, alias, initialState, type) =>{
    const insideReducer = reducerCreator(alias, initialState);

    return (state, action) => 
        createEntityWindowTranslationReducer(
            insideReducer, 
            state, 
            action, 
            initialState, 
            'entityWindow', 
            `${alias}_${type}`
        );
};

// Window
const modalWindowReducer = addTranslationWrapper(createWindowReducer, PLANNER_PAGE_MODAL, initialState.entityWindow.window[PLANNER_PAGE_MODAL], 'window');
const simplifiedModalWindowReducer = addTranslationWrapper(createWindowReducer, PLANNER_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.window[PLANNER_PAGE_MODAL_SIMPLIFIED], 'window');
const detailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, PLANNER_PAGE_DETAILS_PANE, initialState.entityWindow.window[PLANNER_PAGE_DETAILS_PANE], 'window');
const moveToPromptWindowReducer = addTranslationWrapper(createWindowReducer, PLANNER_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.window[PLANNER_PAGE_MOVE_TO_PROMPT], 'window');
const plannerPageAssigneesBudgetModalWindowReducer = addTranslationWrapper(createWindowReducer, PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.window[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], 'window');

const jobsPageModalWindowReducer = addTranslationWrapper(createWindowReducer, JOBS_PAGE_MODAL, initialState.entityWindow.window[JOBS_PAGE_MODAL], 'window');
const jobsPageDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, JOBS_PAGE_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_DETAILS_PANE], 'window');
const jobsPageRoleGroupDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], 'window');
const jobsPageResourceDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, JOBS_PAGE_RESOURCE_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_RESOURCE_DETAILS_PANE], 'window');

const batchModalWindowReducer = addTranslationWrapper(createBatchWindowReducer, PLANNER_PAGE_BATCH_MODAL, initialState.entityWindow.window[PLANNER_PAGE_BATCH_MODAL], 'window');
const batchDetailsPaneWindowReducer = addTranslationWrapper(createBatchWindowReducer, PLANNER_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.window[PLANNER_PAGE_BATCH_DETAILS_PANE], 'window');

const jobsPageBatchDetailsPaneWindowReducer = addTranslationWrapper(createBatchWindowReducer, JOBS_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_BATCHED_DETAILS_PANE], 'window');
const jobsPageBatchModalWindowReducer = addTranslationWrapper(createBatchWindowReducer, JOBS_PAGE_BATCH_MODAL, initialState.entityWindow.window[JOBS_PAGE_BATCH_MODAL], 'window');
const jobsPageRoleGroupListDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE], 'window');
const jobsPageRoleGroupListBatchDetailsPaneWindowReducer = addTranslationWrapper(createBatchWindowReducer, JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, initialState.entityWindow.window[JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE], 'window');

const resourcesPageModalWindowReducer = addTranslationWrapper(createWindowReducer, RESOURCES_PAGE_MODAL, initialState.entityWindow.window[RESOURCES_PAGE_MODAL], 'window');
const resourcesPageDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, RESOURCES_PAGE_DETAILS_PANE, initialState.entityWindow.window[RESOURCES_PAGE_DETAILS_PANE], 'window');
const resourcesPageBatchDetailsPaneWindowReducer = addTranslationWrapper(createBatchWindowReducer, RESOURCES_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.window[RESOURCES_PAGE_BATCHED_DETAILS_PANE], 'window');
const resourcesPageBatchModalWindowReducer = addTranslationWrapper(createBatchWindowReducer, RESOURCES_PAGE_BATCH_MODAL, initialState.entityWindow.window[RESOURCES_PAGE_BATCH_MODAL], 'window');

const resourceRoleByNameWindowReducer = addTranslationWrapper(createBatchWindowReducer, ROLE_REQUEST_FORM, initialState.entityWindow.window[ROLE_REQUEST_FORM], 'window');
const roleGroupPageRoleGroupDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.window[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], 'window');
const roleInboxPageDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_PAGE_DETAILS_PANE, initialState.entityWindow.window[ROLE_INBOX_PAGE_DETAILS_PANE], 'window');

const simplifiedRoleInboxPageModalReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.window[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], 'window');
const roleInboxPageModalWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_PAGE_MODAL, initialState.entityWindow.window[ROLE_INBOX_PAGE_MODAL], 'window');
const roleInboxPageAssigneesBudgetModalWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.window[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], 'window');
const roleInboxPagePublishRoleModalWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL, initialState.entityWindow.window[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], 'window');
const roleInboxPageEditRolePublicationModalWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL, initialState.entityWindow.window[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], 'window');

const createRoleGroupModalReducer = addTranslationWrapper(createWindowReducer, ROLE_GROUP_MODAL, initialState.entityWindow.window[ROLE_GROUP_MODAL], 'window');

const roleInboxPageBatchModalWindowReducer = addTranslationWrapper(createBatchWindowReducer, ROLE_INBOX_PAGE_BATCH_MODAL, initialState.entityWindow.window[ROLE_INBOX_PAGE_BATCH_MODAL], 'window');
const roleInboxPageBatchDetailsPaneWindowReducer = addTranslationWrapper(createBatchWindowReducer, ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.window[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], 'window');

const roleGroupDetailsPageMoveToPromptWindowReducer = addTranslationWrapper(createWindowReducer, ROLE_DETAILS_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.window[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], 'window');
const notificationPageModalWindowReducer = addTranslationWrapper(createWindowReducer, NOTIFICATION_PAGE_MODAL, initialState.entityWindow.window[NOTIFICATION_PAGE_MODAL], 'window');
const profilePageModalWindowReducer = addTranslationWrapper(createWindowReducer, PROFILE_PAGE_MODAL, initialState.entityWindow.window[PROFILE_PAGE_MODAL], 'window');

const manageRoleTemplatesModalReducer = addTranslationWrapper(createBatchWindowReducer, MANAGE_ROLE_TEMPLATES_MODAL, initialState.entityWindow.window[MANAGE_ROLE_TEMPLATES_MODAL], 'window');
const createRoleTemplateModalReducer = addTranslationWrapper(createWindowReducer, CREATE_ROLE_TEMPLATE_MODAL, initialState.entityWindow.window[CREATE_ROLE_TEMPLATE_MODAL], 'window');

const marketplacePageDetailsPaneWindowReducer = addTranslationWrapper(createWindowReducer, MARKETPLACE_DETAILS_PANE, initialState.entityWindow.window[MARKETPLACE_DETAILS_PANE], 'window');
const previewEntityPageModalWindowReducer = addTranslationWrapper(createWindowReducer, PREVIEW_ENTITY_MODAL, initialState.entityWindow.window[PREVIEW_ENTITY_MODAL], 'window');
const marketplacePageModalWindowReducer = addTranslationWrapper(createWindowReducer, MARKETPLACE_PAGE_MODAL, initialState.entityWindow.window[MARKETPLACE_PAGE_MODAL], 'window');

const tableViewPageModalWindowReducer = addTranslationWrapper(createWindowReducer, TABLE_VIEW_MODAL, initialState.entityWindow.window[TABLE_VIEW_MODAL], 'window');
const simplifiedTableViewPageModalWindowReducer = addTranslationWrapper(createWindowReducer, TABLE_VIEW_MODAL_SIMPLIFIED, initialState.entityWindow.window[TABLE_VIEW_MODAL_SIMPLIFIED], 'window');

const globalCreateModalWindowReducer = addTranslationWrapper(createWindowReducer, GLOBAL_CREATE_MODAL, initialState.entityWindow.window[GLOBAL_CREATE_MODAL], 'window');

// Settings
const modalSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_MODAL, initialState.entityWindow.settings[PLANNER_PAGE_MODAL], 'settings');
const simplifiedSettingsWindowReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.settings[PLANNER_PAGE_MODAL_SIMPLIFIED], 'settings');
const detailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_DETAILS_PANE, initialState.entityWindow.settings[PLANNER_PAGE_DETAILS_PANE], 'settings');
const moveToPromptSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.settings[PLANNER_PAGE_MOVE_TO_PROMPT], 'settings');
const plannerPageAssigneesBudgetModalSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.settings[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], 'settings');

const jobsPageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_MODAL, initialState.entityWindow.settings[JOBS_PAGE_MODAL], 'settings');
const jobsPageDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_DETAILS_PANE], 'settings');
const jobsPageRoleGroupDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], 'settings');

const resourcesPageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, RESOURCES_PAGE_MODAL, initialState.entityWindow.settings[RESOURCES_PAGE_MODAL], 'settings');
const resourcesPageDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, RESOURCES_PAGE_DETAILS_PANE, initialState.entityWindow.settings[RESOURCES_PAGE_DETAILS_PANE], 'settings');

const batchModalSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_BATCH_MODAL, initialState.entityWindow.settings[PLANNER_PAGE_BATCH_MODAL], 'settings');
const batchDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, PLANNER_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.settings[PLANNER_PAGE_BATCH_DETAILS_PANE], 'settings');

const jobsPageBatchDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_BATCHED_DETAILS_PANE], 'settings');
const jobsPageBatchModalSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_BATCH_MODAL, initialState.entityWindow.settings[JOBS_PAGE_BATCH_MODAL], 'settings');

const resourcesPageBatchDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, RESOURCES_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.settings[RESOURCES_PAGE_BATCHED_DETAILS_PANE], 'settings');
const resourcesPageBatchModalSettingsReducer = addTranslationWrapper(createSettingsReducer, RESOURCES_PAGE_BATCH_MODAL, initialState.entityWindow.settings[RESOURCES_PAGE_BATCH_MODAL], 'settings');

const resourceRoleByNameSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_REQUEST_FORM, initialState.entityWindow.settings[ROLE_REQUEST_FORM], 'settings');
const jobsPageRoleGroupListDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE], 'settings');
const jobsPageRoleGroupListBatchDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE], 'settings');
const jobsPageResourceDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, JOBS_PAGE_RESOURCE_DETAILS_PANE, initialState.entityWindow.settings[JOBS_PAGE_RESOURCE_DETAILS_PANE], 'settings');

const createRoleGroupModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_GROUP_MODAL, initialState.entityWindow.settings[ROLE_GROUP_MODAL], 'settings');
const roleGroupPageRoleGroupDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.settings[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], 'settings');
const roleInboxPageDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_DETAILS_PANE, initialState.entityWindow.settings[ROLE_INBOX_PAGE_DETAILS_PANE], 'settings');

const simplifiedRoleInboxPageSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.settings[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], 'settings');
const roleInboxPageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_MODAL, initialState.entityWindow.settings[ROLE_INBOX_PAGE_MODAL], 'settings');
const roleInboxPageAssigneesBudgetModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.settings[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], 'settings');
const roleInboxPagePublishRoleModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL, initialState.entityWindow.settings[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], 'settings');
const roleInboxPageEditRolePublicationModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL, initialState.entityWindow.settings[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], 'settings');

const roleInboxPageBatchModalSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_BATCH_MODAL, initialState.entityWindow.settings[ROLE_INBOX_PAGE_BATCH_MODAL], 'settings');
const roleInboxPageBatchDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.settings[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], 'settings');

const roleGroupDetailsPageMoveToPromptSettingsReducer = addTranslationWrapper(createSettingsReducer, ROLE_DETAILS_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.settings[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], 'settings');

const notificationPageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, NOTIFICATION_PAGE_MODAL, initialState.entityWindow.settings[NOTIFICATION_PAGE_MODAL], 'settings');
const profilePageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, PROFILE_PAGE_MODAL, initialState.entityWindow.settings[PROFILE_PAGE_MODAL], 'settings');

const manageRoleTemplatesModalSettingsReducer = addTranslationWrapper(createSettingsReducer, MANAGE_ROLE_TEMPLATES_MODAL, initialState.entityWindow.settings[MANAGE_ROLE_TEMPLATES_MODAL], 'settings');
const createRoleTemplateModalSettingsReducer = addTranslationWrapper(createSettingsReducer, CREATE_ROLE_TEMPLATE_MODAL, initialState.entityWindow.settings[CREATE_ROLE_TEMPLATE_MODAL], 'settings');

const marketplacePageDetailsPaneSettingsReducer = addTranslationWrapper(createSettingsReducer, MARKETPLACE_DETAILS_PANE, initialState.entityWindow.settings[MARKETPLACE_DETAILS_PANE], 'settings');
const previewEntityPageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, PREVIEW_ENTITY_MODAL, initialState.entityWindow.settings[PREVIEW_ENTITY_MODAL], 'settings');
const marketplacePageModalSettingsReducer = addTranslationWrapper(createSettingsReducer, MARKETPLACE_PAGE_MODAL, initialState.entityWindow.settings[MARKETPLACE_PAGE_MODAL], 'settings');

const tableViewModalSettingsReducer = addTranslationWrapper(createSettingsReducer, TABLE_VIEW_MODAL, initialState.entityWindow.settings[TABLE_VIEW_MODAL], 'settings');
const tableViewSimplifiedSettingsReducer = addTranslationWrapper(createSettingsReducer, TABLE_VIEW_MODAL_SIMPLIFIED, initialState.entityWindow.settings[TABLE_VIEW_MODAL_SIMPLIFIED], 'settings');

const globalCreateModalSettingsReducer = addTranslationWrapper(createSettingsReducer, GLOBAL_CREATE_MODAL, initialState.entityWindow.settings[GLOBAL_CREATE_MODAL], 'settings');

// AutoComplete
const modalAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_MODAL, initialState.entityWindow.autoComplete[PLANNER_PAGE_MODAL]);
const simplifiedModalAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.autoComplete[PLANNER_PAGE_MODAL_SIMPLIFIED]);
const detailsPaneAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_DETAILS_PANE, initialState.entityWindow.autoComplete[PLANNER_PAGE_DETAILS_PANE]);
const moveToPromptAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.autoComplete[PLANNER_PAGE_MOVE_TO_PROMPT]);
const plannerPageAssigneesBudgetModalAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.autoComplete[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]);

const jobsPageModalAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_MODAL, initialState.entityWindow.autoComplete[JOBS_PAGE_MODAL]);
const jobsPageDetailsPaneAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_DETAILS_PANE, initialState.entityWindow.autoComplete[JOBS_PAGE_DETAILS_PANE]);
const jobsPageRoleGroupDetailsPaneAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.autoComplete[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]);

const resourcesPageModalAutoCompleteReducer = autoCompleteReducer(RESOURCES_PAGE_MODAL, initialState.entityWindow.autoComplete[RESOURCES_PAGE_MODAL]);
const resourcesPageDetailsPaneAutoCompleteReducer = autoCompleteReducer(RESOURCES_PAGE_DETAILS_PANE, initialState.entityWindow.autoComplete[RESOURCES_PAGE_DETAILS_PANE]);

const batchModalAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_BATCH_MODAL, initialState.entityWindow.autoComplete[PLANNER_PAGE_BATCH_MODAL]);
const batchDetailsPaneAutoCompleteReducer = autoCompleteReducer(PLANNER_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.autoComplete[PLANNER_PAGE_BATCH_DETAILS_PANE]);

const jobsPageBatchDetailsPaneAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.autoComplete[JOBS_PAGE_BATCHED_DETAILS_PANE]);
const jobsPageBatchModalAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_BATCH_MODAL, initialState.entityWindow.autoComplete[JOBS_PAGE_BATCH_MODAL]);
const resourceRoleByNameAutoCompleteReducer = autoCompleteReducer(ROLE_REQUEST_FORM, initialState.entityWindow.autoComplete[ROLE_REQUEST_FORM]);
const jobsPageResourceDetailsPaneAutoCompleteReducer = autoCompleteReducer(JOBS_PAGE_RESOURCE_DETAILS_PANE, initialState.entityWindow.autoComplete[JOBS_PAGE_RESOURCE_DETAILS_PANE]);

const resourcesPageBatchDetailsPaneAutoCompleteReducer = autoCompleteReducer(RESOURCES_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.autoComplete[RESOURCES_PAGE_BATCHED_DETAILS_PANE]);
const resourcesPageBatchModalAutoCompleteReducer = autoCompleteReducer(RESOURCES_PAGE_BATCH_MODAL, initialState.entityWindow.autoComplete[RESOURCES_PAGE_BATCH_MODAL]);

const createRoleGroupModalAutoCompleteReducer = autoCompleteReducer(ROLE_GROUP_MODAL, initialState.entityWindow.autoComplete[ROLE_GROUP_MODAL]);
const roleGroupPageRoleGroupDetailsPaneAutoCompleteReducer = autoCompleteReducer(ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.autoComplete[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]);
const roleInboxPageDetailsPaneAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_DETAILS_PANE, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_DETAILS_PANE]);

const simplifiedRoleInboxPageModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]);
const roleInboxPageModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_MODAL, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_MODAL]);
const roleInboxPageAssigneesBudgetModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.autoComplete[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]);
const roleInboxPagePublishRoleModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]);
const roleInboxPageEditRolePublicationModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]);

const roleInboxPageBatchModalAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_BATCH_MODAL, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_BATCH_MODAL]);
const roleInboxPageBatchDetailsPaneAutoCompleteReducer = autoCompleteReducer(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.autoComplete[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]);

const roleGroupDetailsPageMoveToPromptAutoCompleteReducer = autoCompleteReducer(ROLE_DETAILS_PAGE_MOVE_TO_PROMPT, initialState.entityWindow.autoComplete[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]);

const notificationPageModalAutoCompleteReducer = autoCompleteReducer(NOTIFICATION_PAGE_MODAL, initialState.entityWindow.autoComplete[NOTIFICATION_PAGE_MODAL]);
const profilePageModalAutoCompleteReducer = autoCompleteReducer(PROFILE_PAGE_MODAL, initialState.entityWindow.autoComplete[PROFILE_PAGE_MODAL]);

const marketplacePageDetailsPaneAutoCompleteReducer = autoCompleteReducer(MARKETPLACE_DETAILS_PANE, initialState.entityWindow.autoComplete[MARKETPLACE_DETAILS_PANE]);
const previewEntityPageModalAutoCompleteReducer = autoCompleteReducer(PREVIEW_ENTITY_MODAL, initialState.entityWindow.autoComplete[PREVIEW_ENTITY_MODAL]);
const marketplacePageModalAutoCompleteReducer = autoCompleteReducer(MARKETPLACE_PAGE_MODAL, initialState.entityWindow.autoComplete[MARKETPLACE_PAGE_MODAL]);

const manageRoleTemplatesAutoCompleteReducer = autoCompleteReducer(MANAGE_ROLE_TEMPLATES_MODAL, initialState.entityWindow.autoComplete[MANAGE_ROLE_TEMPLATES_MODAL]);
const createRoleTemplateAutoCompleteReducer = autoCompleteReducer(CREATE_ROLE_TEMPLATE_MODAL, initialState.entityWindow.autoComplete[CREATE_ROLE_TEMPLATE_MODAL]);

const tableViewModalAutoCompleteReducer = autoCompleteReducer(TABLE_VIEW_MODAL, initialState.entityWindow.autoComplete[TABLE_VIEW_MODAL]);
const tableViewModalSimplifiedAutoCompleteReducer = autoCompleteReducer(TABLE_VIEW_MODAL_SIMPLIFIED, initialState.entityWindow.autoComplete[TABLE_VIEW_MODAL_SIMPLIFIED]);

const globalCrateModalAutoCompleteReducer = autoCompleteReducer(GLOBAL_CREATE_MODAL, initialState.entityWindow.autoComplete[GLOBAL_CREATE_MODAL]);
// FiekldValueExplanation
const modalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_MODAL, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_MODAL], 'explanations');
const simplifiedModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_MODAL_SIMPLIFIED, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_MODAL_SIMPLIFIED], 'explanations');
const detailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_DETAILS_PANE], 'explanations');
const plannerPageAssigneesBudgetModalFieldValueExplanationReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], 'explanations');

const jobsPageModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_MODAL, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_MODAL], 'explanations');
const jobsPageDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_DETAILS_PANE], 'explanations');
const jobsPageRoleGroupDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], 'explanations');

const resourcesPageModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, RESOURCES_PAGE_MODAL, initialState.entityWindow.fieldValueExplanations[RESOURCES_PAGE_MODAL], 'explanations');
const resourcesPageDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, RESOURCES_PAGE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[RESOURCES_PAGE_DETAILS_PANE], 'explanations');

const tableViewModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, TABLE_VIEW_MODAL, initialState.entityWindow.fieldValueExplanations[TABLE_VIEW_MODAL], 'explanations');
const tableViewSimplifiedModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, TABLE_VIEW_MODAL_SIMPLIFIED, initialState.entityWindow.fieldValueExplanations[TABLE_VIEW_MODAL_SIMPLIFIED], 'explanations');
//Do we need batch explanations reducer ? We might unless we update the explanations on entity change
const batchModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_BATCH_MODAL, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_BATCH_MODAL], 'explanations');
const batchDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, PLANNER_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_BATCH_DETAILS_PANE], 'explanations');

const jobsPageBatchDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_BATCHED_DETAILS_PANE], 'explanations');
const jobsPageBatchModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_BATCH_MODAL, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_BATCH_MODAL], 'explanations');

const resourcesPageBatchDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, RESOURCES_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[RESOURCES_PAGE_BATCHED_DETAILS_PANE], 'explanations');
const resourcesPageBatchModalFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, RESOURCES_PAGE_BATCH_MODAL, initialState.entityWindow.fieldValueExplanations[RESOURCES_PAGE_BATCH_MODAL], 'explanations');

const resourceRoleByNameFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, ROLE_REQUEST_FORM, initialState.entityWindow.fieldValueExplanations[ROLE_REQUEST_FORM], 'explanations');
const jobsPageRoleGroupListDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE], 'explanations');
const jobsPageRoleGroupListBatchDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE], 'explanations');
const jobsPageResourceDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, JOBS_PAGE_RESOURCE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[JOBS_PAGE_RESOURCE_DETAILS_PANE], 'explanations');

const roleGroupPageRoleGroupDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], 'explanations');
const roleInboxPageDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, ROLE_INBOX_PAGE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_DETAILS_PANE], 'explanations');

const createRoleGroupModalExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_GROUP_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_GROUP_MODAL],
    'explanations'
);

const simplifiedRoleInboxPageModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED],
    'explanations'
);

const roleInboxPageModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_MODAL],
    'explanations'
);

const roleInboxPageAssigneesBudgetModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL],
    'explanations'
);

const roleInboxPagePublishRoleModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL],
    'explanations'
);

const roleInboxPageEditRolePublicationModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL],
    'explanations'
);

const roleGroupDetailsPageMoveToPrompExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    initialState.entityWindow.fieldValueExplanations[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT],
    'explanations'
);

const moveToPrompExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    PLANNER_PAGE_MOVE_TO_PROMPT,
    initialState.entityWindow.fieldValueExplanations[PLANNER_PAGE_MOVE_TO_PROMPT],
    'explanations'
);

const notificationPageModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    NOTIFICATION_PAGE_MODAL,
    initialState.entityWindow.fieldValueExplanations[NOTIFICATION_PAGE_MODAL],
    'explanations'
);

const profilePageModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    PROFILE_PAGE_MODAL,
    initialState.entityWindow.fieldValueExplanations[PROFILE_PAGE_MODAL],
    'explanations'
);

const previewEntityModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    PREVIEW_ENTITY_MODAL,
    initialState.entityWindow.fieldValueExplanations[PREVIEW_ENTITY_MODAL],
    'explanations'
);

const roleInboxPageBatchModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_BATCH_MODAL],
    'explanations'
);

const roleInboxPageBatchDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    initialState.entityWindow.fieldValueExplanations[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE],
    'explanations'
);

const marketplacePageDetailsPaneFieldValueExplanationsReducer = addTranslationWrapper(createFieldValueExplanationsReducer, MARKETPLACE_DETAILS_PANE, initialState.entityWindow.fieldValueExplanations[MARKETPLACE_DETAILS_PANE], 'explanations');

const marketplacePageModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    MARKETPLACE_PAGE_MODAL,
    initialState.entityWindow.fieldValueExplanations[MARKETPLACE_PAGE_MODAL],
    'explanations'
);

const manageMyTemplatesModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    MANAGE_ROLE_TEMPLATES_MODAL,
    initialState.entityWindow.fieldValueExplanations[MANAGE_ROLE_TEMPLATES_MODAL],
    'explanations'
);

const createRoleTemplateModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    CREATE_ROLE_TEMPLATE_MODAL,
    initialState.entityWindow.fieldValueExplanations[CREATE_ROLE_TEMPLATE_MODAL],
    'explanations'
);

const globalCrateModalFieldValueExplanationsReducer = addTranslationWrapper(
    createFieldValueExplanationsReducer,
    GLOBAL_CREATE_MODAL,
    initialState.entityWindow.fieldValueExplanations[GLOBAL_CREATE_MODAL],
    'explanations'
);

// Comments
const modalCommentsReducer = createCommentsReducer(PLANNER_PAGE_MODAL, initialState.entityWindow.comments[PLANNER_PAGE_MODAL]);
const detailsPaneCommentsReducer = createCommentsReducer(PLANNER_PAGE_DETAILS_PANE, initialState.entityWindow.comments[PLANNER_PAGE_DETAILS_PANE]);

const batchModalCommentsReducer = createCommentsReducer(PLANNER_PAGE_BATCH_MODAL, initialState.entityWindow.comments[PLANNER_PAGE_BATCH_MODAL]);
const batchDetailsPaneCommentsReducer = createCommentsReducer(PLANNER_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.comments[PLANNER_PAGE_BATCH_DETAILS_PANE]);

const jobsPageModalCommentsReducer = createCommentsReducer(JOBS_PAGE_MODAL, initialState.entityWindow.comments[JOBS_PAGE_MODAL]);
const jobsPageDetailsPaneCommentsReducer = createCommentsReducer(JOBS_PAGE_DETAILS_PANE, initialState.entityWindow.comments[JOBS_PAGE_DETAILS_PANE]);

const jobsPageBatchModalCommentsReducer = createCommentsReducer(JOBS_PAGE_BATCH_MODAL, initialState.entityWindow.comments[JOBS_PAGE_BATCH_MODAL]);
const jobsPageBatchDetailsPaneCommentsReducer = createCommentsReducer(JOBS_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.comments[JOBS_PAGE_BATCHED_DETAILS_PANE]);

const resourcesPageModalCommentsReducer = createCommentsReducer(RESOURCES_PAGE_MODAL, initialState.entityWindow.comments[RESOURCES_PAGE_MODAL]);
const resourcesPageDetailsPaneCommentsReducer = createCommentsReducer(RESOURCES_PAGE_DETAILS_PANE, initialState.entityWindow.comments[RESOURCES_PAGE_DETAILS_PANE]);

const resourcesPageBatchModalCommentsReducer = createCommentsReducer(RESOURCES_PAGE_BATCH_MODAL, initialState.entityWindow.comments[RESOURCES_PAGE_BATCH_MODAL]);
const resourcesPageBatchDetailsPaneCommentsReducer = createCommentsReducer(RESOURCES_PAGE_BATCHED_DETAILS_PANE, initialState.entityWindow.comments[RESOURCES_PAGE_BATCHED_DETAILS_PANE]);

const roleInboxPageModalCommentsReducer = createCommentsReducer(ROLE_INBOX_PAGE_MODAL, initialState.entityWindow.comments[ROLE_INBOX_PAGE_MODAL]);
const roleInboxPageDetailsPaneCommentsReducer = createCommentsReducer(ROLE_INBOX_PAGE_DETAILS_PANE, initialState.entityWindow.comments[ROLE_INBOX_PAGE_DETAILS_PANE]);

const roleInboxPageBatchModalCommentsReducer = createCommentsReducer(ROLE_INBOX_PAGE_BATCH_MODAL, initialState.entityWindow.comments[ROLE_INBOX_PAGE_BATCH_MODAL]);
const roleInboxPageBatchDetailsPaneCommentsReducer = createCommentsReducer(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE, initialState.entityWindow.comments[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]);

const rolesBoardPageModalCommentsReducer = createCommentsReducer(MARKETPLACE_PAGE_MODAL, initialState.entityWindow.comments[MARKETPLACE_PAGE_MODAL]);
const rolesBoardPageDetailsPaneCommentsReducer = createCommentsReducer(MARKETPLACE_DETAILS_PANE, initialState.entityWindow.comments[MARKETPLACE_DETAILS_PANE]);

const notificationPageModalCommentsReducer = createCommentsReducer(NOTIFICATION_PAGE_MODAL, initialState.entityWindow.comments[NOTIFICATION_PAGE_MODAL]);

// work history
const plannerPageDetailsPaneWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(PLANNER_PAGE_DETAILS_PANE), initialState.entityWindow.workHistory[PLANNER_PAGE_DETAILS_PANE]);
const jobsPageDetailsPaneWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(JOBS_PAGE_RESOURCE_DETAILS_PANE), initialState.entityWindow.workHistory[JOBS_PAGE_RESOURCE_DETAILS_PANE]);
const resourcesPageDetailsPaneWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(RESOURCES_PAGE_DETAILS_PANE), initialState.entityWindow.workHistory[RESOURCES_PAGE_DETAILS_PANE]);
const roleInboxDetailsPaneWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(ROLE_INBOX_PAGE_DETAILS_PANE), initialState.entityWindow.workHistory[ROLE_INBOX_PAGE_DETAILS_PANE]);
const plannerPageEntityWindowWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(PLANNER_PAGE_MODAL), initialState.entityWindow.workHistory[PLANNER_PAGE_MODAL]);
const jobsPageEntityWindowWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(JOBS_PAGE_MODAL), initialState.entityWindow.workHistory[JOBS_PAGE_MODAL]);
const resourcesPageEntityWindowWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(RESOURCES_PAGE_MODAL), initialState.entityWindow.workHistory[RESOURCES_PAGE_MODAL]);
const roleInboxEntityWindowWorkHistoryReducer = createWorkHistoryReducer(getWorkHistoryAlias(ROLE_INBOX_PAGE_MODAL), initialState.entityWindow.workHistory[ROLE_INBOX_PAGE_MODAL]);

export default function entityWindowReducer(state = initialState.entityWindow, action) {
    const { window, autoComplete, settings, fieldValueExplanations, comments, workHistory, fieldValueMessages, } = state;

    const reducedState = {
        window: {
            [PLANNER_PAGE_MODAL]: modalWindowReducer(window[PLANNER_PAGE_MODAL], { ...action, settings }),
            [PLANNER_PAGE_MODAL_SIMPLIFIED]: simplifiedModalWindowReducer(window[PLANNER_PAGE_MODAL_SIMPLIFIED], { ...action, settings }),
            [PLANNER_PAGE_BATCH_MODAL]: batchModalWindowReducer(window[PLANNER_PAGE_BATCH_MODAL], {...action, settings}),
            [PLANNER_PAGE_DETAILS_PANE]: detailsPaneWindowReducer(window[PLANNER_PAGE_DETAILS_PANE], { ...action, settings }),
            [PLANNER_PAGE_BATCH_DETAILS_PANE]: batchDetailsPaneWindowReducer(window[PLANNER_PAGE_BATCH_DETAILS_PANE], {...action, settings}),
            [PLANNER_PAGE_MOVE_TO_PROMPT]: moveToPromptWindowReducer(window[PLANNER_PAGE_MOVE_TO_PROMPT], {...action, settings}),
            [PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]:plannerPageAssigneesBudgetModalWindowReducer(window[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], {...action, settings}),
            [JOBS_PAGE_MODAL]: jobsPageModalWindowReducer(window[JOBS_PAGE_MODAL], { ...action, settings }),
            [JOBS_PAGE_DETAILS_PANE]: jobsPageDetailsPaneWindowReducer(window[JOBS_PAGE_DETAILS_PANE], { ...action, settings }),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneWindowReducer(window[RESOURCES_PAGE_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: jobsPageRoleGroupDetailsPaneWindowReducer(window[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_BATCHED_DETAILS_PANE]: jobsPageBatchDetailsPaneWindowReducer(window[JOBS_PAGE_BATCHED_DETAILS_PANE], {...action, settings}),
            [RESOURCES_PAGE_MODAL]: resourcesPageModalWindowReducer(window[RESOURCES_PAGE_MODAL], { ...action, settings }),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneWindowReducer(window[RESOURCES_PAGE_DETAILS_PANE], { ...action, settings }),
            [RESOURCES_PAGE_BATCH_MODAL]: resourcesPageBatchModalWindowReducer(window[RESOURCES_PAGE_BATCH_MODAL], {...action, settings}),
            [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: resourcesPageBatchDetailsPaneWindowReducer(window[RESOURCES_PAGE_BATCHED_DETAILS_PANE], {...action, settings}),
            [JOBS_PAGE_BATCH_MODAL]: jobsPageBatchModalWindowReducer(window[JOBS_PAGE_BATCH_MODAL], {...action, settings}),
            [ROLE_REQUEST_FORM]: resourceRoleByNameWindowReducer(window[ROLE_REQUEST_FORM], {...action, settings}),
            [JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: jobsPageRoleGroupListDetailsPaneWindowReducer(window[JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: jobsPageRoleGroupListBatchDetailsPaneWindowReducer(window[JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_RESOURCE_DETAILS_PANE]: jobsPageResourceDetailsPaneWindowReducer(window[JOBS_PAGE_RESOURCE_DETAILS_PANE], { ...action, settings }),
            [ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: roleGroupPageRoleGroupDetailsPaneWindowReducer(window[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], { ...action, settings }),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxPageModalWindowReducer(window[ROLE_INBOX_PAGE_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_BATCH_MODAL]: roleInboxPageBatchModalWindowReducer(window[ROLE_INBOX_PAGE_BATCH_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxPageDetailsPaneWindowReducer(window[ROLE_INBOX_PAGE_DETAILS_PANE], { ...action, settings }),
            [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: roleInboxPageBatchDetailsPaneWindowReducer(window[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], { ...action, settings }),
            [ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: simplifiedRoleInboxPageModalReducer(window[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], { ...action, settings }),
            [ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: roleInboxPageAssigneesBudgetModalWindowReducer(window[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: roleInboxPagePublishRoleModalWindowReducer(window[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: roleInboxPageEditRolePublicationModalWindowReducer(window[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], { ...action, settings }),
            [ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: roleGroupDetailsPageMoveToPromptWindowReducer(window[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], { ...action, settings }),
            [NOTIFICATION_PAGE_MODAL]: notificationPageModalWindowReducer(window[NOTIFICATION_PAGE_MODAL], { ...action, settings }),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneWindowReducer(window[MARKETPLACE_DETAILS_PANE], { ...action, settings }),
            [PROFILE_PAGE_MODAL]: profilePageModalWindowReducer(window[PROFILE_PAGE_MODAL], { ...action, settings }),
            [PREVIEW_ENTITY_MODAL]: previewEntityPageModalWindowReducer(window[PREVIEW_ENTITY_MODAL], { ...action, settings }),
            [MANAGE_ROLE_TEMPLATES_MODAL]: manageRoleTemplatesModalReducer(window[MANAGE_ROLE_TEMPLATES_MODAL], { ...action, settings }),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneWindowReducer(window[MARKETPLACE_DETAILS_PANE], { ...action, settings }),
            [CREATE_ROLE_TEMPLATE_MODAL]: createRoleTemplateModalReducer(window[CREATE_ROLE_TEMPLATE_MODAL], { ...action, settings }),
            [TABLE_VIEW_MODAL]:  tableViewPageModalWindowReducer(window[TABLE_VIEW_MODAL], { ...action, settings }),
            [TABLE_VIEW_MODAL_SIMPLIFIED]: simplifiedTableViewPageModalWindowReducer(window[TABLE_VIEW_MODAL_SIMPLIFIED], { ...action, settings }),
            [MARKETPLACE_PAGE_MODAL]: marketplacePageModalWindowReducer(window[MARKETPLACE_PAGE_MODAL], { ...action, settings }),
            [GLOBAL_CREATE_MODAL]: globalCreateModalWindowReducer(window[GLOBAL_CREATE_MODAL], { ...action, settings }),
            [ROLE_GROUP_MODAL]: createRoleGroupModalReducer(window[ROLE_GROUP_MODAL], { ...action, settings })
        },
        settings: {
            [PLANNER_PAGE_MODAL]: modalSettingsReducer(settings[PLANNER_PAGE_MODAL], action),
            [PLANNER_PAGE_MODAL_SIMPLIFIED]: simplifiedSettingsWindowReducer(settings[PLANNER_PAGE_MODAL_SIMPLIFIED], action),
            [PLANNER_PAGE_BATCH_MODAL]: batchModalSettingsReducer(settings[PLANNER_PAGE_BATCH_MODAL], {...action, settings}),
            [PLANNER_PAGE_DETAILS_PANE]: detailsPaneSettingsReducer(settings[PLANNER_PAGE_DETAILS_PANE], { ...action, settings }),
            [PLANNER_PAGE_BATCH_DETAILS_PANE]: batchDetailsPaneSettingsReducer(settings[PLANNER_PAGE_BATCH_DETAILS_PANE], {...action, settings}),
            [PLANNER_PAGE_MOVE_TO_PROMPT]: moveToPromptSettingsReducer(settings[PLANNER_PAGE_MOVE_TO_PROMPT], { ...action, settings }),
            [PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: plannerPageAssigneesBudgetModalSettingsReducer(settings[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], { ...action, settings }),
            [JOBS_PAGE_MODAL]: jobsPageModalSettingsReducer(settings[JOBS_PAGE_MODAL], { ...action, settings }),
            [JOBS_PAGE_DETAILS_PANE]: jobsPageDetailsPaneSettingsReducer(settings[JOBS_PAGE_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: jobsPageRoleGroupDetailsPaneSettingsReducer(settings[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_BATCHED_DETAILS_PANE]: jobsPageBatchDetailsPaneSettingsReducer(settings[JOBS_PAGE_BATCHED_DETAILS_PANE], {...action, settings}),
            [JOBS_PAGE_BATCH_MODAL]: jobsPageBatchModalSettingsReducer(settings[JOBS_PAGE_BATCH_MODAL], {...action, settings}),
            [JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: jobsPageRoleGroupListDetailsPaneSettingsReducer(settings[JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE], { ...action, settings }),
            [JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: jobsPageRoleGroupListBatchDetailsPaneSettingsReducer(settings[JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE], { ...action, settings }),
            [RESOURCES_PAGE_MODAL]: resourcesPageModalSettingsReducer(settings[RESOURCES_PAGE_MODAL], { ...action, settings }),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneSettingsReducer(settings[RESOURCES_PAGE_DETAILS_PANE], { ...action, settings }),
            [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: resourcesPageBatchDetailsPaneSettingsReducer(settings[RESOURCES_PAGE_BATCHED_DETAILS_PANE], {...action, settings}),
            [RESOURCES_PAGE_BATCH_MODAL]: resourcesPageBatchModalSettingsReducer(settings[RESOURCES_PAGE_BATCH_MODAL], {...action, settings}),
            [ROLE_REQUEST_FORM]: resourceRoleByNameSettingsReducer(settings[ROLE_REQUEST_FORM], {...action, settings}),
            [JOBS_PAGE_RESOURCE_DETAILS_PANE]: jobsPageResourceDetailsPaneSettingsReducer(settings[JOBS_PAGE_RESOURCE_DETAILS_PANE], {...action, settings}),
            [ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: roleGroupPageRoleGroupDetailsPaneSettingsReducer(settings[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], {...action, settings}),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxPageModalSettingsReducer(settings[ROLE_INBOX_PAGE_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_BATCH_MODAL]: roleInboxPageBatchModalSettingsReducer(settings[ROLE_INBOX_PAGE_BATCH_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxPageDetailsPaneSettingsReducer(settings[ROLE_INBOX_PAGE_DETAILS_PANE], {...action, settings}),
            [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: roleInboxPageBatchDetailsPaneSettingsReducer(settings[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], {...action, settings}),
            [ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: simplifiedRoleInboxPageSettingsReducer(settings[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], { ...action, settings }),
            [ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: roleInboxPageAssigneesBudgetModalSettingsReducer(settings[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: roleInboxPagePublishRoleModalSettingsReducer(settings[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], { ...action, settings }),
            [ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: roleInboxPageEditRolePublicationModalSettingsReducer(settings[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], { ...action, settings }),
            [ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: roleGroupDetailsPageMoveToPromptSettingsReducer(settings[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], { ...action, settings }),
            [NOTIFICATION_PAGE_MODAL]: notificationPageModalSettingsReducer(settings[NOTIFICATION_PAGE_MODAL], action),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneSettingsReducer(settings[MARKETPLACE_DETAILS_PANE], action),
            [PROFILE_PAGE_MODAL]: profilePageModalSettingsReducer(settings[PROFILE_PAGE_MODAL], action),
            [PREVIEW_ENTITY_MODAL]: previewEntityPageModalSettingsReducer(settings[PREVIEW_ENTITY_MODAL], action),
            [MANAGE_ROLE_TEMPLATES_MODAL]: manageRoleTemplatesModalSettingsReducer(settings[MANAGE_ROLE_TEMPLATES_MODAL], action),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneSettingsReducer(settings[MARKETPLACE_DETAILS_PANE], action),
            [CREATE_ROLE_TEMPLATE_MODAL]: createRoleTemplateModalSettingsReducer(settings[CREATE_ROLE_TEMPLATE_MODAL], action),
            [TABLE_VIEW_MODAL]: tableViewModalSettingsReducer(settings[TABLE_VIEW_MODAL], action),
            [TABLE_VIEW_MODAL_SIMPLIFIED]: tableViewSimplifiedSettingsReducer(settings[TABLE_VIEW_MODAL_SIMPLIFIED], action),
            [MARKETPLACE_PAGE_MODAL]: marketplacePageModalSettingsReducer(settings[MARKETPLACE_PAGE_MODAL], action),
            [GLOBAL_CREATE_MODAL]: globalCreateModalSettingsReducer(settings[GLOBAL_CREATE_MODAL], action),
            [ROLE_GROUP_MODAL]: createRoleGroupModalSettingsReducer(settings[ROLE_GROUP_MODAL], action)
        },
        autoComplete: {
            [PLANNER_PAGE_MODAL]: modalAutoCompleteReducer(autoComplete[PLANNER_PAGE_MODAL], action),
            [PLANNER_PAGE_MODAL_SIMPLIFIED]: simplifiedModalAutoCompleteReducer(autoComplete[PLANNER_PAGE_MODAL_SIMPLIFIED], action),
            [PLANNER_PAGE_BATCH_MODAL]: batchModalAutoCompleteReducer(autoComplete[PLANNER_PAGE_BATCH_MODAL], action),
            [PLANNER_PAGE_DETAILS_PANE]: detailsPaneAutoCompleteReducer(autoComplete[PLANNER_PAGE_DETAILS_PANE], action),
            [PLANNER_PAGE_BATCH_DETAILS_PANE]: batchDetailsPaneAutoCompleteReducer(autoComplete[PLANNER_PAGE_BATCH_DETAILS_PANE], action),
            [PLANNER_PAGE_MOVE_TO_PROMPT]: moveToPromptAutoCompleteReducer(autoComplete[PLANNER_PAGE_MOVE_TO_PROMPT], action),
            [PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: plannerPageAssigneesBudgetModalAutoCompleteReducer(autoComplete[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], action),
            [JOBS_PAGE_MODAL]: jobsPageModalAutoCompleteReducer(autoComplete[JOBS_PAGE_MODAL], action),
            [JOBS_PAGE_DETAILS_PANE]: jobsPageDetailsPaneAutoCompleteReducer(autoComplete[JOBS_PAGE_DETAILS_PANE], action),
            [JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: jobsPageRoleGroupDetailsPaneAutoCompleteReducer(autoComplete[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], action),
            [JOBS_PAGE_BATCHED_DETAILS_PANE]: jobsPageBatchDetailsPaneAutoCompleteReducer(autoComplete[JOBS_PAGE_BATCHED_DETAILS_PANE], action),
            [RESOURCES_PAGE_MODAL]: resourcesPageModalAutoCompleteReducer(autoComplete[RESOURCES_PAGE_MODAL], action),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneAutoCompleteReducer(autoComplete[RESOURCES_PAGE_DETAILS_PANE], action),
            [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: resourcesPageBatchDetailsPaneAutoCompleteReducer(autoComplete[RESOURCES_PAGE_BATCHED_DETAILS_PANE], action),
            [RESOURCES_PAGE_BATCH_MODAL]: resourcesPageBatchModalAutoCompleteReducer(autoComplete[RESOURCES_PAGE_BATCH_MODAL], action),
            [ROLE_REQUEST_FORM]: resourceRoleByNameAutoCompleteReducer(autoComplete[ROLE_REQUEST_FORM], action),
            [JOBS_PAGE_RESOURCE_DETAILS_PANE]: jobsPageResourceDetailsPaneAutoCompleteReducer(autoComplete[JOBS_PAGE_RESOURCE_DETAILS_PANE], action),
            [ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: roleGroupPageRoleGroupDetailsPaneAutoCompleteReducer(autoComplete[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxPageModalAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_MODAL], action),
            [ROLE_INBOX_PAGE_BATCH_MODAL]: roleInboxPageBatchModalAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_BATCH_MODAL], action),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxPageDetailsPaneAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: roleInboxPageBatchDetailsPaneAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: simplifiedRoleInboxPageModalAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], action),
            [ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: roleInboxPageAssigneesBudgetModalAutoCompleteReducer(autoComplete[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], action),
            [ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: roleInboxPagePublishRoleModalAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], action),
            [ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: roleInboxPageEditRolePublicationModalAutoCompleteReducer(autoComplete[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], action),
            [ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: roleGroupDetailsPageMoveToPromptAutoCompleteReducer(autoComplete[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], action),
            [NOTIFICATION_PAGE_MODAL]: notificationPageModalAutoCompleteReducer(autoComplete[NOTIFICATION_PAGE_MODAL], action),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneAutoCompleteReducer(autoComplete[MARKETPLACE_DETAILS_PANE], action),
            [PROFILE_PAGE_MODAL]: profilePageModalAutoCompleteReducer(autoComplete[PROFILE_PAGE_MODAL], action),
            [PREVIEW_ENTITY_MODAL]: previewEntityPageModalAutoCompleteReducer(autoComplete[PREVIEW_ENTITY_MODAL], action),
            [MANAGE_ROLE_TEMPLATES_MODAL]: manageRoleTemplatesAutoCompleteReducer(autoComplete[MANAGE_ROLE_TEMPLATES_MODAL], action),
            [CREATE_ROLE_TEMPLATE_MODAL]: createRoleTemplateAutoCompleteReducer(autoComplete[CREATE_ROLE_TEMPLATE_MODAL], action),
            [TABLE_VIEW_MODAL]: tableViewModalAutoCompleteReducer(autoComplete[TABLE_VIEW_MODAL], action),
            [TABLE_VIEW_MODAL_SIMPLIFIED]: tableViewModalSimplifiedAutoCompleteReducer(autoComplete[TABLE_VIEW_MODAL_SIMPLIFIED], action),
            [MARKETPLACE_PAGE_MODAL]: marketplacePageModalAutoCompleteReducer(autoComplete[MARKETPLACE_PAGE_MODAL], action),
            [GLOBAL_CREATE_MODAL]: globalCrateModalAutoCompleteReducer(autoComplete[GLOBAL_CREATE_MODAL], action),
            [ROLE_GROUP_MODAL]: createRoleGroupModalAutoCompleteReducer(autoComplete[ROLE_GROUP_MODAL], action)
        },
        fieldValueExplanations: {
            [PLANNER_PAGE_MODAL]: modalFieldValueExplanationsReducer( fieldValueExplanations[PLANNER_PAGE_MODAL], action),
            [PLANNER_PAGE_BATCH_MODAL]: batchModalFieldValueExplanationsReducer( fieldValueExplanations[PLANNER_PAGE_BATCH_MODAL], action),
            [PLANNER_PAGE_MODAL_SIMPLIFIED]: simplifiedModalFieldValueExplanationsReducer( fieldValueExplanations[PLANNER_PAGE_MODAL_SIMPLIFIED], action),
            [PLANNER_PAGE_DETAILS_PANE]: detailsPaneFieldValueExplanationsReducer( fieldValueExplanations[PLANNER_PAGE_DETAILS_PANE], action),
            [PLANNER_PAGE_BATCH_DETAILS_PANE]: batchDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[PLANNER_PAGE_BATCH_DETAILS_PANE], action),
            [PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL]: plannerPageAssigneesBudgetModalFieldValueExplanationReducer(fieldValueExplanations[PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL], action),
            [PLANNER_PAGE_MOVE_TO_PROMPT]: moveToPrompExplanationsReducer(fieldValueExplanations[PLANNER_PAGE_MOVE_TO_PROMPT], action),
            [JOBS_PAGE_MODAL]: jobsPageModalFieldValueExplanationsReducer( fieldValueExplanations[JOBS_PAGE_MODAL], action),
            [JOBS_PAGE_DETAILS_PANE]: jobsPageDetailsPaneFieldValueExplanationsReducer( fieldValueExplanations[JOBS_PAGE_DETAILS_PANE], action),
            [JOBS_PAGE_ROLE_GROUP_DETAILS_PANE]: jobsPageRoleGroupDetailsPaneFieldValueExplanationsReducer( fieldValueExplanations[JOBS_PAGE_ROLE_GROUP_DETAILS_PANE], action),
            [JOBS_PAGE_BATCHED_DETAILS_PANE]: jobsPageBatchDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[JOBS_PAGE_BATCHED_DETAILS_PANE], action),
            [JOBS_PAGE_BATCH_MODAL]: jobsPageBatchModalFieldValueExplanationsReducer( fieldValueExplanations[JOBS_PAGE_BATCH_MODAL], action),
            [RESOURCES_PAGE_MODAL]: resourcesPageModalFieldValueExplanationsReducer( fieldValueExplanations[RESOURCES_PAGE_MODAL], action),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneFieldValueExplanationsReducer( fieldValueExplanations[RESOURCES_PAGE_DETAILS_PANE], action),
            [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: resourcesPageBatchDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[RESOURCES_PAGE_BATCHED_DETAILS_PANE], action),
            [RESOURCES_PAGE_BATCH_MODAL]: resourcesPageBatchModalFieldValueExplanationsReducer( fieldValueExplanations[RESOURCES_PAGE_BATCH_MODAL], action),
            [ROLE_REQUEST_FORM]: resourceRoleByNameFieldValueExplanationsReducer( fieldValueExplanations[ROLE_REQUEST_FORM], action),
            [JOBS_PAGE_RESOURCE_DETAILS_PANE]: jobsPageResourceDetailsPaneFieldValueExplanationsReducer( fieldValueExplanations[JOBS_PAGE_RESOURCE_DETAILS_PANE], action),
            [ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE]: roleGroupPageRoleGroupDetailsPaneFieldValueExplanationsReducer( fieldValueExplanations[ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxPageModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_MODAL], action),
            [ROLE_INBOX_PAGE_BATCH_MODAL]: roleInboxPageBatchModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_BATCH_MODAL], action),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxPageDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: roleInboxPageBatchDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_MODAL_SIMPLIFIED]: simplifiedRoleInboxPageModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_MODAL_SIMPLIFIED], action),
            [ROLE_INBOX_ASSIGNEES_BUDGET_MODAL]: roleInboxPageAssigneesBudgetModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_ASSIGNEES_BUDGET_MODAL], action),
            [ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL]: roleInboxPagePublishRoleModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL], action),
            [ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL]: roleInboxPageEditRolePublicationModalFieldValueExplanationsReducer(fieldValueExplanations[ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL], action),
            [ROLE_DETAILS_PAGE_MOVE_TO_PROMPT]: roleGroupDetailsPageMoveToPrompExplanationsReducer(fieldValueExplanations[ROLE_DETAILS_PAGE_MOVE_TO_PROMPT], action),
            [NOTIFICATION_PAGE_MODAL]: notificationPageModalFieldValueExplanationsReducer(fieldValueExplanations[NOTIFICATION_PAGE_MODAL], action),
            [MARKETPLACE_DETAILS_PANE]: marketplacePageDetailsPaneFieldValueExplanationsReducer(fieldValueExplanations[MARKETPLACE_DETAILS_PANE], action),
            [PROFILE_PAGE_MODAL]: profilePageModalFieldValueExplanationsReducer( fieldValueExplanations[PROFILE_PAGE_MODAL], action),
            [PREVIEW_ENTITY_MODAL]: previewEntityModalFieldValueExplanationsReducer( fieldValueExplanations[PREVIEW_ENTITY_MODAL], action),
            [MANAGE_ROLE_TEMPLATES_MODAL]: manageMyTemplatesModalFieldValueExplanationsReducer(fieldValueExplanations[MANAGE_ROLE_TEMPLATES_MODAL], action),
            [CREATE_ROLE_TEMPLATE_MODAL]: createRoleTemplateModalFieldValueExplanationsReducer(fieldValueExplanations[CREATE_ROLE_TEMPLATE_MODAL], action),
            [TABLE_VIEW_MODAL]: tableViewModalFieldValueExplanationsReducer(fieldValueExplanations[TABLE_VIEW_MODAL], action),
            [TABLE_VIEW_MODAL_SIMPLIFIED]: tableViewSimplifiedModalFieldValueExplanationsReducer(fieldValueExplanations[TABLE_VIEW_MODAL_SIMPLIFIED], action),
            [MARKETPLACE_PAGE_MODAL]: marketplacePageModalFieldValueExplanationsReducer(fieldValueExplanations[MARKETPLACE_PAGE_MODAL], action),
            [GLOBAL_CREATE_MODAL]: globalCrateModalFieldValueExplanationsReducer(fieldValueExplanations[GLOBAL_CREATE_MODAL], action),
            [ROLE_GROUP_MODAL]: createRoleGroupModalExplanationsReducer(fieldValueExplanations[ROLE_GROUP_MODAL], action)
        },
        comments: {
            [PLANNER_PAGE_MODAL]: modalCommentsReducer(comments[PLANNER_PAGE_MODAL], action),
            [PLANNER_PAGE_DETAILS_PANE]: detailsPaneCommentsReducer(comments[PLANNER_PAGE_DETAILS_PANE], action),
            [PLANNER_PAGE_BATCH_MODAL]: batchModalCommentsReducer(comments[PLANNER_PAGE_BATCH_MODAL], action),
            [PLANNER_PAGE_BATCH_DETAILS_PANE]: batchDetailsPaneCommentsReducer(comments[PLANNER_PAGE_BATCH_DETAILS_PANE], action),
            [JOBS_PAGE_MODAL]: jobsPageModalCommentsReducer(comments[JOBS_PAGE_MODAL], action),
            [JOBS_PAGE_DETAILS_PANE]: jobsPageDetailsPaneCommentsReducer(comments[JOBS_PAGE_DETAILS_PANE], action),
            [JOBS_PAGE_BATCH_MODAL]: jobsPageBatchModalCommentsReducer(comments[JOBS_PAGE_BATCH_MODAL], action),
            [JOBS_PAGE_BATCHED_DETAILS_PANE]: jobsPageBatchDetailsPaneCommentsReducer(comments[JOBS_PAGE_BATCHED_DETAILS_PANE], action),
            [RESOURCES_PAGE_MODAL]: resourcesPageModalCommentsReducer(comments[RESOURCES_PAGE_MODAL], action),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneCommentsReducer(comments[RESOURCES_PAGE_DETAILS_PANE], action),
            [RESOURCES_PAGE_BATCH_MODAL]: resourcesPageBatchModalCommentsReducer(comments[RESOURCES_PAGE_BATCH_MODAL], action),
            [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: resourcesPageBatchDetailsPaneCommentsReducer(comments[RESOURCES_PAGE_BATCHED_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxPageModalCommentsReducer(comments[ROLE_INBOX_PAGE_MODAL], action),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxPageDetailsPaneCommentsReducer(comments[ROLE_INBOX_PAGE_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_BATCH_MODAL]: roleInboxPageBatchModalCommentsReducer(comments[ROLE_INBOX_PAGE_BATCH_MODAL], action),
            [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: roleInboxPageBatchDetailsPaneCommentsReducer(comments[ROLE_INBOX_PAGE_BATCH_DETAILS_PANE], action),
            [MARKETPLACE_PAGE_MODAL]: rolesBoardPageModalCommentsReducer(comments[MARKETPLACE_PAGE_MODAL], action),
            [MARKETPLACE_DETAILS_PANE]: rolesBoardPageDetailsPaneCommentsReducer(comments[MARKETPLACE_DETAILS_PANE], action),
            [NOTIFICATION_PAGE_MODAL]: notificationPageModalCommentsReducer(comments[NOTIFICATION_PAGE_MODAL], action)
        },
        workHistory: {
            [PLANNER_PAGE_DETAILS_PANE]: plannerPageDetailsPaneWorkHistoryReducer(workHistory[PLANNER_PAGE_DETAILS_PANE], action),
            [JOBS_PAGE_RESOURCE_DETAILS_PANE]: jobsPageDetailsPaneWorkHistoryReducer(workHistory[JOBS_PAGE_RESOURCE_DETAILS_PANE], action),
            [RESOURCES_PAGE_DETAILS_PANE]: resourcesPageDetailsPaneWorkHistoryReducer(workHistory[RESOURCES_PAGE_DETAILS_PANE], action),
            [ROLE_INBOX_PAGE_DETAILS_PANE]: roleInboxDetailsPaneWorkHistoryReducer(workHistory[ROLE_INBOX_PAGE_DETAILS_PANE], action),
            [PLANNER_PAGE_MODAL]: plannerPageEntityWindowWorkHistoryReducer(workHistory[PLANNER_PAGE_MODAL], action),
            [JOBS_PAGE_MODAL]: jobsPageEntityWindowWorkHistoryReducer(workHistory[JOBS_PAGE_MODAL], action),
            [RESOURCES_PAGE_MODAL]: resourcesPageEntityWindowWorkHistoryReducer(workHistory[RESOURCES_PAGE_MODAL], action),
            [ROLE_INBOX_PAGE_MODAL]: roleInboxEntityWindowWorkHistoryReducer(workHistory[ROLE_INBOX_PAGE_MODAL], action)
        },
        fieldValueMessages: fieldValueMessagesReducer(fieldValueMessages, action)
    };

    return keepUnmutatedState(state, reducedState);
}