import { orderBy, isString, isEqual } from 'lodash';
import { ENTITY_WINDOW_MODULES, FIELD_DATA_TYPES, JOBS_PAGE_ALIAS, PLANNER_PAGE_ALIAS, UNASSIGNED_BOOKINGS_RESOURCE } from '../constants';
import { ROLEREQUEST_FIELDS, CRITERIA_ROLE_FTE_INPUT_FIELD, ROLEREQUEST_ESTIMATE_FIELDS } from '../constants/fieldConsts';
import { statusBadgeColors, TABLE_NAMES } from '../constants/globalConsts';
import { ROLE_GROUP_DETAILS_PAGE } from '../constants/jobsPageConsts';
import { MARKETPLACE_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { processMilestonesHistoryField, processHistoryField } from '../lib/historyField/utils';
import { getFieldInfoSelector } from '../selectors/tableStructureSelectors';
import { getIsMilestonesHistoryField, getIsHistoryField, isSubmittableField } from './fieldUtils';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import { NULL_GUID } from '../constants/plannerConsts';
import { isFloatNumber } from './fieldControlUtils';
import { getIsCriteriaRole } from './roleRequestsUtils';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

export function indexByIdField(idFieldName, arrData = []) {
    return arrData.reduce((map, element, index) => {
        let fieldVal = element[idFieldName];
        map[fieldVal] = index;

        return map;
    }, {});
}

export function mapByFieldValue(field, arrData, transformKey = (value) => value, filterKey = () => true) {
    return arrData.filter(filterKey).reduce((map, element) => {
        //temporary change the boolean fields type until more general approach is taken
        switch (element.name) {
            case 'booking_nonwork':
            case 'rolerequest_nonwork':
                element.dataType = 'BookingNonWorkType';
                break;
            case ROLEREQUEST_FIELDS.FTE:
                element.max = CRITERIA_ROLE_FTE_INPUT_FIELD.MAX_VALUE;
                element.min = CRITERIA_ROLE_FTE_INPUT_FIELD.MIN_VALUE;
                element.step = CRITERIA_ROLE_FTE_INPUT_FIELD.STEP;
                break;
        }

        map[transformKey(element[field])] = element;

        return map;
    }, {});
}

export function omit(object, fields) {
    const shallowCopy = {
        ...object
    };

    fields.forEach((field) => {
        delete shallowCopy[field];
    });

    return shallowCopy;
}

export function isMultiDimensionalArray(arr = []) {
    return true === (Array.isArray(arr) && 0 < arr.length && Array.isArray(arr[0]));
}

export function getPropOrDefault(prop, defaultValue) {
    return typeof(prop) !== 'undefined'
        ? prop
        : defaultValue;
}

export function swapArrayElementsByIndeces(arr, startIndex, endIndex) {
    const swapArr = [...arr];

    if (Number.isInteger(startIndex) && Number.isInteger(endIndex) && startIndex !== endIndex) {
        [swapArr[startIndex], swapArr[endIndex]] = [swapArr[endIndex], swapArr[startIndex]];
    }

    return swapArr;
}

export function generateKey() {
    let key = '';
    if (arguments.length) {
        key = arguments[0];
        for (let i = 1; i < arguments.length; i++) {
            key = `${key}_${arguments[i]}`;
        }
    }

    return key;
}

export function getFieldAlias(fieldInfo, fallbackValue) {
    return fieldInfo.alias ? fieldInfo.alias : fallbackValue;
}

export function arraysAreSame(array1, array2) {
    let same = true;

    if (array1.length !== array2.length) {
        return false;
    }

    for (let i = 0; i < array1.length; i++) {
        if (array1[i] !== array2[i]) {
            same = false;
            break;
        }
    }

    return same;
}

export function capitalizeFirstLetter(string) {
    let result = string;

    if (typeof(string) === 'string') {
        result = string.charAt(0).toUpperCase() + string.slice(1);
    }

    return result;
}

export function lowerFirstLetter(string) {
    let result = string;

    if (typeof(string) === 'string') {
        result = string.charAt(0).toLowerCase() + string.slice(1);
    }

    return result;
}

export function lowerCaseObjectKeys(obj = {}) {
    const newObj = {};

    Object
        .keys(obj)
        .reduce((acc, key) => {
            acc[key.toLocaleLowerCase()] = obj[key];

            return acc;
        }, newObj);

    return newObj;
}

export function getObjectKeysByFieldInfo(obj = {}, getFieldInfo, tableName) {
    return Object.keys(obj).reduce((acc, key) => {
        const fieldInfo = getFieldInfo(tableName, key);

        if (fieldInfo && fieldInfo.name) {
            acc[fieldInfo.name] = obj[key];
        } else {
            acc[key] = obj[key];
        }

        return acc;
    }, {});
}

export function getAppliedTemplateValue(stringValue, templateName, templateCollection, label, additionalArgs) {
    const defaultTemplateName = 'default';
    const template = templateCollection[templateName] || templateCollection[defaultTemplateName];

    return template(stringValue, label, additionalArgs);
}

export function getNoResultsMessage(tableAlias, staticLabels = {}, fieldTableName) {
    let prefix = staticLabels.noResultsMessagePrefix || 'No';
    let suffix = staticLabels.noResultsMessageSuffix || 'was found with this name.';
    let result = `${prefix} ${tableAlias} ${suffix}`;

    if (fieldTableName === TABLE_NAMES.ROLECATEGORY) {
        prefix = staticLabels.noResultsMessagePrefix || 'No';
        suffix = staticLabels.noOptionsSetSuffix || 'category options set by your Administator';
        result = `${prefix} ${suffix}`;
    }

    return result;
}

export function sortByField(collection, sortBy, orders = ['asc']) {
    return orderBy(collection, [sortBy], orders);
}

export function isEmptyObject(obj = {}) {
    return Object.keys(obj).length === 0;
}

export function stringReplacePlaceholders(stringWithPlaceholders, placeholderValuesMap) {
    let formattedString = stringWithPlaceholders;
    const placeholderRegEx = new RegExp('\\${(.*?)}', 'g');
    if (isString(formattedString)) {
        const stringPlaceholders = [
            ...(formattedString.match(placeholderRegEx) || []).map(match => {
                const withoutWrapper = match.slice(2, match.length - 1);

                return withoutWrapper.trim();
            })
        ];

        const placeholderValuesKeys = Object.keys(placeholderValuesMap);
        stringPlaceholders.filter(placeholderInString => placeholderValuesKeys.includes(placeholderInString)).forEach(placeHolderKey => {
            const placeholderRegEx = new RegExp(`\\\${\\s*${placeHolderKey}\\s*}`, 'g');
            formattedString = formattedString.replace(placeholderRegEx, placeholderValuesMap[placeHolderKey]);
        });
    }

    return formattedString;
}

export function getLicenseThresholdValue(subscribedCount) {
    let thresholdValue = 0;

    if (subscribedCount > 10) {
        thresholdValue = subscribedCount - Math.floor(subscribedCount * 0.9);
    } else if (subscribedCount >= 1 && subscribedCount <= 10) {
        thresholdValue = 1;
    }

    return thresholdValue;
}

export const keepUnmutatedState = (oldsState = {}, newState = {}) => {
    const allKeys = Array.from(new Set([...Object.keys(oldsState), ...Object.keys(newState)]));
    const mutatedState = allKeys
        .filter(segmentKey => !isEqual(oldsState[segmentKey], newState[segmentKey]))
        .reduce((accumulator, segmentKey) => {
            accumulator[segmentKey] = newState[segmentKey];

            return accumulator;
        }, {});

    let reducedState = oldsState;

    if (Object.keys(mutatedState).length) {
        reducedState = { ...oldsState, ...mutatedState };
    }

    return reducedState;
};

export const isStringNullOrEmpty = string => string === null || string === '';

export const isNullOrUndefined = value => value === null || value === undefined;

export const isBoolean = value => typeof value === 'boolean';

export const isInteger = value => !isNaN(Number.parseInt(value)) && Number.isInteger(Number.parseInt(value));

export const isFloat = value => !isNaN(Number.parseFloat(value)) && isFloatNumber(Number.parseFloat(value));

export const isPositiveInteger = value => isInteger(value) && value > 0;

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_MODAL_SIMPLIFIED,
    JOBS_PAGE_MODAL,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    PLANNER_PAGE_DETAILS_PANE,
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_GROUP_MODAL,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
    NOTIFICATION_PAGE_MODAL,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    PROFILE_PAGE_MODAL,
    MANAGE_ROLE_TEMPLATES_MODAL,
    MARKETPLACE_DETAILS_PANE,
    CREATE_ROLE_TEMPLATE_MODAL,
    TABLE_VIEW_MODAL,
    TABLE_VIEW_MODAL_SIMPLIFIED,
    MARKETPLACE_PAGE_MODAL,
    GLOBAL_CREATE_MODAL
} = ENTITY_WINDOW_MODULES;

export const isWindowModal = windowModuleName => [
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    JOBS_PAGE_MODAL,
    RESOURCES_PAGE_MODAL,
    PLANNER_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
    NOTIFICATION_PAGE_MODAL,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    PROFILE_PAGE_MODAL,
    MANAGE_ROLE_TEMPLATES_MODAL,
    CREATE_ROLE_TEMPLATE_MODAL,
    TABLE_VIEW_MODAL,
    TABLE_VIEW_MODAL_SIMPLIFIED,
    MARKETPLACE_PAGE_MODAL,
    GLOBAL_CREATE_MODAL,
    ROLE_GROUP_MODAL
].some(modalModuleName => modalModuleName === windowModuleName);

export const isDetailsPane = windowModuleName => [
    PLANNER_PAGE_DETAILS_PANE,
    JOBS_PAGE_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    MARKETPLACE_DETAILS_PANE
].some(modalModuleName => modalModuleName === windowModuleName);

export const isBatchWindowModal = windowModuleName => [
    PLANNER_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL
].some(modalModuleName => modalModuleName === windowModuleName);

export const isSimplifiedModal = windowModuleName => [
    PLANNER_PAGE_MODAL_SIMPLIFIED,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    TABLE_VIEW_MODAL_SIMPLIFIED
].some(modalModuleName => modalModuleName === windowModuleName);

export const isAssigneeBudgetModal = (moduleName) => [
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL
].some(modalModuleName => modalModuleName === moduleName);

export function getArrayPropertyValuesComparator(property) {
    return function (a,b) {
        let comparator = -1;

        if (a[property] && b[property]) {
            comparator = a[property].localeCompare(b[property]);
        }

        return comparator;
    };
}

export function getJobBadgeColor(text) {
    return statusBadgeColors[text] || statusBadgeColors['Unconfirmed'];
}

export function isJsonString(str) {
    try {
        JSON.parse(str);
    } catch (e) {
        return false;
    }

    return true;
}

const plannerModules = [
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_MOVE_TO_PROMPT,
    ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL
];

const jobModules = [
    ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCHED_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_BATCH_MODAL,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE
];

const resourceModules = [
    ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.RESOURCES_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.RESOURCES_PAGE_BATCH_MODAL
];

const rolerequestModules = [
    ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM,
    ENTITY_WINDOW_MODULES.ROLE_DETAILS_PAGE_MOVE_TO_PROMPT
];

const roleInboxModules = [
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL
];

const notificationModules = [
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL
];

const marketplaceModules = [
    ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE,
    ENTITY_WINDOW_MODULES.MARKETPLACE_PAGE_MODAL
];
const profileModules = [
    ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL
];

const tableViewModules = [
    ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL,
    ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED
];

export function getPageAliasForEntityModule(moduleName) {
    let result = '';

    if (plannerModules.includes(moduleName)) {
        result = PLANNER_PAGE_ALIAS;
    } else if (jobModules.includes(moduleName)) {
        result = JOBS_PAGE_ALIAS;
    } else if (resourceModules.includes(moduleName)) {
        result = RESOURCES_PAGE_ALIAS;
    } else if (rolerequestModules.includes(moduleName)) {
        result = ROLE_GROUP_DETAILS_PAGE;
    } else if (roleInboxModules.includes(moduleName)) {
        result = ROLE_INBOX_PAGE_ALIAS;
    } else if (notificationModules.includes(moduleName)) {
        result = NOTIFICATIONS_PAGE_ALIAS;
    } else if (marketplaceModules.includes(moduleName)) {
        result = MARKETPLACE_PAGE_ALIAS;
    } else if (profileModules.includes(moduleName)) {
        result = PROFILE_PAGE_ALIAS;
    } else if (tableViewModules.includes(moduleName)) {
        result = TABLE_VIEW_PAGE_ALIAS;
    }

    return result;
}

export function getEntityWindowModulesForPage(pageName) {
    let result = [];

    switch (pageName) {
        case PLANNER_PAGE_ALIAS: {
            result = plannerModules;
            break;
        }
        case JOBS_PAGE_ALIAS: {
            result = jobModules;
            break;
        }
        case RESOURCES_PAGE_ALIAS: {
            result = resourceModules;
            break;
        }
        case ROLE_GROUP_DETAILS_PAGE: {
            result = rolerequestModules;
            break;
        }
        case ROLE_INBOX_PAGE_ALIAS: {
            result = roleInboxModules;
            break;
        }
        case NOTIFICATIONS_PAGE_ALIAS: {
            result = notificationModules;
            break;
        }
        case MARKETPLACE_PAGE_ALIAS: {
            result = marketplaceModules;
            break;
        }
        case TABLE_VIEW_PAGE_ALIAS: {
            result = tableViewModules;
            break;
        }
    }

    return result;
}

export const intersectMapOfArrays = (mapA, mapB) => {
    let newMap = {};
    Object.keys(mapA).forEach(operationKey => {
        let intersection = mapB[operationKey].filter(id => mapA[operationKey].includes(id));
        newMap[operationKey] = intersection;
    });

    return newMap;
};

export const findIndexOf = (arr, prop, searchedElement) => {
    return arr.findIndex(el => el[`${prop}`] === searchedElement);
};

export const getLicenseKeySubscribedCount = (licenseKeyObject) => {
    return (licenseKeyObject || {}).subscribedCount || false;
};

export const getResourceIsInactive = (resourceUserStatus) => resourceUserStatus !== true;

export const getUniqueArrayItems = (arrayWithDuplicates) => {
    return Array.from(new Set(arrayWithDuplicates));
};

// This duplicates getOmittedFields in entityWindowEpics. Both functions should be combined
// this function does more than one thing - gets the omitted fields and also modifies the passed entity. Investigation is needed if this
// function is used for data formatting purposes.
export const getOmittedFields = (state, entity, tableName) => {
    const getFieldInfo = getFieldInfoSelector(state);
    let omittedFields = [];

    for (let fieldName in entity) {
        const fieldInfo = getFieldInfo(tableName, fieldName);

        if (getIsHistoryField(fieldInfo)) {
            entity[fieldName] = processHistoryField(entity[fieldName], fieldInfo);
        }

        if (getIsMilestonesHistoryField(fieldInfo)) {
            entity[fieldName] = processMilestonesHistoryField(entity[fieldName], fieldInfo);
        }

        if (fieldInfo && entity[fieldName] === undefined) {
            entity[fieldName] = null;
        }

        if (fieldInfo && fieldInfo.dataType === FIELD_DATA_TYPES.STRING && entity[fieldName]) {
            entity[fieldName] = entity[fieldName].trim();
        }

        if (!isSubmittableField(fieldName, tableName, fieldInfo)) {
            omittedFields.push(fieldName);
        }

        if (ROLEREQUEST_ESTIMATE_FIELDS.includes(fieldName) && !getIsCriteriaRole(entity)) {
            omittedFields.push(fieldName);
        }
    }

    return omittedFields;
};

export const getData = (dataCollections = [], tableName, id) => {
    if (tableName === TABLE_NAMES.RESOURCE && (id === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID || id === NULL_GUID)) {
        return {
            [`${tableName}_guid`]: null,
            [`${tableName}_description`]: UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_DESCRIPTION
        };
    }

    if (tableName === TABLE_NAMES.OPERATIONSLOG) {
        return {
            [`${tableName}_guid`]: id
        };
    }

    tableName = tableName.toLowerCase();
    let entryData = undefined;

    const filteredDataCollections = dataCollections
        .filter(collection => {
            return (collection || {}).tableName === tableName || (Array.isArray((collection || {}).tableNames) && 0 <= (collection || {}).tableNames.indexOf(tableName));
        });

    for (let i = 0; i < filteredDataCollections.length; i++) {
        const collection = filteredDataCollections[i] || {};
        let map = null;
        const listData = collection.data;

        if (collection.tableName === tableName) {
            map = collection.byId;
        } else if (Array.isArray(collection.tableNames) && 0 <= collection.tableNames.indexOf(tableName)) {
            map = collection.byId[tableName];
        }

        if (map !== null && Array.isArray(listData) && 0 < listData.length && id in map) {
            entryData = {
                ...entryData,
                ...listData[map[id]]
            };
            break;
        }
    }

    return entryData || { dummy: true };
};

// This function does not support insert at index zero
// This is fixed with the introduction of an additional param - allowZeroIndexInsertion
export const insertIntoSortedCollection = (collection = [], element, compareFn, allowZeroIndexInsertion = false) => {
    let sortSliceIndex = collection.length - 1;

    for (let i = collection.length - 1; i >= 0 ; --i) {
        const currentElement = collection[i];

        if (compareFn(element, currentElement, i)) {
            sortSliceIndex = i;
            break;
        }
    }

    return [].concat(
        (allowZeroIndexInsertion && sortSliceIndex === 0) ? [] : collection.slice(0, sortSliceIndex + 1),
        element,
        (allowZeroIndexInsertion && sortSliceIndex === 0) ? collection : collection.slice(sortSliceIndex + 1)
    );
};

export function extractProps(object, fields) {
    let currentProps = {};

    fields.forEach((field) => {
        currentProps = {
            ...currentProps,
            [field]: object[field]
        };
    });

    return currentProps;
}

export const getValueUpToDecimals = (stringValue, decimalPlaces) => {
    const decimalIndex = stringValue.includes('.') ? stringValue.indexOf('.') : stringValue.indexOf(',');

    return decimalIndex >= 0
        ? stringValue.substr(0, decimalIndex) + stringValue.substr(decimalIndex, decimalPlaces + 1)
        : stringValue;
};

export const checkArraySpecificPropIdentical = (array = [], prop = '') => {
    return array.every(item => (
        array[0][prop] === item[prop]));
};

export const calculatePercentage = (value, totalValue) => (value / totalValue) * 100;

export const isTableEntity = (entity, tableName) => {
    return !isEmptyObject(entity) && entity[`${tableName}_guid`] != null ;
};

export const convertStringToGuid = (str) => {
    return str.replace(/(.{8})(.{4})(.{4})(.{4})(.{12})/, '$1-$2-$3-$4-$5');
};

export const constructObjectWithSameValues = (keys, value = {}) => {
    return keys.reduce((acc, key) => {
        acc[key] = value;

        return acc;
    },{});
};

export const getArrayHasSameValues = (array) => array.every(entity => entity === array[0]);

export const getTruncatedText = (text = '', maxLengthChars) => text.length > maxLengthChars ? `${text.slice(0, maxLengthChars)}...` : text;

export const calcPercentage = (progress, count) => Math.round((progress * 100) / count);

export const roundToDecimalPlaces = (num, decimalPlaces) =>
    Number.isInteger(num) ? num : Number(num).toFixed(decimalPlaces ? decimalPlaces : 2);

export const boolToYesOrNoValue = (value,lowerCase = false) => {
    const returnValue = value ? 'Yes' : 'No';

    return lowerCase ? returnValue.toLocaleLowerCase() : returnValue;
};

/* Trims the value and converts it to lowercase */
Object.defineProperty(String.prototype, 'toTrimAndLowerCase', {
    value: function toTrimAndLowerCase() {
        return this.trim().toLowerCase();
    },
    writable: true,
    configurable: true
});

// Check if Object is empty or all the array values inside it is empty
export const isJsonArrayValueEmpty = (value) => {
    const parsedValue = value && typeof value === 'string' ? JSON.parse(value) : {};

    return isEmptyObject(parsedValue) || Object.values(parsedValue).every(v => Array.isArray(v) && v.length === 0);
};