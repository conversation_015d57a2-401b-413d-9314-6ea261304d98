import * as actionTypes from '../actions/actionTypes';
import { entityWindowOpen } from '../actions/entityWindowActions';
import {
    dateRangeChanged,
    dateToggleOptionChanged,
    filterSettingsChanged,
    hideHistoricRecordsChanged,
    hideWeekendsChanged,
    hideFutureRecordsChanged,
    hideRolesChanged,
    hideRequestedRolesChanged,
    hideDraftRolesChanged,
    hideRolesByNameChanged,
    hideRolesByRequirementsChanged,
    hidePotentialConflictsChanged,
    hideJobTimelineChanged,
    hideJobMilestonesChanged,
    hideLiveBarsChanged,
    hideInactiveResourcesChanged,
    tableViewHideHistoricRecordsChanged,
    tableViewHideFutureRecordsChanged,
    tableViewDateRangeChanged,
    tableViewDateToggleOptionChanged,
    tableViewHideInactiveResourcesChanged,
    hideUnassignedRowsChanged,
    hideUnassignedRolesChanged
} from '../actions/workspaceSettingsActions';
import { toggleFilterPane, setBaseFilter, clearAllFilters, resetBaseFilter, unloadMultivaluesFilterLoadedOptions } from '../actions/filterActions';
import { digestSelectWorkspace, saveWorkspaceSettings, saveWorkspaceIfAnyChanges } from '../actions/workspaceActions';
import { setManageMyPlansWindowVisibility } from '../actions/managePlansSectionActions';
import { ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, ENTITY_LOOKUP_WINDOW_SECTION, PLANNER_PAGE_ALIAS, PLANNER_BAR_ACTIONS, PLANNER_ACTIONS, PLANNERPAGE_FILTER_ALIAS, PLANNER_ACTORS, PLANNER_DATE_TOGLE_OPTIONS } from '../constants/plannerConsts';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { setManageEntityLookupWindowVisibility } from '../actions/entityLookupWindowActions';
import { JOBS_PAGE_ACTIONS, JOBSPAGE_FILTER_ALIAS, JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { LIST_PAGE_ACTIONS } from '../constants/listPageConsts';
import { ENTITY_WINDOW_MODULES, DEFAULT_BASE_FILTER_OPTION, TABLE_NAMES } from '../constants';
import {
    getSelectedWorkspaceGuid,
    getSelectedWorkspaceSettings,
    workspaceSettingsChanged,
    workspaceBelongsToResource,
    getWorkspaceIsEditable,
    getIsPublicWorkspace,
    getSelectedWorkspace,
    getWorkspaceColorSchemeInfo,
    getCurrentWSSettingsSelector
} from '../selectors/workspaceSelectors';
import { getCurrentPlannerSelectionSelector, getPlannerPageFilterPaneCollapsed } from '../selectors/plannerPageSelectors';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { hasFunctionalAccessGlobal } from '../selectors/applicationFnasSelectors';
import { getSaveWorkspaceSettings, shouldSaveIfAnyChanges } from './workspaceUtils';
import {
    createJob as plannerCreateJob,
    createBooking,
    createClient,
    editSelectedEntitiesDetails,
    changeDateToggle,
    changeSelectedDateToggleStep,
    goToToday,
    deleteSelectedBars,
    pasteBar,
    copyBar,
    cutBar,
    setViewSettings,
    createWorkspace,
    canPasteBar,
    isSingleEntitySelected,
    hasSelectedEntity,
    createRole,
    buildOpenRoleTransitionDialogPayload,
    rollForwardSelectedEntities,
    saveAsNewPlan,
    peopleFinder,
    managePendingTimeAllocation,
    hasMultipleSelectedEntities
} from './plannerActionsUtils';
import {
    createJob as jobsCreateJob, editJob, duplicateJob
} from './jobsActionsUtils';
import { getJobsPageFilterPaneCollapsed, getMarketplacePageFilterPaneCollapsed, getResourcesPageFilterPaneCollapsed, getRoleInboxPageBaseFilterAppliedValuesSelector, getRoleInboxPagePageFilterPaneCollapsed } from '../selectors/dataGridPageSelectors';
import { getCustomOption, getViewModeFromDates, getDateToggleDisabled } from './dateToggleOptionsUtils';
import { getEntitySingularAlias, getEntityPluralAlias } from './entityStructureUtils';
import { commandBarStringTemplates } from '../constants/stringTemplateConsts';
import { promptAction } from '../actions/promptActions';
import { getAccessibleEntitiesIdsSelector } from '../selectors/userEntityAccessSelectors';
import { ROLES_MODAL, ROLES_TRANSITION_ACTION, ROLE_ITEM_STATUS_KEYS } from '../constants/rolesConsts';
import { openRoleTransitionDialogRequest } from '../actions/roleTransitionDialogsActions';
import { ROLE_INBOX_PAGE_ACTIONS, ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_FILTER_ALIAS } from '../constants/roleInboxPageConsts';
import { createRole as inboxPageCreateRole, deleteRole as inboxPageDeleteRole, dispatchEditRolesActions, getRoleInboxPageSetBaseFilterAction } from './roleInboxActionsUtils';
import { getTableDataRoleRequestStatusGuidSelector } from '../selectors/roleRequestsSelector';
import { RESOURCE_GUID, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { REPORT_ACTIONS, notifyReportEvent, selectEmbedReport, toggleViewMode, updateReportSuccess } from '../reducers/reportReducer/reportDataActions';
import { MANAGE_REPORTS_MODAL_ACTIONS, setupManageReportsModal } from '../reducers/reportReducer/manageReportsModalActions';
import { roleInboxAssignResourceToCriteriaRolerequest } from '../actions/roleInboxPageActions';
import {
    movePendingTimeAllocationAction,
    updateRoleRequestsStatus,
    removePendingTimeAllocationAction,
    openManageRoleTemplatesModal,
    openPublishRoleEW,
    openEditRolePublicationEW,
    removeRolePublication
} from '../actions/rolerequestActions';
import { getData } from './commonUtils';
import { getPageTableDatasSelector } from '../selectors/tableDataSelectors';
import { buildUpdateRoleRequestPayload } from './roleRequestsUtils';
import { getIsRejectRolerequestActionDisabled } from './rolerequestActionsUtils';
import { MARKETPLACE_PAGE_ACTIONS, MARKETPLACE_PAGE_COMMAND_BAR_ALIAS, MARKETPLACE_PAGE_FILTER_ALIAS } from '../constants/marketplacePageConsts';
import { commandBarUpdateActionElementLabel } from '../actions/commandBarActions';
import { TABLE_VIEW_PAGE_ACTIONS, TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS, TABLE_VIEW_ACTORS } from '../constants/tableViewPageConsts';
import { getTableViewPageFilterPaneCollapsed } from '../selectors/tableViewSelectors';
import { setTableViewViewSettings, tableViewCreateBooking, tableViewCreateClient, tableViewCreateJob } from './tableView/tableViewActionUtils';
import { RESTORE_PLANNER_VIEW, restorePlannerViewAction, rotatePlannerView } from '../actions/rotatePlannerViewActions';
import { getRotateViewParentIds } from './plannerPage/rotatePlannerViewUtils';
import { getReportsDataSelector } from '../reducers/reportReducer/reportSelectors';
import { NOTIFY_REPORT_EVENT_TYPES, REPORT_MAPPED_FIELDS, REPORT_TYPE } from '../constants/reportConsts';
import { openCreateRoleGroupModal } from '../actions/roleGroupListActions';
import { ROLE_GROUP_DETAILS_COMMAND_BAR_ACTIONS } from '../constants/roleGroupDetailsPageConsts';
import { getCurrentDate } from './dateUtils';
import { jobHasLongRunningTaskSelector } from '../reducers/longRunningTasksReducer/selectors';
import { updateListView } from '../actions/listPageActions';
import { RESOURCES_PAGE_ACTIONS, RESOURCES_PAGE_FILTER_ALIAS } from '../constants/resourcesPageConsts';
import { EDIT_FNAS_PER_TABLENAME } from '../constants/tablesConsts';
import { editResource } from './listsActionsUtils';

export const commandBarActionCreators = {
    [PLANNER_ACTIONS.CREATE_JOB]: (state, dispatch) => {
        plannerCreateJob(state, dispatch);
    },
    [PLANNER_ACTIONS.CREATE_CLIENT]: (state, dispatch) => {
        createClient(state, dispatch);
    },
    [PLANNER_ACTIONS.MANAGE_JOBS]: (state, dispatch) => {
        dispatch(setManageEntityLookupWindowVisibility(ENTITY_LOOKUP_WINDOW_SECTION.JOB, true, PLANNER_PAGE_ALIAS));
    },
    [PLANNER_ACTIONS.MANAGE_CLIENTS]: (state, dispatch) => {
        dispatch(setManageEntityLookupWindowVisibility(ENTITY_LOOKUP_WINDOW_SECTION.CLIENT, true, PLANNER_PAGE_ALIAS));
    },
    [PLANNER_ACTIONS.CREATE_BOOKING]: (state, dispatch) => {
        createBooking(state, dispatch, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.VIEW_SETTINGS]: (state, dispatch, context) => {
        // dispatch(clearFilterPaneSelection(context.filtersGuid));
        setViewSettings(state, dispatch, context.newViewSettings);
    },
    [PLANNER_ACTIONS.EDIT_BOOKING_BARS]: (state, dispatch, context) => {
        editSelectedEntitiesDetails(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, TABLE_NAMES.BOOKING, context.actionKey);
    },
    [PLANNER_ACTIONS.EDIT_ROLES_BY_NAME_BARS]: (state, dispatch, context) => {
        editSelectedEntitiesDetails(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, TABLE_NAMES.ROLEREQUEST, context.actionKey);
    },
    [PLANNER_ACTIONS.EDIT_ROLES_BY_REQUIREMENTS_BARS]: (state, dispatch, context) => {
        editSelectedEntitiesDetails(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, TABLE_NAMES.ROLEREQUEST, context.actionKey);
    },
    [PLANNER_ACTIONS.CUT_BAR]: (state, dispatch) => {
        cutBar(state, dispatch, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.COPY_BAR]: (state, dispatch) => {
        copyBar(state, dispatch, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.ROLL_FORWARD]: (state, dispatch, context) => {
        rollForwardSelectedEntities(state, dispatch, context.entitiesTableName);
    },
    [PLANNER_ACTIONS.PASTE_BAR]: (state, dispatch) => {
        pasteBar(state, dispatch);
    },
    [PLANNER_ACTIONS.DELETE_BAR]: (state, dispatch, context) => {
        deleteSelectedBars(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, context.entitiesTableName);
    },
    [PLANNER_ACTIONS.GO_TO_TODAY]: (state, dispatch) => {
        goToToday(state, dispatch, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.GO_TO_DATE]: (state, dispatch, context) => {
        dispatch(dateRangeChanged(context.workspaceGuid, context.newStartDate, context.newEndDate, PLANNER_ACTORS.COMMAND_BAR));
    },
    [PLANNER_ACTIONS.GO_TO_DATE_RANGE]: (state, dispatch, context) => {
        const { workspaceGuid, newStartDate, newEndDate } = context;
        dispatch(dateToggleOptionChanged(workspaceGuid, getCustomOption().value, getViewModeFromDates(newStartDate, newEndDate).name));
        dispatch(dateRangeChanged(workspaceGuid, newStartDate, newEndDate, PLANNER_ACTORS.COMMAND_BAR));
    },
    [PLANNER_ACTIONS.CHANGE_DATE_TOGGLE]: (state, dispatch, context) => {
        const { options } = context;

        changeDateToggle(state, dispatch, options.canIncreaseRange, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.CHANGE_SELECTED_DATE_TOGGLE_STEP]: (state, dispatch, context) => {
        changeSelectedDateToggleStep(state, dispatch, context.value, PLANNER_ACTORS.COMMAND_BAR);
    },
    [PLANNER_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        const { workspaceGuid, filtersGuid } = context;
        dispatch(toggleFilterPane(PLANNERPAGE_FILTER_ALIAS, filtersGuid, !getPlannerPageFilterPaneCollapsed(state.plannerPage, filtersGuid)));
        dispatch(filterSettingsChanged(workspaceGuid, PLANNER_PAGE_ALIAS));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_HISTORIC_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideHistoricRecordsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_FUTURE_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideFutureRecordsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROWS_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideUnassignedRowsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_BOOKINGS_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideUnassignedRowsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_JOB_TIMELINE]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideJobTimelineChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_JOB_MILESTONEE]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideJobMilestonesChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideRolesChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_UNASSIGNED_ROLES]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideUnassignedRolesChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_BY_NAME]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideRolesByNameChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_ROLES_BY_REQUIREMENTS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideRolesByRequirementsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_DRAFT_ROLES_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideDraftRolesChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_REQUESTED_ROLES_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideRequestedRolesChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_LIVE_BARS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(hideLiveBarsChanged(workspaceGuid, checked));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_WEEKENDS]: (state, dispatch, context) => {
        const { workspaceGuid, hideWeekends } = context;

        dispatch(hideWeekendsChanged(workspaceGuid, !hideWeekends));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_POTENTIAL_CONFLICTS]: (state, dispatch, context) => {
        const { workspaceGuid, hidePotentialConflicts } = context;

        dispatch(hidePotentialConflictsChanged(workspaceGuid, !hidePotentialConflicts));
    },
    [PLANNER_ACTIONS.TOGGLE_HIDE_INACTIVE_RESOURCES]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        const { filtersGuid } = getCurrentWSSettingsSelector(state);

        dispatch(hideInactiveResourcesChanged(workspaceGuid, checked));
        dispatch(unloadMultivaluesFilterLoadedOptions(PLANNERPAGE_FILTER_ALIAS, filtersGuid, RESOURCE_GUID, TABLE_NAMES.RESOURCE));
    },
    [PLANNER_ACTIONS.SHOW_IN_VIEW]: (state, dispatch) => {
        const selectedWorkspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);
        const parentIds = getRotateViewParentIds(state);

        dispatch(rotatePlannerView(selectedWorkspaceSettings.subRecTableName, parentIds));
    },
    [JOBS_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        dispatch(toggleFilterPane(JOBSPAGE_FILTER_ALIAS, context.jobsFilterPaneContextGuid, !getJobsPageFilterPaneCollapsed(state)));
    },
    [JOBS_PAGE_ACTIONS.CREATE_CLIENT]: (state, dispatch, context) => {
        const dispatchAction = entityWindowOpen(
            ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
            'client',
            ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS,
            ENTITY_WINDOW_OPERATIONS.CREATE,
            context,
            undefined,
            false
        );
        dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
    },
    [JOBS_PAGE_ACTIONS.CREATE_JOB]: (state, dispatch, context) => {
        jobsCreateJob(state, dispatch);
    },
    [JOBS_PAGE_ACTIONS.CREATE_ROLE_GROUP]: (state, dispatch, context) => {
        dispatch(openCreateRoleGroupModal());
    },
    [JOBS_PAGE_ACTIONS.EDIT_JOB]: (state, dispatch, context) => {
        editJob(state, dispatch);
    },
    [JOBS_PAGE_ACTIONS.DUPLICATE_JOB]: (state, dispatch, context) => {
        duplicateJob(state, dispatch);
    },
    [JOBS_PAGE_ACTIONS.MANAGE_JOBS]: (state, dispatch, context) => {
        dispatch(setManageEntityLookupWindowVisibility(ENTITY_LOOKUP_WINDOW_SECTION.JOB, true, JOBS_PAGE_ALIAS));
    },
    [JOBS_PAGE_ACTIONS.MANAGE_CLIENTS]: (state, dispatch, context) => {
        dispatch(setManageEntityLookupWindowVisibility(ENTITY_LOOKUP_WINDOW_SECTION.CLIENT, true, JOBS_PAGE_ALIAS));
    },
    [RESOURCES_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        dispatch(toggleFilterPane(RESOURCES_PAGE_FILTER_ALIAS, context.resourcesFilterPaneContextGuid, !getResourcesPageFilterPaneCollapsed(state)));
    },
    [actionTypes.SELECT_WORKSPACE]: (state, dispatch, context) => {
        if (shouldSaveIfAnyChanges(state.plannerPage.workspaces, context.workspaceGuid)) {
            const dispatchAction = saveWorkspaceIfAnyChanges(context.workspaceGuid, context.plansSectionPlanGuid);
            dispatch(promptAction(dispatchAction, PLANNER_PAGE_ALIAS));
        } else {
            dispatch(digestSelectWorkspace(context.plansSectionPlanGuid));
        }
    },
    [actionTypes.DIGEST_CREATE_WORKSPACE]: (state, dispatch) => {
        createWorkspace(state, dispatch);
    },
    [actionTypes.DIGEST_SAVE_AS_NEW_PLAN]: (state, dispatch) => {
        saveAsNewPlan(state, dispatch);
    },
    [actionTypes.MANAGE_WORKSPACES_SECTION.SET_VISIBILITY]: (state, dispatch) => {
        dispatch(setManageMyPlansWindowVisibility(true));
    },
    [actionTypes.SAVE_WORKSPACE_SETTINGS]: (state, dispatch) => {
        const selectedWorkspaceGuid = getSelectedWorkspaceGuid(state.plannerPage.workspaces);
        const workspaceSettingsData = {
            'workspace_settings': JSON.stringify({
                ...getSaveWorkspaceSettings(state.plannerPage, selectedWorkspaceGuid, true)
            }),
            ...getWorkspaceColorSchemeInfo(state.plannerPage.workspaces, selectedWorkspaceGuid)
        };

        dispatch(saveWorkspaceSettings(selectedWorkspaceGuid, workspaceSettingsData));
    },
    [JOBS_PAGE_ACTIONS.TOGGLE_BASE_FILTER]: (state, dispatch, context) => {
        const { filtersAlias, jobsFilterPaneContextGuid: contextGuid, newValue, filterPayload, tableName } = context;
        const selectedFilter = filterPayload.find(filter => filter.baseFilterOption === newValue);

        const { filter, value, baseFilterOption } = selectedFilter;
        const isBaseFilterApplied = baseFilterOption !== DEFAULT_BASE_FILTER_OPTION;

        const setFilterPayload = {
            filter,
            value,
            group: tableName,
            filtersAlias,
            contextGuid,
            baseFilterApplied: isBaseFilterApplied,
            selectedBaseFilter: newValue
        };

        dispatch(resetBaseFilter(filtersAlias, contextGuid));

        if (isBaseFilterApplied) {
            dispatch(setBaseFilter(setFilterPayload));
        }
    },
    [PLANNER_ACTIONS.CREATE_ROLE_GROUP]: (state, dispatch) => {
        dispatch(openCreateRoleGroupModal());
    },
    [PLANNER_ACTIONS.CREATE_ROLE]: (state, dispatch) => {
        const isCriteriaRole = false;
        createRole(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, isCriteriaRole);
    },
    [PLANNER_ACTIONS.CREATE_ROLE_BY_REQUIREMENT]: (state, dispatch) => {
        const isCriteriaRole = true;
        createRole(state, dispatch, PLANNER_ACTORS.COMMAND_BAR, isCriteriaRole);
    },
    [PLANNER_ACTIONS.ARCHIVE_ROLE]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, workflowAccessType, actionKey } = context;

        const accessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state)(entitiesTableName, entitiesIds, entityAccess, null, actionKey, workflowAccessType);
        const payload = buildUpdateRoleRequestPayload(TABLE_NAMES.ROLEREQUEST, accessibleEntitiesIds, ROLE_ITEM_STATUS_KEYS.ARCHIVED);

        dispatch(updateRoleRequestsStatus(PLANNER_PAGE_ALIAS, payload));
    },
    [PLANNER_ACTIONS.RESTART_ROLE]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, workflowAccessType, actionKey } = context;

        const accessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state)(entitiesTableName, entitiesIds, entityAccess, null, actionKey, workflowAccessType);
        const payload = buildUpdateRoleRequestPayload(TABLE_NAMES.ROLEREQUEST, accessibleEntitiesIds, ROLE_ITEM_STATUS_KEYS.DRAFT);

        dispatch(updateRoleRequestsStatus(PLANNER_PAGE_ALIAS, payload));
    },
    [PLANNER_ACTIONS.REJECT_ROLE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName: barTableName } = context;

        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_REJECT_ROLE;
        const payload = buildOpenRoleTransitionDialogPayload(barTableName, entitiesIds, transitionActionType, ROLES_MODAL.REJECT_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(actionTypes.ROLE_TRANSITION_DIALOG.REJECT_REQUEST, payload));
    },
    [PLANNER_ACTIONS.SUBMIT_REQUEST]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName: barTableName } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_SUBMIT_REQUEST;
        const payload = buildOpenRoleTransitionDialogPayload(barTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.SUBMIT_REQUEST, payload));
    },
    [PLANNER_ACTIONS.MOVE_PENDING_RESOURCES]: (state, dispatch, context) => {
        managePendingTimeAllocation(state, dispatch, movePendingTimeAllocationAction);
    },
    [PLANNER_ACTIONS.REMOVE_PENDING_RESOURCES]: (state, dispatch, context) => {
        managePendingTimeAllocation(state, dispatch, removePendingTimeAllocationAction);
    },
    [PLANNER_ACTIONS.MAKE_LIVE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName: barTableName } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE;
        const payload = buildOpenRoleTransitionDialogPayload(barTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.MAKE_LIVE, payload));
    },
    [PLANNER_ACTIONS.MANAGE_ROLE_TEMPLATES]: (state, dispatch, context) => {
        dispatch(openManageRoleTemplatesModal());
    },
    [ROLE_INBOX_PAGE_ACTIONS.CREATE_ROLE]: (state, dispatch) => {
        const isCriteriaRole = false;
        inboxPageCreateRole(state, dispatch, isCriteriaRole);
    },
    [ROLE_INBOX_PAGE_ACTIONS.CREATE_ROLE_BY_REQUIREMENT]: (state, dispatch) => {
        const isCriteriaRole = true;
        inboxPageCreateRole(state, dispatch, isCriteriaRole);
    },
    [ROLE_INBOX_PAGE_ACTIONS.CREATE_ROLE_GROUP]: (state, dispatch) => {
        dispatch(openCreateRoleGroupModal());
    },
    [ROLE_INBOX_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        dispatch(toggleFilterPane(ROLE_INBOX_PAGE_FILTER_ALIAS, context.roleInboxPageFilterPaneContextGuid, !getRoleInboxPagePageFilterPaneCollapsed(state)));
    },
    [ROLE_INBOX_PAGE_ACTIONS.TOGGLE_HIDE_ROLES_BY_HASCRITERIA]: (state, dispatch, context) => {
        const { checked, roleInboxPageFilterPaneContextGuid: contextGuid, filtersAlias, toggleFieldShowValue } = context;

        const getBaseFilterFieldAppliedValues = getRoleInboxPageBaseFilterAppliedValuesSelector(state);
        const currentFilterValues = getBaseFilterFieldAppliedValues(ROLEREQUEST_FIELDS.HASCRITERIA);

        const isToggledToHideValue = true === checked;
        const newFilterValue = isToggledToHideValue
            ? currentFilterValues.filter(val => val !== toggleFieldShowValue)
            : [...currentFilterValues, toggleFieldShowValue];

        const setBaseFilterAction = getRoleInboxPageSetBaseFilterAction(ROLEREQUEST_FIELDS.HASCRITERIA, newFilterValue, contextGuid, filtersAlias);

        dispatch(setBaseFilterAction);
    },
    [ROLE_INBOX_PAGE_ACTIONS.TOGGLE_HIDE_ROLES_BY_STATUS]: (state, dispatch, context) => {
        const { checked, toggleTargetRoleStatus, roleInboxPageFilterPaneContextGuid: contextGuid, filtersAlias } = context;

        const getBaseFilterFieldAppliedValues = getRoleInboxPageBaseFilterAppliedValuesSelector(state);
        const currentVisibleRolesStatuses = getBaseFilterFieldAppliedValues(ROLEREQUEST_FIELDS.STATUS_GUID);
        const targetStatusGuid = getTableDataRoleRequestStatusGuidSelector(state)(toggleTargetRoleStatus);

        const isToggledToHideRolesByStatus = true === checked;
        const newFilterValue = isToggledToHideRolesByStatus
            ? currentVisibleRolesStatuses.filter(val => val !== targetStatusGuid)
            : [...currentVisibleRolesStatuses, targetStatusGuid];

        const setBaseFilterAction = getRoleInboxPageSetBaseFilterAction(ROLEREQUEST_FIELDS.STATUS_GUID, newFilterValue, contextGuid, filtersAlias);

        dispatch(setBaseFilterAction);
    },
    [ROLE_INBOX_PAGE_ACTIONS.DELETE_ROLE]: (state, dispatch, context) => {
        inboxPageDeleteRole(state, dispatch, context.entitiesTableName);
    },
    [ROLE_INBOX_PAGE_ACTIONS.SUBMIT_REQUEST]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_SUBMIT_REQUEST;
        const payload = buildOpenRoleTransitionDialogPayload(entitiesTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.SUBMIT_REQUEST, payload));
    },
    [ROLE_INBOX_PAGE_ACTIONS.MAKE_LIVE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE;
        const payload = buildOpenRoleTransitionDialogPayload(entitiesTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.MAKE_LIVE, payload));
    },
    [ROLE_INBOX_PAGE_ACTIONS.REJECT_ROLE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName: barTableName } = context;

        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_REJECT_ROLE;
        const payload = buildOpenRoleTransitionDialogPayload(barTableName, entitiesIds, transitionActionType, ROLES_MODAL.REJECT_ROLES_DATA_ALIAS);
        dispatch(openRoleTransitionDialogRequest(actionTypes.ROLE_TRANSITION_DIALOG.REJECT_REQUEST, payload));
    },
    [ROLE_INBOX_PAGE_ACTIONS.EDIT_ROLES_BY_NAME]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, actionKey } = context;
        dispatchEditRolesActions(state, dispatch, entitiesIds, entitiesTableName, entityAccess, null, actionKey);
    },
    [ROLE_INBOX_PAGE_ACTIONS.EDIT_ROLES_BY_REQUIREMENTS]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, actionKey } = context;
        dispatchEditRolesActions(state, dispatch, entitiesIds, entitiesTableName, entityAccess, null, actionKey);
    },
    [ROLE_INBOX_PAGE_ACTIONS.ARCHIVE_ROLE]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, actionKey, workflowAccessType } = context;

        const accessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state)(entitiesTableName, entitiesIds, entityAccess, null, actionKey, workflowAccessType);
        const payload = buildUpdateRoleRequestPayload(TABLE_NAMES.ROLEREQUEST, accessibleEntitiesIds, ROLE_ITEM_STATUS_KEYS.ARCHIVED);

        dispatch(updateRoleRequestsStatus(ROLE_INBOX_PAGE_ALIAS, payload));
    },
    [ROLE_INBOX_PAGE_ACTIONS.RESTART_ROLE]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, actionKey, workflowAccessType } = context;

        const accessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state)(entitiesTableName, entitiesIds, entityAccess, null, actionKey, workflowAccessType);
        const payload = buildUpdateRoleRequestPayload(TABLE_NAMES.ROLEREQUEST, accessibleEntitiesIds, ROLE_ITEM_STATUS_KEYS.DRAFT);

        dispatch(updateRoleRequestsStatus(ROLE_INBOX_PAGE_ALIAS, payload));
    },
    [ROLE_INBOX_PAGE_ACTIONS.UNASSIGN_RESOURCE_FROM_ROLE]: (state, dispatch, context) => {
        const { entitiesIds, entitiesTableName, entityAccess, actionKey, workflowAccessType } = context;

        const accessibleIds = getAccessibleEntitiesIdsSelector(state)(entitiesTableName, entitiesIds, entityAccess, null, actionKey, workflowAccessType);

        if (accessibleIds.length === 1) {
            dispatch(roleInboxAssignResourceToCriteriaRolerequest(accessibleIds[0], null));
        }
    },
    [ROLE_INBOX_PAGE_ACTIONS.MOVE_PENDING_TIME_ALLOCATION]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName, options = {} } = context;
        const entityId = entitiesIds[0];

        const dataCollections = getPageTableDatasSelector(state);
        const description = getData(dataCollections, entitiesTableName, entityId)[ROLEREQUEST_FIELDS.DESCRIPTION];

        dispatch(promptAction(
            movePendingTimeAllocationAction(ROLE_INBOX_PAGE_ALIAS, { entityId }),
            ROLE_INBOX_PAGE_ALIAS,
            { description, fixedTimeValue: options.fixedTimeValue }
        ));
    },
    [ROLE_INBOX_PAGE_ACTIONS.REMOVE_PENDING_TIME_ALLOCATION]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName, options = {} } = context;
        const entityId = entitiesIds[0];

        const dataCollections = getPageTableDatasSelector(state);
        const description = getData(dataCollections, entitiesTableName, entityId)[ROLEREQUEST_FIELDS.DESCRIPTION];

        dispatch(promptAction(
            removePendingTimeAllocationAction(ROLE_INBOX_PAGE_ALIAS, { entityId }),
            ROLE_INBOX_PAGE_ALIAS,
            { description, fixedTimeValue: options.fixedTimeValue }
        ));
    },
    [ROLE_INBOX_PAGE_ACTIONS.PUBLISH_ROLE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const entityId = entitiesIds[0];

        const moduleName = ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL;
        const dataCollections = getPageTableDatasSelector(state);
        const roleEntity = getData(dataCollections, entitiesTableName, entityId);

        dispatch(openPublishRoleEW(entityId, roleEntity, moduleName));
    },
    [ROLE_INBOX_PAGE_ACTIONS.EDIT_ROLE_PUBLICATION]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const entityId = entitiesIds[0];

        const moduleName = ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL;
        const dataCollections = getPageTableDatasSelector(state);
        const roleEntity = getData(dataCollections, entitiesTableName, entityId);

        dispatch(openEditRolePublicationEW(entityId, roleEntity, moduleName));
    },
    [ROLE_INBOX_PAGE_ACTIONS.REMOVE_ROLE_PUBLICATION]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const entityId = entitiesIds[0];

        const dataCollections = getPageTableDatasSelector(state);
        const roleEntity = getData(dataCollections, entitiesTableName, entityId);

        dispatch(
            promptAction(
                removeRolePublication(entityId, roleEntity, ROLE_INBOX_PAGE_ALIAS),
                ROLE_INBOX_PAGE_ALIAS,
                {}
            )
        );
    },
    [ROLE_INBOX_PAGE_ACTIONS.MANAGE_ROLE_TEMPLATES]: (state, dispatch, context) => {
        dispatch(openManageRoleTemplatesModal());
    },
    [TABLE_VIEW_PAGE_ACTIONS.CREATE_BOOKING]: (state, dispatch) => {
        tableViewCreateBooking(state, dispatch, TABLE_VIEW_ACTORS.COMMAND_BAR);
    },
    [TABLE_VIEW_PAGE_ACTIONS.CRAETE_JOB]: (state, dispatch) => {
        tableViewCreateJob(state, dispatch);
    },
    [TABLE_VIEW_PAGE_ACTIONS.CREATE_CLIENT]: (state, dispatch) => {
        tableViewCreateClient(state, dispatch);
    },
    [REPORT_ACTIONS.TOGGLE_VIEW_MODE] : (state, dispatch) => {
        dispatch(toggleViewMode());
    },
    [REPORT_ACTIONS.SELECT_EMBED_REPORT]: (state, dispatch, context) => {
        const { key: selectedReportGuid } = context;
        const { viewModeValue: currentViewModeValue, reports = [] } = getReportsDataSelector(state);
        const selectedReport = reports.find(report => report[REPORT_MAPPED_FIELDS.GUID] === selectedReportGuid) || {};

        const {
            [REPORT_MAPPED_FIELDS.CAN_EDIT]: canEditSelectedReport = false,
            [REPORT_MAPPED_FIELDS.TYPE]: reportType
        } = selectedReport;

        const shouldSwitchToViewMode = false === currentViewModeValue && false === canEditSelectedReport;

        if (shouldSwitchToViewMode) {
            dispatch(toggleViewMode());
        }

        const updatedReport = {
            ...selectedReport,
            [REPORT_MAPPED_FIELDS.LAST_ACCESSED_DATE]: getCurrentDate()
        };

        dispatch(selectEmbedReport(selectedReportGuid));
        dispatch(updateReportSuccess(updatedReport));

        if (reportType !== REPORT_TYPE.DEFAULT) {
            dispatch(notifyReportEvent(selectedReportGuid, NOTIFY_REPORT_EVENT_TYPES.ACCESSED));
        }
    },
    [MANAGE_REPORTS_MODAL_ACTIONS.OPEN]: (state, dispatch) => {
        const preselectedReport = null;
        const shouldOpenModal = true;
        dispatch(setupManageReportsModal(preselectedReport, shouldOpenModal));
    },
    [actionTypes.PEOPLE_FINDERS_DIALOG.OPEN]: (state, dispatch) => {
        peopleFinder(state, dispatch);
    },
    [RESTORE_PLANNER_VIEW]: (state, dispatch) => {
        const currentWorkspaceSettings = getCurrentWSSettingsSelector(state);
        const { workspace_guid } = currentWorkspaceSettings;

        dispatch(restorePlannerViewAction(workspace_guid));
    },
    [MARKETPLACE_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        dispatch(toggleFilterPane(MARKETPLACE_PAGE_FILTER_ALIAS, context.marketplacePageFilterPaneContextGuid, !getMarketplacePageFilterPaneCollapsed(state)));
    },
    [MARKETPLACE_PAGE_ACTIONS.TOGGLE_BASE_FILTER]: (state, dispatch, context) => {
        const { filtersAlias, marketplacePageFilterPaneContextGuid: contextGuid, newValue, filterPayload, tableName, actionKey } = context;
        const selectedFilter = filterPayload.find(filter => filter.baseFilterOption === newValue) || {};

        const { filter, value, baseFilterOption } = selectedFilter;
        const isBaseFilterApplied = baseFilterOption !== DEFAULT_BASE_FILTER_OPTION;

        const setFilterPayload = {
            filter,
            value,
            group: tableName,
            filtersAlias,
            contextGuid,
            baseFilterApplied: isBaseFilterApplied,
            selectedBaseFilter: newValue
        };

        dispatch(clearAllFilters(filtersAlias, contextGuid));
        dispatch(resetBaseFilter(filtersAlias, contextGuid));
        dispatch(commandBarUpdateActionElementLabel(MARKETPLACE_PAGE_COMMAND_BAR_ALIAS, actionKey, newValue));

        if (isBaseFilterApplied) {
            dispatch(setBaseFilter(setFilterPayload));
        }
    },
    [TABLE_VIEW_PAGE_ACTIONS.VIEW_SETTINGS]: (state, dispatch, context) => {
        setTableViewViewSettings(state, dispatch, context.newViewSettings);
    },
    [TABLE_VIEW_PAGE_ACTIONS.GO_TO_TODAY]: (state, dispatch) => {

    },
    [TABLE_VIEW_PAGE_ACTIONS.GO_TO_DATE]: (state, dispatch, context) => {
        dispatch(tableViewDateRangeChanged(context.workspaceGuid, context.newStartDate, context.newEndDate, PLANNER_ACTORS.COMMAND_BAR));
    },
    [TABLE_VIEW_PAGE_ACTIONS.GO_TO_DATE_RANGE]: (state, dispatch, context) => {
        const { workspaceGuid, newStartDate, newEndDate } = context;
        dispatch(tableViewDateToggleOptionChanged(workspaceGuid, getCustomOption().value, getViewModeFromDates(newStartDate, newEndDate).name));
        dispatch(tableViewDateRangeChanged(workspaceGuid, newStartDate, newEndDate, PLANNER_ACTORS.COMMAND_BAR));
    },
    [TABLE_VIEW_PAGE_ACTIONS.CHANGE_DATE_TOGGLE]: (state, dispatch, context) => {
    },
    [TABLE_VIEW_PAGE_ACTIONS.CHANGE_SELECTED_DATE_TOGGLE_STEP]: (state, dispatch, context) => {
        changeSelectedDateToggleStep(state, dispatch, context.value, PLANNER_ACTORS.COMMAND_BAR);
    },
    [TABLE_VIEW_PAGE_ACTIONS.TOGGLE_FILTER_PANE]: (state, dispatch, context) => {
        const { workspaceGuid, filtersGuid } = context;
        dispatch(toggleFilterPane(TABLE_VIEW_PAGE_FILTER_ALIAS, filtersGuid, !getTableViewPageFilterPaneCollapsed(state, filtersGuid)));
        dispatch(filterSettingsChanged(workspaceGuid, TABLE_VIEW_PAGE_ALIAS));
    },
    [TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_HISTORIC_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(tableViewHideHistoricRecordsChanged(workspaceGuid, checked));
    },
    [TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_FUTURE_RECORDS]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        dispatch(tableViewHideFutureRecordsChanged(workspaceGuid, checked));
    },
    [TABLE_VIEW_PAGE_ACTIONS.TOGGLE_HIDE_INACTIVE_RESOURCES]: (state, dispatch, context) => {
        const { workspaceGuid, checked } = context;
        const { filtersGuid } = getSelectedWorkspaceSettings(state[TABLE_VIEW_PAGE_ALIAS].workspaces);

        dispatch(tableViewHideInactiveResourcesChanged(workspaceGuid, checked));
        dispatch(unloadMultivaluesFilterLoadedOptions(TABLE_VIEW_PAGE_FILTER_ALIAS, filtersGuid, RESOURCE_GUID, TABLE_NAMES.RESOURCE));
    },
    [ROLE_GROUP_DETAILS_COMMAND_BAR_ACTIONS.MAKE_LIVE]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName = TABLE_NAMES.ROLEREQUEST } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE;
        const payload = buildOpenRoleTransitionDialogPayload(entitiesTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.MAKE_LIVE, payload));
    },
    [ROLE_GROUP_DETAILS_COMMAND_BAR_ACTIONS.SUBMIT_REQUEST]: (state, dispatch, context) => {
        const { entitiesIds = [], entitiesTableName } = context;
        const transitionActionType = ROLES_TRANSITION_ACTION.ROLES_ACTION_SUBMIT_REQUEST;
        const payload = buildOpenRoleTransitionDialogPayload(entitiesTableName, entitiesIds, transitionActionType, ROLES_MODAL.PROGRESS_ROLES_DATA_ALIAS);

        dispatch(openRoleTransitionDialogRequest(ROLES_MODAL.SUBMIT_REQUEST, payload));
    },
    [LIST_PAGE_ACTIONS.VIEW_SETTINGS]: (state, dispatch, context) => {
        dispatch(updateListView(context.newViewSettings));
    },
    [RESOURCES_PAGE_ACTIONS.EDIT_RESOURCE]: (state, dispatch, context) => {
        editResource(state, dispatch);
    },
};

export const getItemDisabled = (state, action, additionalProps) => {
    let disabled = false;
    const { bar } = state.clipboard;
    const { entitiesIds = [] } = additionalProps;

    const isSelectedBarCut = () => {
        let barCut = false;

        if (bar && bar.clipboardAction === PLANNER_BAR_ACTIONS.CUT) {
            barCut = bar.barData[`${bar.barTableName}_guid`] === entitiesIds[0];
        }

        return barCut;
    };

    switch (action) {
        case PLANNER_ACTIONS.EDIT_BOOKING_BARS:
        case PLANNER_ACTIONS.EDIT_ROLES_BY_NAME_BARS:
        case PLANNER_ACTIONS.EDIT_ROLES_BY_REQUIREMENTS_BARS:
        case PLANNER_ACTIONS.DELETE_BAR: {
            if (!hasSelectedEntity({ entities: entitiesIds }) || isSelectedBarCut()) {
                disabled = true;
            }
            break;
        }
        case PLANNER_ACTIONS.CUT_BAR:
        case PLANNER_ACTIONS.COPY_BAR: {
            if (!isSingleEntitySelected(entitiesIds)) {
                disabled = true;
            }
            break;
        }
        case PLANNER_ACTIONS.ROLL_FORWARD: {
            if (!hasSelectedEntity({ entities: entitiesIds }) || isSelectedBarCut()) {
                disabled = true;
            }
            break;
        }
        case PLANNER_ACTIONS.PASTE_BAR: {
            if (!canPasteBar(state)) {
                disabled = true;
            }
            break;
        }
        case JOBS_PAGE_ACTIONS.EDIT_JOB: {
            const selectedJobPane = state.entityWindow.window.jobsPageDetailsPane;

            if (!hasSelectedEntity({ entities: entitiesIds }) && selectedJobPane.loading === false && selectedJobPane.entityId === null) {
                disabled = true;
            }
            break;
        }
        case JOBS_PAGE_ACTIONS.DUPLICATE_JOB: {
            const selectedJobHasLongRunningOperation = hasSelectedEntity({ entities: entitiesIds }) && jobHasLongRunningTaskSelector(state)(entitiesIds[0]);

            if (
                !hasSelectedEntity({ entities: entitiesIds })
                || hasMultipleSelectedEntities({ entities: entitiesIds })
                || selectedJobHasLongRunningOperation
            ) {
                disabled = true;
            }
            break;
        }
        case PLANNER_ACTIONS.CHANGE_DATE_TOGGLE: {
            const selectedWorkspaceSettings = getSelectedWorkspaceSettings(state.plannerPage.workspaces);
            const { dateOption } = selectedWorkspaceSettings.viewMode;
            const { canIncreaseRange } = additionalProps;

            disabled = getDateToggleDisabled(dateOption, canIncreaseRange, PLANNER_DATE_TOGLE_OPTIONS);

            break;
        }
        case actionTypes.SAVE_WORKSPACE_SETTINGS: {
            const selectedWorkspaceGuid = getSelectedWorkspaceGuid(state.plannerPage.workspaces);
            const plan = getSelectedWorkspace(state.plannerPage.workspaces);
            const belongsToRes = workspaceBelongsToResource(plan, getApplicationUserId(state));
            const hasEditRights = getWorkspaceIsEditable(plan);
            const isPublic = getIsPublicWorkspace(plan);
            disabled = !workspaceSettingsChanged(state.plannerPage.workspaces, selectedWorkspaceGuid);

            if (isPublic && !belongsToRes)
                disabled = disabled || !(hasEditRights || hasFunctionalAccessGlobal(state, 'ManageAllPublicPlans'));

            break;
        }
        case PLANNER_ACTIONS.REJECT_ROLE:
        case ROLE_INBOX_PAGE_ACTIONS.REJECT_ROLE: {
            disabled = getIsRejectRolerequestActionDisabled(state, entitiesIds);
            break;
        }
        case ROLE_INBOX_PAGE_ACTIONS.EDIT_ROLES_BY_NAME:
        case ROLE_INBOX_PAGE_ACTIONS.EDIT_ROLES_BY_REQUIREMENTS:
        case ROLE_INBOX_PAGE_ACTIONS.DELETE_ROLE: {
            disabled = !hasSelectedEntity({ entities: entitiesIds });
            break;
        }
        case PLANNER_ACTIONS.CREATE_ROLE_BY_REQUIREMENT: {
            const plannerSelection = getCurrentPlannerSelectionSelector(state)(TABLE_NAMES.ROLEREQUEST);
            const roleResourceGuid = plannerSelection[ROLEREQUEST_FIELDS.RESOURCE_GUID];

            disabled = roleResourceGuid != null;
            break;
        }
        case PLANNER_ACTIONS.SHOW_IN_VIEW: {
            disabled = getRotateViewParentIds(state).length === 0;
            break;
        }
        case RESOURCES_PAGE_ACTIONS.EDIT_RESOURCE: {
            const selectedResourcePane = state.entityWindow.window.resourcesPageDetailsPane;

            if (!hasSelectedEntity({ entities: entitiesIds }) && selectedResourcePane.loading === false && selectedResourcePane.entityId === null) {
                disabled = true;
            }
            break;
        }
        default:
            break;
    }

    return disabled;
};

export const getMenusSectionIndexByKey = (sections, sectionKey) => {
    return sections.findIndex((section) => {
        return section.key === sectionKey;
    });
};

export const getActionSectionIndexByKey = (sections = [], sectionKey) => {
    return sections.findIndex(section => section.actionKey === sectionKey);
};

const subMenuItemType = 'SubMenu';
const radioGroupItemType = 'RadioGroup';
const PageTitleSection = 'PageTitle';

const getUpdatedLabel = (options, getEntityInfo) => {
    const { tableName, singularForm, labelTemplate } = options;
    const entityInfo = getEntityInfo(tableName);
    const entityAlias = singularForm ? getEntitySingularAlias(entityInfo) : getEntityPluralAlias(entityInfo);
    const template = labelTemplate ? commandBarStringTemplates[labelTemplate] : null;
    const newLabel = template ? template(entityAlias) : entityAlias;

    return newLabel;
};

const updateSectionItems = (items, getEntityInfo) => {
    const updatedItems = items.map((element) => {
        if (element.options && element.options.isEntityDependant === true) {
            const updatedLabel = getUpdatedLabel(element.options, getEntityInfo);

            return {
                ...element,
                label: updatedLabel || element.label
            };
        } else if (element.type === subMenuItemType) {
            return { ...element, items: updateSectionItems(element.items, getEntityInfo) };
        }

        return element;
    });

    return updatedItems;
};

const updateSection = (section, getEntityInfo) => {
    if (Array.isArray(section)) {
        return section.map(subSection => {
            if (subSection.items) {
                const updatedSection = {
                    ...subSection,
                    items: updateSectionItems(subSection.items, getEntityInfo)
                };

                return updatedSection;
            } else if (subSection.type === radioGroupItemType) {
                const updatedRadioSection = {
                    ...subSection,
                    options: updateSectionItems(subSection.options, getEntityInfo)
                };

                return updatedRadioSection;
            }

            return subSection;
        });
    } else if (section.type == PageTitleSection) {
        const updatedPageTitle = getUpdatedLabel(section.options, getEntityInfo) || section.pageTitle;

        return {
            ...section,
            pageTitle: updatedPageTitle
        };
    }
};

export const updateCommandBarConfiguration = (cbConfig, getEntityInfo) => {
    const newConfig = {};
    Object.keys(cbConfig).map((sectionKey) => {
        const section = cbConfig[sectionKey];
        newConfig[sectionKey] = updateSection(section, getEntityInfo);
    });

    return newConfig;
};