import { ENTITY_WINDOW_MODULES, JO<PERSON>_PAGE_ALIAS, OPERATORS, PLANNER_PAGE_ALIAS, ROLE_GROUP_DETAILS_PAGE, ROLE_INBOX_PAGE_ALIAS, TABLE_NAMES } from '../constants';
import { WORK_HISTORY_ALIAS } from '../constants/workHistoryConstants';
import { JOB_END_DATE, JOB_RESOURCE_WORKED_HOURS, JOB_START_DATE } from '../constants/fieldConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { getCurrentDate } from './dateUtils';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

export const getWorkHistoryAlias = (alias) => `${alias}_${WORK_HISTORY_ALIAS}`;

export const getWorkHistoryModuleAlias = (alias = '') => {
    return alias.replace(`_${WORK_HISTORY_ALIAS}`, '');
};

export const getLoadWorkHistoryFilterSelection = (resourceGuid = '') => {

    return [
        {
            'tableName': `${TABLE_NAMES.BOOKING}_subTable`,
            'field': `${TABLE_NAMES.BOOKING}_resource_guid`,
            'operator': OPERATORS.DB_OPERATORS.EQUALS,
            'value': resourceGuid
        },
        {
            'field': JOB_START_DATE,
            'operator': OPERATORS.DB_OPERATORS.ASSIGNED
        },
        {
            'field': JOB_END_DATE,
            'operator': OPERATORS.DB_OPERATORS.LESS_THAN_OR_EQUAL,
            'value': getCurrentDate()
        },
        {
            'field': JOB_RESOURCE_WORKED_HOURS,
            'operator': OPERATORS.DB_OPERATORS.GREATER_THAN,
            'value': 0,
            'parameters': {
                ResourceID: resourceGuid
            }
        }
    ];
};

export const getWorkHistoryEntityWindowDataBasedOnAlias = (alias) => {
    let collectionAlias = '';
    let entityWindowModule = '';
    let entityWindowData = {};

    switch (alias) {
        case PROFILE_PAGE_ALIAS:
            collectionAlias = TABLE_NAMES.JOB,
            entityWindowModule = ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL;
            break;
        case PLANNER_PAGE_ALIAS:
            collectionAlias = TABLE_NAMES.JOB,
            entityWindowModule = ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL;
            break;
        case ROLE_INBOX_PAGE_ALIAS:
            collectionAlias = TABLE_NAMES.JOB,
            entityWindowModule = ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL;
            break;
        case JOBS_PAGE_ALIAS:
        case ROLE_GROUP_DETAILS_PAGE:
            collectionAlias = TABLE_NAMES.JOB,
            entityWindowModule = ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL;
            break;
        case RESOURCES_PAGE_ALIAS:
            collectionAlias = TABLE_NAMES.RESOURCE,
            entityWindowModule = ENTITY_WINDOW_MODULES.RESOURCES_PAGE_MODAL;
            break;
        default:
            break;
    }

    entityWindowData = {
        collectionAlias,
        entityWindowModule
    };

    return entityWindowData;
};

export const isRecentWorkGridHasData = (dataCollections) => {
    let hasRecentWorkData = false;

    const filteredDataCollections = dataCollections
        .filter(collection => {
            return collection.tableName === TABLE_NAMES.JOB || (Array.isArray(collection.tableNames) && 0 <= collection.tableNames.indexOf(TABLE_NAMES.JOB))
        });

    filteredDataCollections && filteredDataCollections.forEach((entry) => {
        if (entry.tableName === TABLE_NAMES.JOB) {
            hasRecentWorkData = entry.data.length > 0;
        }
    });

    return hasRecentWorkData;

};