import React from 'react';
import { ConnectedTableOptions } from '../connectedComponents/connectedTableOptions';
import { DATA_GRID_DENSITY_KEYS, TABLE_NAMES } from '../constants';
import { DATA_GRID_EMPTY_GRID_STATE, DATA_GRID_MODULE_CONSTS, DATA_GRID_NO_ITEMS_STATE, DATA_GRID_NO_MATCHING_ITEMS_STATE, FIXED_COLUMN_MIN_WIDTH, DATA_GRID_EMPTY_ROLE_LIST_PAGE_STATE, CUSTOM_DATAGRID_FIELDS, DATA_GRID_DENSITY_OPTIONS  } from '../constants/dataGridConsts';
import { FIELD_LOOKUP_VALUE_TABLE } from '../constants/fieldConsts';
import { ROLEREQUEST_DISPLAY_FIELD } from '../constants/rolesConsts';
import { DataGridEmpty, DataGridEmptyRoleListPage, DataGridNoItems, DataGridNoMatchingItems } from '../lib/dataGrid/emptyStates';
import { getIsCustomPlanningDataField } from './fieldUtils';
import { getFieldTableName, isCalculatedFieldLinked, isCustomLookupOrPlanningDataField, isTableFieldLinked } from './tableStructureUtils';

export const densityFields = [
    { value: DATA_GRID_DENSITY_KEYS.COMPACT, translationId: 'compactDensityOptionTitle', label: 'Compact', icon: 'menu', height: DATA_GRID_DENSITY_OPTIONS.SMALL },
    { value: DATA_GRID_DENSITY_KEYS.DEFAULT, translationId: 'defaultDensityOptionTitle', label: 'Default', icon: 'menu', height: DATA_GRID_DENSITY_OPTIONS.MEDIUM },
    { value: DATA_GRID_DENSITY_KEYS.EXPANDED, translationId: 'expandedDensityOptionTitle', label: 'Expanded', icon: 'menu', height: DATA_GRID_DENSITY_OPTIONS.BIG }
];

const defaultTableOptionsColumnWidth = 60;
export const getTableOptionsColumn = (tableOptionsColumnWidth = defaultTableOptionsColumnWidth, density, tableName, fixedColumns = [], omitFields = [], useMultipleAssignees, configAlias = '') => {
    const defaultColumnHeight = 50;

    return {
        title: <React.Suspense fallback={<div />}>
            <ConnectedTableOptions
                dropdownContainer={() => document.getElementById('data-grid')}
                densityFields={densityFields} triggerSubMenuAction='click'
                tableName={tableName} 
                density={density} 
                fixedColumns={fixedColumns} 
                omitFields={omitFields} 
                configAlias={configAlias}
            />
        </React.Suspense>,
        fixed: 'right',
        key: 'tableOptions',
        width: tableOptionsColumnWidth,
        onCell: (record) => {
            return {
                height: getColumnHeight(density, defaultColumnHeight)
            }
        },
        onHeaderCell: (cell) => {
            return {
                isDataCell: false
            }
        }
    };
};

export const getColumnHeight = (selectedDensity = 'default', defaultColumnHeight = 50) => {
    let { height } = densityFields.find(({ value }) => value === selectedDensity) || {};

    if (height == undefined) {
        height = defaultColumnHeight;
    }

    return height;
};

export const getColumnWidth = (defaultColumnWidth = 300, defaultOptionsColumnWidth = defaultTableOptionsColumnWidth, displayFields = [], isFixed = false, fixedColumnWidth, dataGridClassName, fillContainerWidth = true) => {
    const dataGridContainer = dataGridClassName ? document.getElementsByClassName(dataGridClassName) : document.getElementById('data-grid');
    let columnWidth = defaultColumnWidth;
    let summedWidth = displayFields.length * defaultColumnWidth;

    if (fillContainerWidth && dataGridContainer && dataGridContainer.clientWidth > summedWidth) {
        columnWidth = Math.ceil((dataGridContainer.clientWidth + defaultOptionsColumnWidth) / displayFields.length);
    }

    if (isFixed) {
        if (fixedColumnWidth) {
            columnWidth = fixedColumnWidth;
        } else {
            columnWidth = (FIXED_COLUMN_MIN_WIDTH > columnWidth) ? FIXED_COLUMN_MIN_WIDTH : columnWidth;
        }
    }

    return columnWidth;
};

export const getDataGridRecordData = (wrappedGetFieldInfo, dataPageTableName, getDataWrapped) => {
    return (record) => {
        const result = {};

        Object
            .keys(record)
            .forEach((dataKey, index) => {
                const fieldInfo = wrappedGetFieldInfo(dataKey);
                const tableName = getFieldTableName(fieldInfo, dataPageTableName);

                if (
                    (isTableFieldLinked(dataPageTableName, fieldInfo)
                        || isCalculatedFieldLinked(dataPageTableName, fieldInfo))
                    && record[dataKey]) {
                    const linkedRecordId = record[dataKey];
                    const linkedRecord = getDataWrapped(tableName, linkedRecordId);

                    result[dataKey] = linkedRecord[`${tableName}_description`];
                } else if (isCustomLookupOrPlanningDataField(fieldInfo) && record[dataKey]) {
                    const linkedRecordId = record[dataKey];

                    let dateTable = FIELD_LOOKUP_VALUE_TABLE;
                    let recordKey = `${FIELD_LOOKUP_VALUE_TABLE}_value`;

                    if (getIsCustomPlanningDataField(fieldInfo)) {
                        dateTable = fieldInfo.plannerLookupTable;
                        recordKey = `${fieldInfo.plannerLookupTable}_description`;
                    }

                    const linkedRecord = getDataWrapped(dateTable, linkedRecordId);
                    result[dataKey] = linkedRecord[recordKey];
                } else {
                    result[dataKey] = record[dataKey];
                }

            });

        return result;
    };
};

export const getEmptyStateConfig = (alias) => {
    const {
        DATA_GRID_NO_ITEMS_ICON,
        DATA_GRID_NO_ITEMS_MESSAGE,
        DATA_GRID_NO_ITEMS_CONTENT,
        DATA_GRID_NO_ITEMS_BUTTON_LABEL,
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE,
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT,
        DATA_GRID_ADDITIONAL_STYLE
    } = DATA_GRID_MODULE_CONSTS[alias] || {};

    return {
        [DATA_GRID_NO_ITEMS_STATE]: {
            render: DataGridNoItems,
            props: {
                icon: DATA_GRID_NO_ITEMS_ICON,
                message: DATA_GRID_NO_ITEMS_MESSAGE,
                button: {
                    label: DATA_GRID_NO_ITEMS_BUTTON_LABEL,
                    onClick: () => null
                }
            }
        },
        [DATA_GRID_NO_MATCHING_ITEMS_STATE]: {
            render: DataGridNoMatchingItems,
            props: {
                icon: DATA_GRID_NO_ITEMS_ICON,
                message: DATA_GRID_NO_MATCHING_ITEMS_MESSAGE,
                content: DATA_GRID_NO_MATCHING_ITEMS_CONTENT,
                additionalStyle: DATA_GRID_ADDITIONAL_STYLE
            }
        },
        [DATA_GRID_EMPTY_GRID_STATE]: {
            render: DataGridEmpty,
            additionalStyle: DATA_GRID_ADDITIONAL_STYLE
        },
        [DATA_GRID_EMPTY_ROLE_LIST_PAGE_STATE]: {
            render: DataGridEmptyRoleListPage,
            props: {
                message: DATA_GRID_NO_ITEMS_MESSAGE,
                content: DATA_GRID_NO_ITEMS_CONTENT
            }
        }
    };
};

export const isCustomDataGridField = (filedName) => CUSTOM_DATAGRID_FIELDS.includes(filedName);

export const getCustomDataGridFieldAlias = (fieldName, options) => {
    const { getEntitySingularAliasSelector } = options;
    let alias = fieldName;
    if (fieldName == ROLEREQUEST_DISPLAY_FIELD) {
        alias = getEntitySingularAliasSelector(TABLE_NAMES.ROLEREQUEST);
    }

    return alias;
};

export const getColumnSort = (fieldName, { orderFields }, isColumnSortable = true) => {
    const orderFieldIndex = orderFields.findIndex((orderField) => orderField.field === fieldName);
    const sortDisabled = !isColumnSortable || isCustomDataGridField(fieldName);
    let columnSort = { sortDisabled };

    if (orderFieldIndex > -1) {
        const { order } = orderFields[orderFieldIndex];

        columnSort = {
            ...columnSort,
            sort: order,
            sortIconColor: 'grey'
        };
    }

    return columnSort;
};