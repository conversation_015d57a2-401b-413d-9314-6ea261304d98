import {
    isEqual,
    isBefore,
    differenceInCalendarWeeks,
    differenceInCalendarMonths,
    differenceInCalendarYears,
    isSameDay,
    isSameMonth,
    subDays,
    isSameISOWeek,
    getDayOfYear,
    getISOWeek,
    getQuarter
} from 'date-fns';
import { DATE_UNITS, PLANNER_VIEW_MODES, TABLE_NAMES } from '../constants';
import {
    isAfter,
    startOfDay,
    endOfDay,
    startOfISOWeek,
    lastDayOfISOWeek,
    startOfMonth,
    lastDayOfMonth,
    startOfYear,
    lastDayOfYear,
    addDays,
    addWeeks,
    addMonths,
    addYears,
    subWeeks,
    subMonths,
    subYears,
    getMonth,
    workingDaysBetweenDates,
    isWeekendDay,
    isDateWeekendDay,
    dayValues,
    getLocalTodayDate,
    differenceInCalendarDays,
    parseToUtcDate,
    getDay,
    DATE_TIME_UNITS,
    parseLocalDate,
    parseUtcToLocalDate,
    convertToJSDateObject,
    getAdjustedDstOffset
} from './dateUtils';
import { STYLE_SETTINGS_KEYS } from '../constants/dataGridConsts';
import { getDateToggleOptionByValue, getCustomOption } from './dateToggleOptionsUtils';
import { PLANNER_COLLAPSE_MODES } from '../constants/plannerConsts';
import { getMonthSequelNumber } from './dataGridUtils';
import { getIsCriteriaRole } from './roleRequestsUtils';
import { dayjsInstance } from '../setup/dayjsInstanceSetup';

export const ModesRenderLogicMap = {
    [DATE_UNITS.DATE_UNIT_DAY]: {
        getMajorStartDateLogic: startOfDay,
        getMajorEndDateLogic: endOfDay,
        getLineDateLogic: addDays,
        differenceInUnitsLogic: differenceInCalendarDays,
        isEqualLogic: isSameDay,
        addUnitsLogic: addDays,
        subUnitsLogic: subDays,
        getMajorToYear: getDayOfYear
    },
    [DATE_UNITS.DATE_UNIT_WEEK]: {
        getMajorStartDateLogic: startOfISOWeek,
        getMajorEndDateLogic: lastDayOfISOWeek,
        getLineDateLogic: addWeeks,
        differenceInUnitsLogic: differenceInCalendarWeeks,
        isEqualLogic: isSameDay,
        addUnitsLogic: addWeeks,
        subUnitsLogic: subWeeks,
        getMajorToYear: getISOWeek
    },
    [DATE_UNITS.DATE_UNIT_MONTH]: {
        getMajorStartDateLogic: startOfMonth,
        getMajorEndDateLogic: lastDayOfMonth,
        getLineDateLogic: addMonths,
        differenceInUnitsLogic: differenceInCalendarMonths,
        isEqualLogic: isSameISOWeek,
        addUnitsLogic: addMonths,
        subUnitsLogic: subMonths,
        getMajorToYear: getMonth
    },
    [DATE_UNITS.DATE_UNIT_YEAR]: {
        getMajorStartDateLogic: startOfYear,
        getMajorEndDateLogic: lastDayOfYear,
        getLineDateLogic: addYears,
        differenceInUnitsLogic: differenceInCalendarYears,
        isEqualLogic: isSameMonth,
        addUnitsLogic: addYears,
        subUnitsLogic: subYears,
        getMajorToYear: getQuarter
    }
};

const offsetFromCellBottom = -1;
const tooltipMaxHeight = 170;
const weekLength = 7;
const weekendDaysCount = 2;

export const getRowsDensityData = (record, settings) => {
    return settings[settings.selected][record];
};

export const getSubRowHeight = (barRows = [], singleRowHeight, margin) => {
    const rowsCount = Object.keys(barRows).length;

    return Math.max((rowsCount * singleRowHeight) + ((rowsCount - 1) * margin), singleRowHeight);
};

const getIsCriteriaRoleMasterRecord = (masterRecTableName, row) => {
    return masterRecTableName === TABLE_NAMES.RESOURCE && row.tableName === TABLE_NAMES.ROLEREQUEST;
};

export const getMasterRowHeight = (row, singleRowHeight, margin, expanded, masterRecTableName, collapseMode) => {
    return (row.tableName === masterRecTableName || getIsCriteriaRoleMasterRecord(masterRecTableName, row))
        && (expanded || collapseMode === PLANNER_COLLAPSE_MODES.HIDE_BARS)
        ? singleRowHeight
        : getSubRowHeight(row.barsRowsMap, singleRowHeight, margin);
};

//Recursion, but it will not be infinite for sure (as we will not have infinite nesting)
export function getRowRenderingData(row, detailsFieldHeight = 0, singleRowHeight = 40, barsRowsMargin = 2, masterRecTableName, rowsExpanded = {}, collapseMode = PLANNER_COLLAPSE_MODES.SHOW_BARS) {
    //Should get it from workspace settings so it is the same here and in CGutils
    const borderHeight = 1;
    const rowExpanded = rowsExpanded[row.id];
    const rowHeight = getMasterRowHeight(row, singleRowHeight, barsRowsMargin, rowExpanded, masterRecTableName, collapseMode);

    let renderingData = {
        totalHeight: rowHeight + borderHeight,
        heightIndex: [rowHeight + borderHeight],
        totalRows: 1
    };
    const minTotalExpandedHeight = renderingData.totalHeight + detailsFieldHeight;

    if (rowExpanded /*&& row.subRows.length > 0*/) {
        renderingData.totalHeight = rowHeight;
        renderingData.heightIndex[0] = rowHeight;

        if (row.subRows && row.subRows.length > 0) {
            for (let i = 0; i < row.subRows.length; i++) {
                let tempRow = row.subRows[i];
                let tempRenderingData = getRowRenderingData(tempRow, detailsFieldHeight, singleRowHeight, barsRowsMargin, masterRecTableName, rowsExpanded);

                let tempTotalHeight = renderingData.totalHeight + tempRenderingData.totalHeight - borderHeight;
                let tempHeightIndex = renderingData.heightIndex.concat(tempRenderingData.heightIndex.map(value => value - borderHeight));
                let tempTotalRows = renderingData.totalRows + tempRenderingData.totalRows;

                renderingData = {
                    totalHeight: tempTotalHeight,
                    heightIndex: tempHeightIndex,
                    totalRows: tempTotalRows
                };
            }
        }

        const hasBlankRow = minTotalExpandedHeight > renderingData.totalHeight;

        let tempTotalHeight = hasBlankRow ? minTotalExpandedHeight : renderingData.totalHeight + borderHeight;
        let tempHeightIndex = hasBlankRow ? renderingData.heightIndex.concat(minTotalExpandedHeight - renderingData.totalHeight) : renderingData.heightIndex.concat(borderHeight);
        let tempTotalRows = renderingData.totalRows + 1;

        renderingData = {
            totalHeight: tempTotalHeight,
            heightIndex: tempHeightIndex,
            totalRows: tempTotalRows
        };
    }

    return renderingData;
}

export function getRenderPositions(rows, renderedFrom, rowsExpanded, detailFieldsSumHeight, density, collapseMode = PLANNER_COLLAPSE_MODES.SHOW_BARS) {
    const { ROW_HEIGHT, BARS_ROWS_MARGIN } = STYLE_SETTINGS_KEYS;
    //Should get it from workspace settings so it is the same here and in CGutils
    const borderHeight = 1;
    let currentHeight = renderedFrom.y;
    let result = [];

    //Iterating master records
    for (let i = 0; i < rows.length; i++) {
        const parentRow = rows[i];
        const parentRowExpanded = rowsExpanded[parentRow.id];
        const subRows = parentRow.subRows;

        //Incrementing the current height coordinate
        const rowHeight = getRowsDensityData(ROW_HEIGHT, density);
        const barsRowsMargin = getRowsDensityData(BARS_ROWS_MARGIN, density);

        currentHeight += parentRowExpanded || collapseMode === PLANNER_COLLAPSE_MODES.HIDE_BARS ? rowHeight + borderHeight : 0;
        result[i] = {
            masterStart: currentHeight,
            subPositions: []
        };

        //To Be implemented with real nesting in the future
        if (parentRowExpanded) {
            let subRowsHeight = 0;
            if (subRows && 0 < subRows.length) {
                for (let k = 0; k < subRows.length; k++) {
                    result[i].subPositions[k] = { subStart: subRowsHeight };
                    let currSubRow = subRows[k];

                    subRowsHeight += getSubRowHeight(currSubRow.barsRowsMap, rowHeight, barsRowsMargin);
                    result[i].subPositions[k].subEnd = subRowsHeight;
                }
            }

            //Incrementing the current height coordinate
            currentHeight += subRowsHeight < detailFieldsSumHeight ? detailFieldsSumHeight : subRowsHeight;
        } else if (collapseMode === PLANNER_COLLAPSE_MODES.SHOW_BARS) {
            currentHeight += getRowRenderingData(parentRow, detailFieldsSumHeight, rowHeight, barsRowsMargin, parentRow.tableName, rowsExpanded, collapseMode).totalHeight;
        }

        result[i].masterEnd = currentHeight;
    }

    return result;
}

export function getDateColumnStep(startDate, endDate, width, viewModeOptions, viewMode, hideWeekends, dateToggleOptions) {
    const { name, minorDateUnit } = viewModeOptions;
    const { dateOption } = viewMode;
    let unitsDiff = hideWeekends && isMinorUnitDay(viewModeOptions) && dateOption === getCustomOption().value ?
        workingDaysBetweenDates(startDate, endDate) :
        differenceInUnits(endDate, startDate, minorDateUnit);

    if (hideWeekends && name === PLANNER_VIEW_MODES.VIEW_MODE_WEEK && dateOption !== getCustomOption().value) {
        const dateOptionOffset = getDateToggleOptionByValue(dateOption, dateToggleOptions).offset;
        const weekendDays = dateOptionOffset * 2;
        unitsDiff -= weekendDays;
    } else if (name === PLANNER_VIEW_MODES.VIEW_MODE_MONTH || (isDateWeekendDay(endDate) && hideWeekends && dateOption === getCustomOption().value)) {
        //In order to include all the selected weeks we need to increase unitsDiff by one in view mode months.
        unitsDiff += 1;
    }

    const dateColumnStep = width / unitsDiff;

    return Math.abs(dateColumnStep);
}

export function getMinorStartDate(date, { minorDateUnit }) {
    return ModesRenderLogicMap[minorDateUnit].getMajorStartDateLogic(date);
}

export function getMinorEndDate(date, { minorDateUnit }) {
    return ModesRenderLogicMap[minorDateUnit].getMajorEndDateLogic(date);
}

export function getMinorDateRange(start, end, viewModeOptions, hideWeekends) {
    const { minorDateUnit } = viewModeOptions;
    const minorDateRange = [];
    let date = start;

    while (date < end) {
        if (!(hideWeekends && isMinorUnitDay(viewModeOptions) && isWeekendDay(date))) {
            minorDateRange.push(date);
        }
        date = addUnits(date, 1, minorDateUnit);
    }

    return minorDateRange;
}

export function getMajorDateRange(start, end, viewModeOptions, hideWeekends) {
    const { majorDateUnit } = viewModeOptions;
    const dateRange = [];
    let date = getMajorDate(start, viewModeOptions);

    while (date < end) {
        if (!(hideWeekends && majorDateUnit === DATE_UNITS.DATE_UNIT_DAY && isWeekendDay(date))) {
            dateRange.push(date);
        }
        date = addUnits(date, 1, majorDateUnit);
    }

    return dateRange;
}

export function getMajorDate(date, { minorDateUnit, majorDateUnit }) {
    const minorEndDate = ModesRenderLogicMap[minorDateUnit].getMajorEndDateLogic(date);

    return ModesRenderLogicMap[majorDateUnit].getMajorStartDateLogic(minorEndDate);
}

export function getMajorToYear(date, { majorDateUnit }) {
    return ModesRenderLogicMap[majorDateUnit].getMajorToYear(date);
}

//TODO: these functions are to be double checked for correct units usage when migrating do dayjs

export function differenceInUnits(right, left, units) {
    return dayjsInstance(right).diff(left, units.toLowerCase()); // this is fixing grid cell selection and date window but seems to be 1 day offset between the 2
}

export function addUnits(date, unitsCount, units) {
    return dayjsInstance(date).add(unitsCount, units.toLowerCase()); // this is fixing grid cell selection and date window but seems to be 1 day offset between the 2
}

export function subUnits(date, unitsCount, units) {
    return dayjsInstance(date).add(unitsCount * -1, units.toLowerCase()); // this is fixing grid cell selection and date window but seems to be 1 day offset between the 2
}

export function getPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends, parserFunction = parseLocalDate, adjustDate = true) {
    const startDate = adjustDate ? getAdjustedDstOffset(date, parserFunction(startOfDay(date))) : date;
    const pivotPosition = datePivot.position;
    const stepPerDay = getStepPerDay(step, viewModeOptions, hideWeekends);
    const pivotDate = parserFunction(startOfDay(datePivot.date));

    const minutesInDay = 24 * 60;
    let left = pivotDate; //later
    let right = startDate; //sooner
    let direction = 1;
    let daysBetween = 0;

    if (isAfter(startDate, pivotDate) || isEqual(startDate, pivotDate)) {
        direction = 1;
        left = startDate; //later
        right = pivotDate; //sooner

        daysBetween = hideWeekends
            ? workingDaysBetweenDates(right, left)
            : differenceInCalendarDays(left, right);
    } else if (isBefore(startDate, pivotDate)) {
        direction = -1;

        daysBetween = hideWeekends
            ? workingDaysBetweenDates(right, left)
            : Math.floor(differenceInUnits(left, right, DATE_TIME_UNITS.MINUTE) / minutesInDay);
    }

    return (pivotPosition + direction * daysBetween * stepPerDay);
}

export function getBarEndPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends) {
    const offset = getStepPerDay(step, viewModeOptions, hideWeekends);

    const barEndPosition = viewModeOptions.name === PLANNER_VIEW_MODES.VIEW_MODE_YEAR
        ? getPositionInYearMode(date, datePivot, step, viewModeOptions)
        : getPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends);

    return barEndPosition + offset;
}

export function getSelectedCellStartPosition(date, datePivot, step, viewModeOptions, hideWeekends) {
    let result;

    if (viewModeOptions.name !== PLANNER_VIEW_MODES.VIEW_MODE_YEAR) {
        result = getPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends);
    } else {
        result = getSelectedCellPossitionYearView(date, datePivot, step, viewModeOptions);
    }

    return result;
}

export function getSelectedCellEndPosition(date, datePivot, step, viewModeOptions, hideWeekends) {
    let result;

    if (viewModeOptions.name !== PLANNER_VIEW_MODES.VIEW_MODE_YEAR) {
        const positionFromDate = getPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends);
        const stepPerDay = getStepPerDay(step, viewModeOptions, hideWeekends);

        result = positionFromDate + stepPerDay;
    } else {
        const offset = 1;
        result = getSelectedCellPossitionYearView(date, datePivot, step, viewModeOptions, offset);
    }

    return result;
}

function getSelectedCellPossitionYearView(date, datePivot, step, viewModeOptions, offset = 0) {
    const { minorDateUnitToDay } = viewModeOptions;
    const pivotPosition = datePivot.position;
    const stepPerDay = step / minorDateUnitToDay;
    const startDate = parseUtcToLocalDate(startOfDay(date));
    const pivotDate = parseUtcToLocalDate(startOfDay(datePivot.date));
    const direction = isBefore(startDate, pivotDate) ? -1 : 1;

    const monthsBetween = getMonthSequelNumber(pivotDate, startDate) + offset;

    return pivotPosition + direction * monthsBetween * minorDateUnitToDay * stepPerDay;
}

function getStepPerDay(step, viewModeOptions, hideWeekends = false) {
    let unitToDay = viewModeOptions.minorDateUnitToDay;

    if (hideWeekends && isMinorUnitWeek(viewModeOptions)) {
        unitToDay -= weekendDaysCount;
    }

    return step / unitToDay;
}

function getBarPositionOffset(date, datePivot, step, viewModeOptions, hideWeekends) {
    const weekendDateHidden = hideWeekends && isWeekendDay(convertToJSDateObject(date)) && isAfter(date, datePivot.date);

    return weekendDateHidden ? getStepPerDay(step, viewModeOptions, hideWeekends) : 0;
}

export function getBarPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends) {
    const offset = getBarPositionOffset(date, datePivot, step, viewModeOptions, hideWeekends);

    return viewModeOptions.name === PLANNER_VIEW_MODES.VIEW_MODE_YEAR
        ? getPositionInYearMode(date, datePivot, step, viewModeOptions)
        : getPositionFromDate(date, datePivot, step, viewModeOptions, hideWeekends) + offset;
}

export function getPositionInYearMode(date, datePivot, step, viewModeOptions) {
    const { minorDateUnitToDay } = viewModeOptions;
    const stepPerDay = step / minorDateUnitToDay;
    const wholeMonthsInBetween = differenceInCalendarMonths(date, datePivot.date);

    return datePivot.position + (wholeMonthsInBetween * step) + (getDay(date) * stepPerDay);
}

export function getNextAvailableWorkday(datePivotDate, units, nonWorkDays = weekendDaysCount) {
    const minWorkdaysInWeek = 1;
    const maxWorkdaysInWeek = 6;
    const workdaysInWeek = Math.min(Math.max(weekLength - nonWorkDays, minWorkdaysInWeek), maxWorkdaysInWeek);

    if (units > 0) {
        const day = datePivotDate.get('day');
        const startOfAddition = dayValues.saturday - day;
        const daysAfterSOA = units - startOfAddition;

        if (daysAfterSOA >= 0) {
            const daysToAdd = (Math.floor(daysAfterSOA / workdaysInWeek) + 1) * nonWorkDays;
            units += daysToAdd;
        }
    } else if (units < 0) {
        const startOfAddition = datePivotDate.get('day') + (weekendDaysCount - nonWorkDays);
        const daysAfterSOA = startOfAddition + units;

        if (daysAfterSOA <= 0) {
            const daysToSub = (Math.floor((daysAfterSOA * -1) / workdaysInWeek) + 1) * nonWorkDays;
            units -= daysToSub;
        }
    }

    return units;
}

export function getJumpToWorkDateOffset(date, units) {
    let daysToAdd = Math.floor(units);
    const tempDate = addDays(date, daysToAdd);
    const direction = units >= 0;

    if (tempDate.get('d') === dayValues.saturday) {
        //Jump to Monday or Friday depending on the direction
        daysToAdd = direction ? daysToAdd + 2 : daysToAdd - 1;
    } else if (tempDate.get('d') === dayValues.sunday) {
        //Jump to Monday or Friday depending on the direction
        daysToAdd = direction ? daysToAdd + 1 : daysToAdd - 2;
    }

    return daysToAdd;
}

export function getBarDateFromPosition(position, datePivot, step, viewModeOptions, hideWeekends) {
    const { minorDateUnitToDay } = viewModeOptions;
    const stepPerDay = step / minorDateUnitToDay;
    let days = Math.round((position - datePivot.position) / stepPerDay);
    const date = startOfDay(datePivot.date);

    if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
        days = getNextAvailableWorkday(date, days);
    }

    return addUnits(
        date,
        days,
        DATE_UNITS.DATE_UNIT_DAY
    );
}

export function getBarEndDateFromPosition(position, datePivot, step, viewModeOptions, hideWeekends) {
    const date = endOfDay(getBarDateFromPosition(position, datePivot, step, viewModeOptions, hideWeekends));
    let days = -1;

    if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
        days = getNextAvailableWorkday(date, days);
    }

    return addUnits(
        date,
        days,
        DATE_UNITS.DATE_UNIT_DAY
    );
}

export function getDateFromPosition(position, datePivot, step, viewModeOptions, hideWeekends) {
    const { minorDateUnit } = viewModeOptions;
    let minorUnits = Math.round((position - datePivot.position) / step);
    const date = ModesRenderLogicMap[minorDateUnit].getMajorStartDateLogic(datePivot.date);

    if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
        minorUnits = getNextAvailableWorkday(date, minorUnits);
    }

    return addUnits(
        date,
        minorUnits,
        minorDateUnit
    );
}

export function getEndDateFromPosition(position, datePivot, step, viewModeOptions, hideWeekends) {
    const { minorDateUnit } = viewModeOptions;
    let minorUnits = Math.round((position - datePivot.position) / step) - 1;
    const date = ModesRenderLogicMap[minorDateUnit].getMajorStartDateLogic(datePivot.date);

    if (hideWeekends && isMinorUnitDay(viewModeOptions)) {
        minorUnits = getNextAvailableWorkday(date, minorUnits);
    }

    return addUnits(
        ModesRenderLogicMap[minorDateUnit].getMajorEndDateLogic(datePivot.date),
        minorUnits,
        minorDateUnit
    );
}

export function getBarTouchedData(gridSelection, barProps) {
    const barStartDate = startOfDay(barProps.startDate);

    const barRowInSelection = barProps.yPos >= gridSelection.startYPos && barProps.yPos < gridSelection.endYPos;

    const selectionBeforeBooking = gridSelection.endDate <= barStartDate;
    const selectionAfterBooking = gridSelection.startDate >= barProps.endDate;
    const barColInSelection = !(selectionBeforeBooking || selectionAfterBooking);

    return {
        isTouched: barRowInSelection && barColInSelection,
        selectionStartConflicted: gridSelection.startDate >= barStartDate && gridSelection.startDate <= barProps.endDate,
        selectionEndConflicted: gridSelection.endDate >= barStartDate && gridSelection.endDate <= barProps.endDate
    };
}

export function setBookingDataTableRow(bookingData = {}, rows, rowIndex, childRowIndex) {
    let id = null;

    if (rows[rowIndex]) {
        let tableName = rows[rowIndex].tableName;
        id = rows[rowIndex].id;

        if (childRowIndex != null && rows[rowIndex].subRows && rows[rowIndex].subRows[childRowIndex]) {
            tableName = rows[rowIndex].subRows[childRowIndex].tableName;
            id = rows[rowIndex].subRows[childRowIndex].id;
        }

        bookingData[`booking_${tableName}_guid`] = id;
    }

    return bookingData;
}

export function getSelectionParentRowId(rows, rowIndex) {
    let rowsId = {};

    if (rows[rowIndex]) {
        rowsId = rows[rowIndex].id;
    }

    return rowsId;
}

export function getSelectionChildRowId(rows, rowIndex, childRowIndex) {
    let rowsId = null;

    if (rows[rowIndex]) {
        if (childRowIndex != null && rows[rowIndex].subRows && rows[rowIndex].subRows[childRowIndex]) {
            rowsId = rows[rowIndex].subRows[childRowIndex].id;
        }
    }

    return rowsId;
}

export function getSelectionChildRowTableName(rows, rowIndex, childRowIndex) {
    let tableName = null;

    if (rows[rowIndex] && childRowIndex != null && rows[rowIndex].subRows && rows[rowIndex].subRows[childRowIndex]) {
        tableName = rows[rowIndex].subRows[childRowIndex].tableName;
    }

    return tableName;
}

export function isUnassignedCriteriaRow(row) {
    return row.tableName === TABLE_NAMES.ROLEREQUEST;
}

export function getRowGuidFieldFromBar(row, barTableName) {
    return isUnassignedCriteriaRow(row)
        ? `${barTableName}_guid`
        : `${barTableName}_${row.tableName}_guid`;
}

export function getRowBookingsCount(rows, rowIndex) {
    let barsCount = 0;

    if (rows[rowIndex]) {

        const row = rows[rowIndex];

        for (let i = 0; i < row.subRows.length; i++) {
            barsCount += row.subRows[i].bars.length;
        }

    }

    return barsCount;

}

export function getCellXFromClientX(clientX, calendarGridX, step, datePivot, startDate, viewModeOptions, hideWeekends) {
    const offsetX = clientX - calendarGridX;

    return getCellXFromOffset(offsetX, step, datePivot, startDate, viewModeOptions, hideWeekends);
}

export function getCellXFromOffset(offsetX, step, datePivot, startDate, viewModeOptions, hideWeekends) {
    const position = getPositionFromDate(startOfDay(startDate), datePivot, step, viewModeOptions, hideWeekends);
    const differenceX = offsetX % step;
    const cellXPos = position + offsetX - differenceX;

    return cellXPos;
}

export const isOneDayPeriod = (startDate, endDate) => differenceInCalendarDays(parseToUtcDate(endDate), parseToUtcDate(startDate)) === 0;

export function getCellCenterX(clientX, step, calendarGrid) {

    const offsetFromCalendarStart = clientX - calendarGrid.left;
    const selectedCellNumber = Math.floor(offsetFromCalendarStart / step);
    const cellCenterX = calendarGrid.left + selectedCellNumber * step + (step / 2);

    return cellCenterX;
}

export function getTooltipPositionProps(bookingPositionTop, bookingPositionBottom, cursorPositionX, step, calendarDomObj) {
    let positionX, positionY, align;

    if (bookingPositionBottom + tooltipMaxHeight < calendarDomObj.bottom) {
        positionY = bookingPositionBottom - offsetFromCellBottom;
        align = 'top';
    } else {
        positionY = window.innerHeight - bookingPositionTop;
        align = 'bottom';
    }

    positionX = getCellCenterX(cursorPositionX, step, calendarDomObj);

    let tooltipPositionProps = {
        positionX,
        positionY,
        align
    };

    return tooltipPositionProps;
}

export function processTooltip(tooltipData, operation, actor, positionData, actionToExecute) {

    const positionProps = getTooltipPositionProps(...Object.values(positionData));

    const tooltipOptions = {
        data: tooltipData,
        actor,
        operation,
        x: positionProps.positionX,
        y: positionProps.positionY,
        align: positionProps.align
    };

    actionToExecute(tooltipOptions);
}

export function isCursorWithinCalendarGrid(e, calendarDomObj) {

    const { clientX, clientY } = e;
    const buffer = 10;

    const calendarGrid = calendarDomObj.getBoundingClientRect();

    return (
        clientX - buffer > calendarGrid.left &&
        clientX <= calendarGrid.right &&
        clientY - buffer > calendarGrid.top &&
        clientY <= calendarGrid.bottom
    );
}

export const isUnassignedBar = (barData, barTableName) => barData[`${barTableName}_${TABLE_NAMES.RESOURCE}_guid`] === null;

export const isUnassignedCriteriaBar = (barData, barTableName) => barTableName === TABLE_NAMES.ROLEREQUEST && getIsCriteriaRole(barData) && isUnassignedBar(barData, barTableName);

export const isMinorUnitDay = (viewModeOptions) => viewModeOptions.minorDateUnit === DATE_UNITS.DATE_UNIT_DAY;

export const isMinorUnitWeek = (viewModeOptions) => viewModeOptions.minorDateUnit === DATE_UNITS.DATE_UNIT_WEEK;

export const getCurrentDateRangeFromToday = (currentStartDate, currentEndDate) => {
    const today = startOfDay(getLocalTodayDate());
    const currentDaysDiff = differenceInCalendarDays(currentEndDate, currentStartDate);
    const newEndDate = addDays(today, currentDaysDiff);

    return {
        newStartDate: today,
        newEndDate
    };
};

export function getDateOptionDaysDiff(startDate, endDate, dateOption, dateToggleOptions) {
    const { offset, unit } = getDateToggleOptionByValue(dateOption, dateToggleOptions);
    const daysDiff = dateOption === getCustomOption().value ?
        differenceInCalendarDays(endDate, startDate) :
        differenceInCalendarDays(addUnits(startDate, offset, unit), startDate);

    return daysDiff;
}

const heightToVisibleAreaBufferRatio = 1.5;

export const recordInRenderArea = (verticalScrollYOffset, verticalScrollRatio, height, renderedFrom, recordStartY, recordEndY) => {
    // const { verticalScrollYOffset, verticalScrollRatio, height, renderedFrom } = this.props;
    const visibleAreaBufferSize = height * heightToVisibleAreaBufferRatio;
    const calendarVisibleFrom = verticalScrollYOffset * verticalScrollRatio + renderedFrom.y;
    const calendarRenderAreaStart = calendarVisibleFrom - visibleAreaBufferSize;
    const calendarRenderAreaEnd = calendarVisibleFrom + height + visibleAreaBufferSize;

    return recordEndY >= calendarRenderAreaStart && recordStartY <= calendarRenderAreaEnd;
};

export const recordPastRenderArea = (verticalScrollYOffset, verticalScrollRatio, height, renderedFrom, lastPos) => {
    // const { verticalScrollYOffset, verticalScrollRatio, height, renderedFrom } = this.props;
    const visibleAreaBufferSize = height * heightToVisibleAreaBufferRatio;
    const calendarRenderAreaEnd = verticalScrollYOffset * verticalScrollRatio + renderedFrom.y + visibleAreaBufferSize;

    return lastPos > calendarRenderAreaEnd;
};

export const rangeInHiddenWeekend = (start, end, viewModeOptions, hideWeekends) =>
    hideWeekends &&
    isMinorUnitDay(viewModeOptions) &&
    differenceInCalendarDays(end, start) <= 1 &&
    isWeekendDay(parseToUtcDate(start)) &&
    isWeekendDay(parseToUtcDate(end));