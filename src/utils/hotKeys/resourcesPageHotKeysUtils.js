import { setHotKeysHelpWindowSections, setHotKeysHelpWindowVisibility } from '../../actions/hotKeysHelpWindowActions';
import { commandBarSetSectionVisibility } from '../../actions/listPageActions';
import { COMMAND_BAR_MENUS_SECTION_KEYS, DATA_GRID_DENSITY_KEYS } from '../../constants';
import { JOBS_PAGE_ALIAS, JOBS_PAGE_COMMAND_BAR_ALIAS } from '../../constants/jobsPageConsts';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import HOT_KEYS_ACTION_TYPES from '../../state/hotKeys/listPage/actionTypes';
import { listPageHotKeysConfig } from '../../state/hotKeys/listPage/hotKeysConfig';
import {     
    createJob,
    editJob,
    deleteJob,
    duplicateJob
 } from '../jobsActionsUtils';
import {
    editResource
} from '../listsActionsUtils';
import { translateConfig } from '../translationUtils';

const hotKeyActions = {
    [HOT_KEYS_ACTION_TYPES.ADD_JOB]: (state, dispatch) => {
        createJob(state, dispatch);
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_JOB]: (state, dispatch) => {
        editJob(state, dispatch);
    },
    [HOT_KEYS_ACTION_TYPES.DELETE_JOB]: (state, dispatch) => {
        deleteJob(state, dispatch);
    },
    [HOT_KEYS_ACTION_TYPES.ADD_MENU]: (state, dispatch) => {
        const visible = true;
        dispatch(commandBarSetSectionVisibility(COMMAND_BAR_MENUS_SECTION_KEYS.ADD, visible));
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_MENU]: (state, dispatch) => {
        const visible = true;
        dispatch(commandBarSetSectionVisibility(COMMAND_BAR_MENUS_SECTION_KEYS.EDIT, visible));
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_COMPACT]: (state, dispatch) => {
        dispatch(changeDataGridDensity(JOBS_PAGE_ALIAS, DATA_GRID_DENSITY_KEYS.COMPACT));
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_DEFAULT]: (state, dispatch) => {
        dispatch(changeDataGridDensity(JOBS_PAGE_ALIAS, DATA_GRID_DENSITY_KEYS.DEFAULT));
    },
    [HOT_KEYS_ACTION_TYPES.SET_DISPLAY_DENSITY_TO_EXPANDED]: (state, dispatch) => {
        dispatch(changeDataGridDensity(JOBS_PAGE_ALIAS, DATA_GRID_DENSITY_KEYS.EXPANDED));
    },
    [HOT_KEYS_ACTION_TYPES.SHOW_HELP]: (state, dispatch) => {
        const staticMessages = getTranslationsSelector(state, { sectionName: 'hotKeysHelpWindow', idsArray: ['jobsPage']});
        const hotKeysConfig = translateConfig(listPageHotKeysConfig, listPageHotKeysConfig, staticMessages.jobsPage);

        dispatch(setHotKeysHelpWindowSections(hotKeysConfig, JOBS_PAGE_ALIAS));
        dispatch(setHotKeysHelpWindowVisibility(true));
    },
    [HOT_KEYS_ACTION_TYPES.DUPLICATE_JOB]: (state, dispatch) => {
        duplicateJob(state, dispatch);
    },
    [HOT_KEYS_ACTION_TYPES.EDIT_RESOURCE]: (state, dispatch) => {
        editResource(state, dispatch);
    },
};

export const triggerHotKeyAction = (state, dispatch, hotKey, context) => {
    if(hotKeyActions[hotKey]){
        hotKeyActions[hotKey](state, dispatch, context);
    }
};

export const getHotKeyConfig = (hotKey) => {
    return listPageHotKeysConfig[hotKey];
};