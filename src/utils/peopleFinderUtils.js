import { PLANNER_PAGE_ALIAS } from '../constants';
import {
    startDateFieldByTableName,
    endDateFieldByTableName,
    resourceGuidFieldByTableName,
    resourceGuidsFieldByTableName,
    jobGuidFieldByTableName
} from '../constants/fieldConsts';
import { PLANNER_BOOKING_GROUPS_ALIAS } from '../constants/plannerConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { TABLE_NAMES, ENTITY_WINDOW_MODULES } from '../constants';
import getServerinfo from '../serverInfo';

export const createPeopleFinderActionBar = (resourceGuid, selectedDataRange, tableName, additionalData = {}) => {
    const resourceGuidField = resourceGuidFieldByTableName[tableName];
    const resourceGuidsField = resourceGuidsFieldByTableName[tableName];
    const jobGuidField = jobGuidFieldByTableName[tableName];
    const startDateField = startDateFieldByTableName[tableName];
    const endDateField = endDateFieldByTableName[tableName];

    let entityWindowData = {};

    entityWindowData = {
        [resourceGuidField]: resourceGuid,
        [resourceGuidsField]: [resourceGuid],
        [jobGuidField]: null,
        [startDateField]: selectedDataRange.startDate,
        [endDateField]: selectedDataRange.endDate
    };

    if (Object.keys(additionalData).length !== 0) {
        entityWindowData = {
            ...entityWindowData,
            ...additionalData
        };
    }

    return {
        entityWindowData
    };
};

export const copyUrlToClipboard = (url) => {
    const el = document.createElement('input');
    el.value = url;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);
};

export const getCopyProfileUrl = (userId, navigationLink) => {
    const {
        protocol,
        hostname,
        port = ''
    } = window.location;
    const client = getServerinfo().Client;
    const formattedPort = port ? `:${port}` : '';
    const formattedClient = client ? `/${client}` : '';

    return `${protocol}//${hostname}${formattedPort}${formattedClient}${navigationLink}/${userId}`;
};

export const getEntityWindowDataBasedOnAlias = (alias) => {
    let collectionAlias = '';
    let entityWindowModule = '';
    let entityWindowData = {};

    switch (alias) {
        case PROFILE_PAGE_ALIAS:
            collectionAlias = TABLE_NAMES.RESOURCE,
            entityWindowModule = ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL;
            break;
        case PLANNER_PAGE_ALIAS:
            collectionAlias = PLANNER_BOOKING_GROUPS_ALIAS,
            entityWindowModule = ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL;
            break;
        default:
            break;
    }

    entityWindowData = {
        collectionAlias,
        entityWindowModule
    };

    return entityWindowData;
};