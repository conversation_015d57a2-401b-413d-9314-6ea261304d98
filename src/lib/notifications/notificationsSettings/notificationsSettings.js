import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Form } from '@ant-design/compatible';

// import '@ant-design/compatible/assets/index.css';

import { Spin } from 'antd';
import FooterButtons from '../../actionBar/FooterButtons';
import EmailFrequency from './components/EmailFrequency';
import Icon from '../../icon';
import styles from './notificationSettings.less';
import EntityNotificationsSettingsTable from './components/EntityNotificationsSettingsTable';
import { AliasConstants } from '../../../constants/adminSettingConsts';
import GlobalNotificationsCard from './globalNotificationsCard';
import { useSelector } from 'react-redux';
import { getAdminNotificationsSettingsSelector } from '../../../selectors/notificationsSettingsSelectors';
import { FEATURE_FLAGS, TABLE_NAMES } from '../../../constants/globalConsts';
import { getFeatureFlagSelector } from '../../../selectors/featureManagementSelectors';
import { UnsavedChangesPrompt } from '../../../connectedComponents/connectedPrompt/connectedPrompts/connectedUnsavedChangesPrompt';

export function NotificationSettings(props) {
    const {
        configuration = {},
        alias,
        getEntityInfo,
        form,
        changedFields = [],
        staticMessages = {},
        unsavedChangesPromptProps = {},
        onFormReset,
        loadTableDatas,
        onNotificationSettingSave
    } = props;
    const isAdminNotificationSettings = alias === AliasConstants.ADMIN_NOTIFICATIONS_SETTINGS;
    const { sections = [], pageDescription = '', pageTitle = '', primaryTableName, footer, globalNotificationSettings = {} } = configuration;
    const { unconfirmedBookingsData } = useSelector(state => getAdminNotificationsSettingsSelector(state));
    const [otherFieldChanges, setOtherFieldChanges] = useState([]);
    const [shouldPrompt, setShouldPrompt] = useState(false);
    const bookingTypeNotificationFeatureEnabled = useSelector(getFeatureFlagSelector(FEATURE_FLAGS.BOOKING_TYPE_NOTIFICATION));
    const skillNotificationEnabled = useSelector(getFeatureFlagSelector(FEATURE_FLAGS.SKILL_NOTIFICATION));

    const handleOtherFieldsSettingChange = (field) => {
        setOtherFieldChanges((prev) => {
            if (!prev.includes(field)) {
                return [...prev, field];
            }

            return prev.filter(f => f !== field);
        });
    };

    const footerProps = {
        hasChanges: changedFields.length > 0 || otherFieldChanges.length > 0,
        onSaveClicked: () => {
            onNotificationSettingSave(alias);
            setOtherFieldChanges([]);
        },
        onCancelClicked: () => {
            onFormReset(alias);
            setOtherFieldChanges([]);
        },
        hasErrors: false,
        staticMessages
    };

    useEffect(() => {
        loadTableDatas(alias);
    }, [alias]);

    const navigateToLocation = (location) => {
        if (location) {
            const { pathname, state } = location;
            setTimeout(() => {
                props.history.push(pathname, state);
            }, 0);
        }
    };

    const onPromptDiscardChanges = (location) => {
        onFormReset(alias);
        setOtherFieldChanges([]);
        setShouldPrompt(false);
        navigateToLocation(location);
    };

    const onPromptSaveChanges = (location) => {
        onNotificationSettingSave(alias);
        setOtherFieldChanges([]);
        setShouldPrompt(false);
        navigateToLocation(location);
    };

    const onCancel = () => {
        setShouldPrompt(false);
    };

    const promptProps = {
        ...unsavedChangesPromptProps,
        shouldPrompt: !shouldPrompt && (changedFields.length > 0 || otherFieldChanges.length > 0),
        onSaveChanges: onPromptSaveChanges,
        onDiscardChanges: onPromptDiscardChanges,
        onCancel: onCancel
    };

    //TODO: REMOVE WHEN FEATURE FLAG IS OUTDATED
    const getFilteredSections = () =>{
        if (!skillNotificationEnabled) {
            return sections.filter(x => x.entity !== TABLE_NAMES.RESOURCE);
        }

        return sections;
    };

    const renderForm = () => {
        return (
            <>
                <div className="notificationSettingsContent">
                    {isAdminNotificationSettings && bookingTypeNotificationFeatureEnabled && <GlobalNotificationsCard
                        globalNotificationSettings={globalNotificationSettings}
                        staticMessages={staticMessages}
                        onSettingChanged={handleOtherFieldsSettingChange}
                        unconfirmedBookingsData={unconfirmedBookingsData}
                    />}
                    <div className="notificationCard">
                        {
                            pageTitle && (
                                <div className="notificationsSettingsPageTitle">
                                    <h3>{pageTitle}</h3>
                                </div>
                            )
                        }
                        <div className="notificationSubHeading">
                            <h2 role="presentation" className="preFormatedText">{pageDescription}</h2>
                        </div>
                        <EmailFrequency
                            {...footer}
                            form={form} />
                    </div>
                    <Form id="notificationsSettingsForm">
                        {
                            getFilteredSections().map(section => {
                                return <EntityNotificationsSettingsTable
                                    {...section}
                                    getEntityInfo={getEntityInfo}
                                    form={form}
                                    primaryTableName={primaryTableName} />;
                            })
                        }
                    </Form>
                </div>
                <div className={styles.notificationActions}>
                    <FooterButtons {...footerProps} />
                </div>
            </>
        );
    };

    const antIcon = <Icon type="loading" style={{ fontSize: 32 }} spin />;
    const { loading } = props;

    return (
        <div className="notification-settings-container">
            <Spin indicator={antIcon} spinning={loading} style={{ top: '25%' }}>
                {renderForm()}
            </Spin>
            <UnsavedChangesPrompt {...promptProps} />
        </div>
    );
}

NotificationSettings.propTypes = {
    configuration: PropTypes.object,
    uiFormData: PropTypes.object,
    changedFields: PropTypes.array,
    onFieldDataChange: PropTypes.func,
    onFormReset: PropTypes.func,
    loadTableDatas: PropTypes.func,
    alias: PropTypes.string,
    getEntityInfo: PropTypes.func,
    staticMessages: PropTypes.object,
    loading: PropTypes.bool,
    onNotificationSettingSave: PropTypes.func,
    form: PropTypes
};

export const NotificationSettingsForm = Form.create({
    name: 'notificationsSettingsForm',
    onFieldsChange: (props, changedField) => {
        const { onFieldDataChange, alias } = props;
        onFieldDataChange(alias, changedField);
    },
    mapPropsToFields: (props) => {
        const { uiFormData = {} } = props;
        const fields = Object.keys(uiFormData);

        return fields.reduce((acc, field) => {
            acc[field] = Form.createFormField({
                ...fields[field],
                value: uiFormData[field].value
            });

            return acc;
        }, {});
    }
})(NotificationSettings);
