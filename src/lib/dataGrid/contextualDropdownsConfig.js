import { PLANNER_PAGE_ALIAS, TABLE_NAMES } from '../../constants';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../constants/jobsPageConsts';
import { ENTITY_ACTION_KEYS } from '../../constants/entityAccessConsts';
import { ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../constants/rolegroupListPageConsts';
import { ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS } from '../../constants/roleInboxPageConsts';
import {
    CREATE_FNAS_PER_TABLENAME,
    EDIT_FNAS_PER_TABLENAME,
    ROLEREQUEST_WORKFLOW_ACCESS_TYPES,
    ROLEREQUESTRESOURCE_WORKFLOW_ACCESS_TYPES,
    MANAGE_ROLE_TEMPLATES_FNA,
    PUBLISH_ROLE_FNA,
    EDIT_OPERATION_LOG_FNA
} from '../../constants/tablesConsts';
import { PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS } from '../../constants/peopleFinderConst';
import { OPERTION_LOG_CONTEXTUAL_MENU_ACTIONS } from '../../constants/operationLogDialogConsts';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import { getContextualMenuClickAction } from './entryRenderers';
import { shouldShowItemOnAccess } from '../../selectors/functionalAccessSelectors';
import { RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../constants/resourcesPageConsts';
import { getPrimaryHotKeyDescription } from '../../utils/hotKeys';
import { resourcesPageHotKeysConfig } from '../../state/hotKeys/resourcesPage/hotKeysConfig';
import HOT_KEYS_ACTION_TYPES from '../../state/hotKeys/resourcesPage/actionTypes';

export const getJobsContextualDropdownConfig = (labels = {}) => {
    const {
        detailsJobLabel = 'details',
        editJobLabel = 'Edit',
        viewRoleRequestGroupLabel = 'Compare',
        newRoleRequestGroupLabel = 'Create',
        duplicateJobLabel = 'Duplicate',
        moreOptionsButtonLabel = 'More options'
    } = labels;

    return {
        quickActionDropdown: true,
        label: '',
        items: [
            {
                label: detailsJobLabel,
                type: 'MenuItem',
                onClickActionType: JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_JOB_DETAILS,
                options: { tableName: TABLE_NAMES.JOB }
            },
            {
                label: editJobLabel,
                type: 'MenuItem',
                onClickActionType: JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_JOB,
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.JOB],
                options: { tableName: TABLE_NAMES.JOB }
            },
            {
                label: duplicateJobLabel,
                type: 'MenuItem',
                actionKey: ENTITY_ACTION_KEYS.DUPLICATE_JOB,
                onClickActionType: JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.DUPLICATE_JOB,
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
                options: { tableName: TABLE_NAMES.JOB }
            },
            {
                label: '',
                type: 'Divider'
            },
            {
                label: viewRoleRequestGroupLabel,
                type: 'MenuItem',
                onClickActionType: JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_ROLE_GROUP_LIST,
                options: { tableName: TABLE_NAMES.ROLEREQUESTGROUP }
            },
            {
                label: newRoleRequestGroupLabel,
                type: 'MenuItem',
                onClickActionType: JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.ADD_ROLE_GROUP,
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
                options: { tableName: TABLE_NAMES.ROLEREQUESTGROUP }
            }
        ],
        key: 'roleGroupsContextual',
        openIcon: 'ellipsis',
        type: 'Menu',
        visible: true,
        ariaLabel: moreOptionsButtonLabel
    };
};

export const getRoleGroupContextualDropdownConfig = (labels = {}) => {
    const {
        deleteLabel = 'Delete',
        editLabel = 'Edit',
        duplicateLabel = 'Duplicate',
        openLabel = 'Open',
        moreOptionsButtonLabel = 'More options'
    } = labels;

    return {
        quickActionDropdown: true,
        label: '',
        items: [
            {
                label: openLabel,
                type: 'MenuItem',
                onClickActionType: ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_ROLE_GROUP_DETAILS // should implement action
            },
            {
                label: editLabel,
                icon: 'edit',
                type: 'MenuItem',
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
                onClickActionType: ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_ROLE_GROUP,
                options: { tableName: TABLE_NAMES.ROLEREQUESTGROUP }
            },
            {
                label: duplicateLabel,
                icon: 'duplicateBar',
                type: 'MenuItem',
                onClickActionType: ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.DUPLICATE_ROLE_GROUP,
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUESTGROUP],
                options: { tableName: TABLE_NAMES.ROLEREQUESTGROUP }
            },
            {
                label: '',
                type: 'Divider'
            },
            {
                label: deleteLabel,
                icon: 'delete',
                type: 'MenuItem',
                onClickActionType: ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.DELETE_ROLE_GROUP
            }
        ],
        key: 'roleGroupsContextual',
        openIcon: 'ellipsis',
        type: 'Menu',
        visible: true,
        ariaLabel: moreOptionsButtonLabel
    };
};

const {
    CAN_RESTART,
    CAN_REJECT,
    CAN_ARCHIVE,
    CAN_SUBMIT_REQUEST,
    CAN_MAKE_LIVE,
    CAN_MOVE_PENDING,
    CAN_REMOVE_PENDING,
    CAN_MANAGE_BUDGET
} = ROLEREQUEST_WORKFLOW_ACCESS_TYPES;

const getRoleByNameMenuItems = (labels = {}) => {
    const {
        detailsLabel = 'details',
        editDetailsLabel = 'Edit details',
        deleteLabel = 'Delete',
        archiveLabel = 'Archive',
        restartLabel = 'Restart',
        rejectLabel = 'Reject',
        submitRequestLabel = 'Submit request',
        duplicateLabel = 'Duplicate',
        makeLiveLabel = 'Make live'
    } = labels;

    return [
        {
            label: makeLiveLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MAKE_LIVE,
            workflowAccessType: CAN_MAKE_LIVE,
            actionKey: ENTITY_ACTION_KEYS.MAKE_LIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: submitRequestLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.SUBMIT_REQUEST,
            workflowAccessType: CAN_SUBMIT_REQUEST,
            actionKey: ENTITY_ACTION_KEYS.SUBMIT_REQUEST,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: restartLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.RESTART_ROLE,
            workflowAccessType: CAN_RESTART,
            actionKey: ENTITY_ACTION_KEYS.RESTART,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: rejectLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REJECT_ROLE,
            workflowAccessType: CAN_REJECT,
            actionKey: ENTITY_ACTION_KEYS.REJECT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: editDetailsLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.EDIT_ROLE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: archiveLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.ARCHIVE_ROLE,
            workflowAccessType: CAN_ARCHIVE,
            actionKey: ENTITY_ACTION_KEYS.ARCHIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: duplicateLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DUPLICATE_ROLE,
            selectionIndependentAccess: true,
            actionKey: ENTITY_ACTION_KEYS.DUPLICATE,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'duplicateEntity'
            }
        },
        {
            label: deleteLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DELETE_ROLE,
            entityAccess: ENTITY_ACCESS_TYPES.DELETE,
            actionKey: ENTITY_ACTION_KEYS.DELETE,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'deleteEntity'
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: detailsLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.VIEW_ROLE_BY_NAME,
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                labelTemplate: 'entityDetails'
            }
        }
    ];
};

const getRoleByCriteriaMenuItems = (labels = {}) => {
    const {
        detailsLabel = 'details',
        editDetailsLabel = 'Edit details',
        deleteLabel = 'Delete',
        archiveLabel = 'Archive',
        restartLabel = 'Restart',
        rejectLabel = 'Reject',
        submitRequestLabel = 'Submit request',
        duplicateLabel = 'Duplicate',
        makeLiveLabel = 'Make live',
        movePendingFTE = 'Move pending FTEs',
        removePendingFTE = 'Remove pending FTEs',
        movePendingResourcesLabel = 'Move pending resources',
        removePendingResourcesLabel = 'Remove pending resources',
        manageBudgetLabel = 'Manage budget',
        publishToMarketplaceLabel = 'Publish to Roles board',
        saveAsTemplateLabel = 'Save as template',
        editRolePublicationButtonLabel = 'Edit role publication',
        removeRolePublicationButtonLabel = 'Remove role publication'
    } = labels;

    return [
        {
            label: makeLiveLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MAKE_LIVE,
            workflowAccessType: CAN_MAKE_LIVE,
            actionKey: ENTITY_ACTION_KEYS.MAKE_LIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: submitRequestLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.SUBMIT_REQUEST,
            workflowAccessType: CAN_SUBMIT_REQUEST,
            actionKey: ENTITY_ACTION_KEYS.SUBMIT_REQUEST,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                isTableSelectionDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: restartLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.RESTART_ROLE,
            workflowAccessType: CAN_RESTART,
            actionKey: ENTITY_ACTION_KEYS.RESTART,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: rejectLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REJECT_ROLE,
            workflowAccessType: CAN_REJECT,
            actionKey: ENTITY_ACTION_KEYS.REJECT,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: archiveLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.ARCHIVE_ROLE,
            workflowAccessType: CAN_ARCHIVE,
            actionKey: ENTITY_ACTION_KEYS.ARCHIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: publishToMarketplaceLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.PUBLISH_ROLE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            functionalAccessName: PUBLISH_ROLE_FNA,
            actionKey: ENTITY_ACTION_KEYS.PUBLISH,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: editRolePublicationButtonLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.EDIT_ROLE_PUBLICATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            functionalAccessName: PUBLISH_ROLE_FNA,
            actionKey: ENTITY_ACTION_KEYS.EDIT_ROLE_PUBLICATION,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: removeRolePublicationButtonLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REMOVE_ROLE_PUBLICATION,
            entityAccess: ENTITY_ACCESS_TYPES.DELETE,
            functionalAccessName: PUBLISH_ROLE_FNA,
            actionKey: ENTITY_ACTION_KEYS.REMOVE_ROLE_PUBLICATION,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: movePendingFTE,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MOVE_PENDING_TIME_ALLOCATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.MOVE_PENDING_FTEs,
            workflowAccessType: CAN_MOVE_PENDING,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: removePendingFTE,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REMOVE_PENDING_TIME_ALLOCATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.REMOVE_PENDING_FTEs,
            workflowAccessType: CAN_REMOVE_PENDING,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: movePendingResourcesLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MOVE_PENDING_TIME_ALLOCATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.MOVE_PENDING_RESOURCES,
            workflowAccessType: CAN_MOVE_PENDING,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: removePendingResourcesLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REMOVE_PENDING_TIME_ALLOCATION,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.REMOVE_PENDING_RESOURCES,
            workflowAccessType: CAN_REMOVE_PENDING,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: manageBudgetLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MANAGE_BUDGET,
            workflowAccessType: CAN_MANAGE_BUDGET,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: editDetailsLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.EDIT_ROLE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            actionKey: ENTITY_ACTION_KEYS.EDIT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: duplicateLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DUPLICATE_ROLE,
            selectionIndependentAccess: true,
            actionKey: ENTITY_ACTION_KEYS.DUPLICATE,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'duplicateEntity'
            }
        },
        {
            label: deleteLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DELETE_ROLE,
            entityAccess: ENTITY_ACCESS_TYPES.DELETE,
            actionKey: ENTITY_ACTION_KEYS.DELETE,
            options: {
                isEntityDependant: false,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'deleteEntity'
            }
        },
        {
            label: '',
            type: 'Divider'
        },
        {
            label: detailsLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.VIEW_ROLE_BY_NAME,
            actionKey: ENTITY_ACTION_KEYS.VIEW_DETAILS,
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.ROLEREQUEST,
                singularForm: true,
                labelTemplate: 'entityDetails'
            }
        },
        {
            label: saveAsTemplateLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.SAVE_AS_TEMPLATE,
            actionKey: ENTITY_ACTION_KEYS.SAVE_AS_TEMPLATE,
            entityAccess: ENTITY_ACCESS_TYPES.CREATE,
            functionalAccessName: MANAGE_ROLE_TEMPLATES_FNA,
            options: {
                tableName: TABLE_NAMES.ROLEREQUEST,
                labelTemplate: 'saveAsTemplate'
            }
        }
    ];
};

export const getRolesContextualDropdownConfig = (labels = {}, isCriteriaRole) => {
    const menuItems = isCriteriaRole
        ? getRoleByCriteriaMenuItems(labels)
        : getRoleByNameMenuItems(labels);
    const { moreOptionsButtonLabel = 'More options' } = labels;

    return {
        key: 'rolerequestContextual',
        type: 'Menu',
        items: menuItems,
        label: '',
        visible: true,
        openIcon: 'ellipsis',
        quickActionDropdown: true,
        ariaLabel: moreOptionsButtonLabel
    };
};

export const getAssigneeContextualDropdownConfig = (labels = {}) => {
    const {
        detailsLabel = 'details',
        rejectLabel = 'Reject',
        makeLiveLabel = 'Make live',
        unassignLabel = 'Unassign from',
        moreOptionsButtonLabel = 'More options'
    } = labels;

    const menuItems = [
        {
            label: makeLiveLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MAKE_LIVE,
            workflowAccessType: ROLEREQUESTRESOURCE_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE_ROLE_ASSIGNEE,
            actionKey: ENTITY_ACTION_KEYS.MAKE_LIVE,
            options: {
                tableName: TABLE_NAMES.ROLEREQUESTRESOURCE
            }
        },
        {
            label: rejectLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REJECT_ROLE,
            workflowAccessType: ROLEREQUESTRESOURCE_WORKFLOW_ACCESS_TYPES.CAN_REJECT_ROLE_ASSIGNEE,
            actionKey: ENTITY_ACTION_KEYS.REJECT,
            options: {
                tableName: TABLE_NAMES.ROLEREQUESTRESOURCE
            }
        },
        {
            label: unassignLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.UNASSIGN_FROM_ROLE,
            workflowAccessType: ROLEREQUESTRESOURCE_WORKFLOW_ACCESS_TYPES.CAN_UNASSIGN_FROM_ROLE,
            actionKey: ENTITY_ACTION_KEYS.UNASSIGN_FROM_ROLE,
            options: {
                tableName: TABLE_NAMES.ROLEREQUESTRESOURCE,
                entityAliasTableName: TABLE_NAMES.ROLEREQUEST,
                labelTemplate: 'unassignResourceFromRole',
                isEntityDependant: true,
                capitalized: false
            }
        },
        {
            label: detailsLabel,
            type: 'MenuItem',
            onClickActionType: ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.VIEW_ROLE_BY_NAME,
            actionKey: ENTITY_ACTION_KEYS.VIEW_DETAILS,
            options: {
                isEntityDependant: true,
                tableName: TABLE_NAMES.RESOURCE,
                singularForm: true,
                labelTemplate: 'entityDetails'
            }
        }
    ];

    return {
        key: 'assigneeContextual',
        type: 'Menu',
        items: menuItems,
        label: '',
        visible: true,
        openIcon: 'ellipsis',
        quickActionDropdown: true,
        ariaLabel: moreOptionsButtonLabel
    };
};

const getPeopleFinderMenuItems = (labels = {}, alias) => {
    const {
        createBookingEllipsisLabel = 'Create Booking...',
        createRoleByNameEllipsisLabel = 'Create Role by name...',
        editEllipsisLabel = 'Edit...',
        goToProfileEllipsisLabel = 'Go to profile',
        copyProfileUrlEllipsisLabel = 'Copy profile URL'
    } = labels;

    const menuItems = {
        [PLANNER_PAGE_ALIAS]: [
            {
                label: createBookingEllipsisLabel,
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.CREATE_BOOKING,
                functionalAccessName: CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING],
                entityAccess: ENTITY_ACCESS_TYPES.CREATE,
                options: {
                    isEntityDependant: false,
                    tableName: TABLE_NAMES.BOOKING,
                    singularForm: true,
                    labelTemplate: 'createBookingEntity'
                }
            },
            {
                label: createRoleByNameEllipsisLabel,
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.CREATE_ROLE_BY_NAME,
                options: {
                    isEntityDependant: false,
                    tableName: TABLE_NAMES.ROLEREQUEST,
                    singularForm: true,
                    labelTemplate: 'createRoleByNameEntity'
                }
            },
            {
                label: '',
                type: 'Divider'
            },
            {
                label: editEllipsisLabel,
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.EDIT,
                functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.RESOURCE],
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                options: {
                    tableName: TABLE_NAMES.RESOURCE
                }
            },
            {
                label: '',
                type: 'Divider'
            },
            {
                label: goToProfileEllipsisLabel,
                icon: 'external-link-goToProfile',
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.GO_TO_PROFILE
            },
            {
                label: copyProfileUrlEllipsisLabel,
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.COPY_PROFILE_URL
            }
        ],
        [PROFILE_PAGE_ALIAS]: [
            {
                label: goToProfileEllipsisLabel,
                icon: 'external-link-goToProfile',
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.GO_TO_PROFILE
            },
            {
                label: copyProfileUrlEllipsisLabel,
                type: 'MenuItem',
                onClickActionType: PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.COPY_PROFILE_URL
            }
        ]
    };

    return menuItems[alias] || [];
};

export const getPeopleFinderContextualDropdownConfig = (labels = {}, alias) => {
    const menuItems = getPeopleFinderMenuItems(labels, alias);
    const { moreOptionsButtonLabel = 'More options' } = labels;

    return {
        key: 'resourceContextual',
        type: 'Menu',
        items: menuItems,
        label: '',
        visible: true,
        openIcon: 'ellipsis',
        quickActionDropdown: true,
        ariaLabel: moreOptionsButtonLabel
    };
};

export const buildRoleGroupContextualMenuProps = (props) => {
    return {
        ...props,
        config: getRoleGroupContextualDropdownConfig(props.labels),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem: (item) => shouldShowItemOnAccess(props, item),
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

export const getOperationLogContextualDropdownConfig = (label) => {
    const menuItems = getOperationLogMenuItems(label);

    return {
        key: 'operationContextual',
        type: 'Menu',
        items: menuItems,
        label: '',
        visible: true,
        openIcon: 'ellipsis',
        quickActionDropdown: true
    };
};

const getOperationLogMenuItems = (label) => {

    const menuItems = [
        {
            icon: label === 'Undo operation' ? 'delete' : 'cancel',
            label: label,
            type: 'MenuItem',
            onClickActionType: OPERTION_LOG_CONTEXTUAL_MENU_ACTIONS.CANCEL_LONG_RUNNING_OPERATION,
            functionalAccessName: EDIT_OPERATION_LOG_FNA,
            selectionIndependentAccess: true
        }
    ];

    return menuItems || [];
};

export const getResourcesContextualDropdownConfig = (labels = {}) => {
    const {
        detailsResourceLabel = 'Resource details',
        goToProfileEllipsisLabel = 'Go to Profile',
        editEllipsisLabel = 'Edit...',
        moreOptionsButtonLabel = 'More options'
    } = labels;

    return {
        quickActionDropdown: true,
        label: '',
        items: [
            {
                label: detailsResourceLabel,
                type: 'MenuItem',
                icon: 'info-circle-grey',
                onClickActionType: RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_RESOURCE_DETAILS,
                options: { tableName: TABLE_NAMES.RESOURCE }
            },
            {
                label: goToProfileEllipsisLabel,
                type: 'MenuItem',
                icon: 'user-circle',
                onClickActionType: RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.GO_TO_PROFILE
            },
            {
                label: editEllipsisLabel,
                type: 'MenuItem',
                icon: 'edit-resource',
                onClickActionType: RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_RESOURCE,
                hotKeyDescription: getPrimaryHotKeyDescription(resourcesPageHotKeysConfig[HOT_KEYS_ACTION_TYPES.EDIT_RESOURCE]),
                entityAccess: ENTITY_ACCESS_TYPES.EDIT,
                functionalAccessName: EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.RESOURCE],
                options: { tableName: TABLE_NAMES.RESOURCE }
            }
        ],
        key: 'resourceContextual',
        openIcon: 'ellipsis',
        type: 'Menu',
        visible: true,
        ariaLabel: moreOptionsButtonLabel
    };
};