import React from 'react';

import { Spin } from 'antd';

import { Prefix<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ell, getJobStateCellIcon, <PERSON>ffixCell, Calc<PERSON>ell, ContextualMenuCellWrapper, HealthCell } from '../entryRenderers/fieldRenderers';
import { isBlankFieldValue } from '../../../utils/fieldControlUtils';
import { FIELD_DATA_TYPES, TABLE_NAMES } from '../../../constants';
import styles from './styles.css';
import { formatFieldValue } from '../../../utils/fieldControlUtils';
import { getIsCustomCalcField, getIsCalculated, getIsFieldSetToHidden, getIsSystemDateField } from '../../../utils/fieldUtils';
import { getJobsContextualDropdownConfig, getRolesContextualDropdownConfig, getPeopleFinderContextualDropdownConfig, getOperationLogContextualDropdownConfig, getAssigneeContextualDropdownConfig, buildRoleGroupContextualMenuProps, getResourcesContextualDropdownConfig } from '../contextualDropdownsConfig';
import { JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../../constants/jobsPageConsts';
import { ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../../constants/rolegroupListPageConsts';
import { shouldShowItemOnAccess } from '../../../selectors/functionalAccessSelectors';
import { getRoleItemTag } from '../../rolesList/roleItemTag';
import { ROLE_ITEM_STATUS_CLASS_NAMES } from '../../../constants/rolesConsts';
import { JOB_DESCRIPTION, JOB_GUID, ROLEREQUESTGROUP_FIELDS, ROLEREQUEST_FIELDS } from '../../../constants/fieldConsts';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS } from '../../../constants/roleInboxPageConsts';
import { DATA_GRID_FIELDS_BLANK_VALUES } from '../../../constants/dataGridConsts';
import { OPERTION_LOG_CONTEXTUAL_MENU_ACTIONS } from '../../../constants/operationLogDialogConsts';
import { ProgressBar } from '../../progressBar/roleProgressBar';
import { PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS } from '../../../constants/peopleFinderConst';
import { getIsAssigneeActionAllowed } from '../../../utils/assigneeActionsUtils';
import ConnectedJobPrefixComponent from '../../../connectedComponents/connectedJobPrefixCmponent';
import withClassNameMap from '../../hocs/withClassNameMap';
import { HEALTH_CELL_STATUS_CLASSNAME } from '../../badges/healthBadge/healthBadgeClassNameMaps';
import Icon from '../../icon';
import { parseUtcToLocalDate } from '../../../utils/dateUtils';
import { RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS } from '../../../constants/resourcesPageConsts';
import { LIST_PAGE_ALIAS } from '../../../constants/listPageConsts';

const getCellFromDataType = (fieldInfo, tableName, displayValue, textClassName, progressBarOptions) => {
    const { name: fieldName, dataType } = fieldInfo;
    let cell = <span>{displayValue}</span>;

    switch (dataType) {
        case FIELD_DATA_TYPES.STRING:
        case FIELD_DATA_TYPES.DATE_TIME: {
            cell = <DefaultCell text={displayValue} className={textClassName} />;
            break;
        }
        case FIELD_DATA_TYPES.ID:
            cell = getIdFieldDataTypeCell(fieldName, tableName, displayValue, textClassName, progressBarOptions);
            break;
        default:
            break;
    }

    return cell;
};

export const getContextualMenuClickAction = (actions, context) => {
    const {
        viewJobsDetails,
        editJob,
        viewRoleGroupList,
        addRoleGroup,
        deleteRoleGroup,
        addRoleByName,
        addCriteriaRole,
        viewRoleGroupDetails,
        viewRoleByNameDetails,
        editRole,
        deleteRole,
        archiveRole,
        restartRole,
        rejectRole,
        duplicateRole,
        submitRequest,
        makeLive,
        viewCriteriaRole,
        unassignFromRole,
        movePendingTimeAllocation,
        removePendingTimeAllocation,
        createBookingPeopleFinder,
        createRoleByNamePeopleFinder,
        cancelLongRunningOperation,
        editResource,
        viewResourceDetails,
        manageBudget,
        goToProfile,
        copyProfileUrl,
        publishRole,
        saveRoleAsTemplate,
        editPublication,
        removePublication,
        duplicateJob,
        duplicateRoleGroup,
        editRoleGroup
    } = actions;

    switch (context.actionType) {
        case JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_JOB_DETAILS:
            return viewJobsDetails();
        case JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_JOB:
            return editJob();
        case JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_ROLE_GROUP_LIST:
            return viewRoleGroupList();
        case JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.ADD_ROLE_GROUP:
            return addRoleGroup();
        case JOBS_PAGE_CENTEXTUAL_MENU_ACTIONS.DUPLICATE_JOB:
            return duplicateJob();
        case RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_RESOURCE_DETAILS:
            return viewResourceDetails();
        case RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_RESOURCE:
            return editResource();
        case RESOURCES_PAGE_CENTEXTUAL_MENU_ACTIONS.GO_TO_PROFILE:
            return goToProfile();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.DELETE_ROLE_GROUP:
            return deleteRoleGroup();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.CREATE_ROLE_BY_NAME:
            return addRoleByName();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.CREATE_CRITERIA_ROLE:
            return addCriteriaRole();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.VIEW_ROLE_GROUP_DETAILS:
            return viewRoleGroupDetails();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.EDIT_ROLE_GROUP:
            return editRoleGroup();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.VIEW_ROLE_BY_NAME:
            return viewRoleByNameDetails();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.EDIT_ROLE:
            return editRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DELETE_ROLE:
            return deleteRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.ARCHIVE_ROLE:
            return archiveRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.RESTART_ROLE:
            return restartRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REJECT_ROLE:
            return rejectRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.DUPLICATE_ROLE:
            return duplicateRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.SUBMIT_REQUEST:
            return submitRequest();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MAKE_LIVE:
            return makeLive();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.VIEW_CRITERIA_ROLE:
            return viewCriteriaRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.UNASSIGN_FROM_ROLE:
            return unassignFromRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MOVE_PENDING_TIME_ALLOCATION:
            return movePendingTimeAllocation();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REMOVE_PENDING_TIME_ALLOCATION:
            return removePendingTimeAllocation();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.MANAGE_BUDGET:
            return manageBudget();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.PUBLISH_ROLE:
            return publishRole();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.SAVE_AS_TEMPLATE:
            return saveRoleAsTemplate();
        case PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.EDIT:
            return editResource();
        case PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.CREATE_BOOKING:
            return createBookingPeopleFinder();
        case PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.CREATE_ROLE_BY_NAME:
            return createRoleByNamePeopleFinder();
        case PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.GO_TO_PROFILE:
            return goToProfile();
        case PEOPLE_FINDER_CONTEXTUAL_MENU_ACTIONS.COPY_PROFILE_URL:
            return copyProfileUrl();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.EDIT_ROLE_PUBLICATION:
            return editPublication();
        case ROLE_INBOX_PAGE_CONTEXTUAL_MENU_ACTIONS.REMOVE_ROLE_PUBLICATION:
            return removePublication();
        case ROLE_GROUP_LIST_PAGE_CENTEXTUAL_MENU_ACTIONS.DUPLICATE_ROLE_GROUP:
            return duplicateRoleGroup();
        case OPERTION_LOG_CONTEXTUAL_MENU_ACTIONS.CANCEL_LONG_RUNNING_OPERATION:
            return cancelLongRunningOperation();
        default:
            return null;
    }
};

const buildJobsContextualMenuProps = (props) => {
    return {
        ...props,
        config: getJobsContextualDropdownConfig(props.labels),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem: (item) => shouldShowItemOnAccess(props, item),
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

const buildRolesContextualMenuProps = (props) => {
    return {
        ...props,
        config: getRolesContextualDropdownConfig(props.labels, props.isCriteriaRole),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem: (item) => shouldShowItemOnAccess(props, item),
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

const buildAssigneesContextualMenuProps = (props) => {
    const shouldShowItem = (item) => shouldShowItemOnAccess(props, item) && getIsAssigneeActionAllowed(item.actionKey, props.data);

    return {
        ...props,
        config: getAssigneeContextualDropdownConfig(props.labels),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem,
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

const buildPeopleFinderContextualMenuProps = (props) => {
    const shouldShowItem = (item) => shouldShowItemOnAccess(props, item);

    return {
        ...props,
        config: getPeopleFinderContextualDropdownConfig(props.labels, props.alias),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem,
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

const buildOperationLogContextualMenuProps = (props) => {
    const shouldShowItem = (item) => shouldShowItemOnAccess(props, item);

    return {
        ...props,
        config: getOperationLogContextualDropdownConfig(props.labels),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem,
        getItemDisabled: () => false,
        setVisibility: () => { }
    };
};

const buildResourcesContextualMenuProps = (props) => {
    return {
        ...props,
        config: getResourcesContextualDropdownConfig(props.labels),
        visible: false,
        onAction: (context) => getContextualMenuClickAction(props.actions, context),
        shouldShowItem: (item) => shouldShowItemOnAccess(props, item),
        getItemDisabled: ()=> false,
        setVisibility: ()=> {}
    };
};

export const createCellFieldRenderer = (
    tableName,
    fieldInfo,
    fieldValue,
    {
        fieldName,
        onClick,
        loaded,
        prefix,
        suffix,
        getFieldBlankValue,
        noValueSetMessage,
        className = '',
        supportsContextualMenu = false,
        contextualProps = {},
        isLink = false,
        isLocalDate = false,
        isHealth = false,
        useDefaultCalcCell = true,
        getDynamicComponent = () => null,
        isLinkCellExcluded = () => null,
        isDynamicComponent = false,
        staticLabels = {},
        progressBarOptions,
        shouldHideFieldValue = () => false,
        dateFormat
    } = {},
    data = {}
) => {
    const fieldHasBlankValue = isBlankFieldValue(fieldValue);
    const shouldHideValue = shouldHideFieldValue(data, fieldName);

    let textClassName = className;
    let displayValue;

    if (fieldHasBlankValue) {
        displayValue = getFieldBlankValue(fieldInfo.name, fieldInfo.alias, data.isNestedRow, shouldHideValue);
        textClassName = `${styles.placeholder} ${textClassName}`;
    } else {
        displayValue = shouldHideValue
            ? ''
            : formatFieldValue(fieldValue, fieldInfo, { dateFormat, keepLocalTime: !getIsSystemDateField(fieldInfo) });
    }

    if (!loaded) {
        const icon = (
            <Icon
                type={'loading'}
                spin
            />
        );

        return (
            <Spin
                indicator={icon}
            />
        );
    }

    if (getIsFieldSetToHidden(fieldInfo.name, data)) {
        return <Icon type="lock" />;
    }

    const hasPrefixComponent = (fieldInfo) => fieldInfo.name === JOB_DESCRIPTION || fieldInfo.name === ROLEREQUESTGROUP_FIELDS.DESCRIPTION;
    const getPrefixComponent = (tableName, data) => {
        let result = null;

        switch (tableName) {
            case TABLE_NAMES.JOB:
                result = <ConnectedJobPrefixComponent id={data[JOB_GUID] || null} data={data} className={styles.jobPrefix} />;
                break;
            case TABLE_NAMES.ROLEREQUESTGROUP:
                result = <Icon className={styles.prefixIcon} type="role-group-avatar"/>;
                break;
        }

        return result;
    };

    const getContextualDropdownProps = (tableName, props) => {
        let result;
        switch (tableName) {
            case TABLE_NAMES.JOB:
                result = buildJobsContextualMenuProps(props);
                break;
            case TABLE_NAMES.ROLEREQUESTGROUP:
                result = buildRoleGroupContextualMenuProps(props);
                break;
            case TABLE_NAMES.ROLEREQUEST:
                const { isNestedRow = false } = props.data || {};

                result = isNestedRow ? buildAssigneesContextualMenuProps(props) : buildRolesContextualMenuProps(props);
                break;
            case TABLE_NAMES.RESOURCE:
                result = props.pageAlias === LIST_PAGE_ALIAS ? buildResourcesContextualMenuProps(props) : buildPeopleFinderContextualMenuProps(props);
                break;
            case TABLE_NAMES.OPERATIONSLOG:
                result = buildOperationLogContextualMenuProps(props);
                break;
            default:
                result = {};
                break;
        }

        return result;
    };
    let renderer = getCellFromDataType(fieldInfo, tableName, displayValue, textClassName, progressBarOptions);

    if (getIsCustomCalcField(fieldInfo) || getIsCalculated(fieldInfo)) {
        renderer = useDefaultCalcCell
            ? <CalcCell fieldInfo={fieldInfo} value={displayValue} noValueSetMessage={noValueSetMessage} staticLabels={staticLabels}/>
            : <span>{displayValue}</span>;
    }

    if (isLink && !isLinkCellExcluded(data)) {
        let prefixComponent = null;
        let linkClassName = className;
        const text = <span className={styles.linkValue}>{displayValue}</span>;

        if (hasPrefixComponent(fieldInfo)) {
            prefixComponent = getPrefixComponent(tableName, data);
            linkClassName = `${linkClassName} ${styles.prefixComponent}`;
        }

        renderer = <LinkCell title={displayValue} text={text} onClick={onClick} className={linkClassName} prefixComponent={prefixComponent} />;
    }

    if (prefix) {
        renderer = <PrefixCell text={renderer || displayValue} prefix={prefix} className={className}/>;
    }

    if (suffix) {
        renderer = <SuffixCell text={displayValue} suffix={suffix} />;
    }

    if (isDynamicComponent) {
        renderer = getDynamicComponent(displayValue, data, fieldInfo.name || fieldName, isLink, onClick);
    }

    if (isLocalDate) {
        let displayValue = parseUtcToLocalDate(fieldValue);
        const text = formatFieldValue(displayValue, fieldInfo, { dateFormat, keepLocalTime: !getIsSystemDateField(fieldInfo) });

        renderer = <DefaultCell text={text} />;
    }

    if (supportsContextualMenu) {
        renderer = <ContextualMenuCellWrapper className={styles.contextuallyWrappedText} contextualMenuProps={getContextualDropdownProps(tableName, contextualProps)}>{renderer}</ContextualMenuCellWrapper>;
    }

    if (isHealth) {
        const Cell = withClassNameMap(HealthCell, HEALTH_CELL_STATUS_CLASSNAME, 'text');

        renderer = <Cell text={displayValue}/>;
    }

    return renderer;
};

const getIdFieldDataTypeCell = (fieldName, tableName, displayValue, textClassName, progressBarOptions = {}) => {
    let cell = <span>{displayValue}</span>;

    if (fieldName === `${tableName}_jobstatus_guid`) {
        cell = <PrefixCell text={displayValue} prefix={getJobStateCellIcon(displayValue)} />;
    } else if (fieldName === ROLEREQUEST_FIELDS.STATUS_GUID) {
        const statusTag = getRoleItemTag({ value: displayValue, className: ROLE_ITEM_STATUS_CLASS_NAMES[displayValue] });
        const { displayProgressBar, progressBarClassName, filledPercentage } = progressBarOptions;
        const displayText = <>
            {statusTag}
            {displayProgressBar && <ProgressBar className={progressBarClassName} filledPercentage={filledPercentage} />}
        </>;

        cell = <DefaultCell status={displayValue} text={displayText} />;
    } else {
        if (fieldName === ROLEREQUEST_FIELDS.RESOURCE_GUID) {
            const blankValue = DATA_GRID_FIELDS_BLANK_VALUES[ROLE_INBOX_PAGE_ALIAS][ROLEREQUEST_FIELDS.RESOURCE_GUID];
            textClassName = displayValue == blankValue ? '' : textClassName;
        }

        cell = <DefaultCell text={displayValue} className={textClassName} />;
    }

    return cell;
};