import { Breadcrumb } from 'antd';
import React from 'react';
import styles from './styles.less';
import { buildBrowserHistoryUrl } from '../../history';
import { capitalizeFirstLetter } from '../../utils/commonUtils';
import { Link } from 'react-router-dom';
import Icon from '../icon';

export const buildBreadcrumbOptions = (breadcrumbOptions, params, subPagesOptionsMap, masterPageParams, masterPageAlias, aliasPageDisplayName, listPageAndBulkUpdateFeatureFlag) => {
    const result = [];

    result.push({
        description: capitalizeFirstLetter(aliasPageDisplayName.toLowerCase()),
        params: masterPageParams,
        pageAlias: masterPageAlias,
        listPageAndBulkUpdateFeatureFlag: listPageAndBulkUpdateFeatureFlag
    });

    breadcrumbOptions.forEach((element, index) => {
        params[element] && result.push({
            description: params[element],
            params: {
                subPageOption: {
                    subPageNavLink: subPagesOptionsMap['navLinks'][index],
                    subPagesParams: params
                }
            },
            pageAlias: subPagesOptionsMap['aliases'][index],
            listPageAndBulkUpdateFeatureFlag: listPageAndBulkUpdateFeatureFlag
        });
    });

    return result || [];
}

const buildBreadcrumbItems = (breadcrumbOptions, sepratatorIconType, onClick) => {
    const itemIndexesCount = breadcrumbOptions.length - 1;

    const items = breadcrumbOptions.reduce((accumulator, item, index) => {
        const { description, params, pageAlias, listPageAndBulkUpdateFeatureFlag } = item;
        const url = buildBrowserHistoryUrl(params);
        let breadcrumbLink = {
            title: <Link className={styles.breadCrumbLink} to={url} >{description}</Link>,
            onClick: () => onClick(params, pageAlias, listPageAndBulkUpdateFeatureFlag),
            key: `${description}`
        };
        const breadcrumbSeparator = {
            type: 'separator',
            separator: <Icon type={sepratatorIconType} className={styles.breadCrumbSeparator}/>
        };
        let breadcrumbText = {
            title: <div className={styles.breadCrumbText} key={description} >{description}</div>,
            key: `${description}_separator`
        };

        if (item.isCustom && item.customComponent) {
            breadcrumbLink, breadcrumbText = {
                title: <item.customComponent {...item}/>
            };
        }

        index !== itemIndexesCount
            ? accumulator.push(breadcrumbLink, breadcrumbSeparator)
            : accumulator.push(breadcrumbText);

        return accumulator;
    }, []);

    return items;
};

const BreadCrumb = (props) => {
    const { breadcrumbOptions, sepratatorIconType, onClick } = props;
    const items = buildBreadcrumbItems(breadcrumbOptions, sepratatorIconType, onClick);

    return (
        <Breadcrumb className={styles.breadCrumb} items={items} separator=""/>
    );
};

export default BreadCrumb;