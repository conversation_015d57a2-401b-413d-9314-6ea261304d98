import { Col, Row, Divider } from 'antd';
import React from 'react';
import Icon from '../../icon';
import PropTypes from 'prop-types';

const getArrowClassName = (collapsed) => `timeline-arrow ${collapsed && 'timeline-arrow-collapsed'}`;

export const EntityFormSectionTitle = (props) => {
    const { id, collapsed, toggleExpandCollapseSection, shouldDisplayWarning = false, additionalTitleComponent, title, isSubSectionTitle = false } = props;

    return (
        <div>
            <div className="entityFormSectionTitle">
                {isSubSectionTitle
                    ? <Row type="flex">
                        <Col span={24}>
                            <Divider orientation="left" orientationMargin="0" plain>{title}</Divider>
                        </Col>
                    </Row>
                    : <Row type="flex">
                        <Col span={23}>
                            {
                                collapsed != null && <Icon tabIndex="0" role="button" ariaLabel={title} ariaHidden={false} aria-expanded={!collapsed} type="up" className={getArrowClassName(collapsed)} onClick={toggleExpandCollapseSection} />
                            }
                            <span id={`${id}_label`} role="heading" aria-level="3" className="entitySubHeading">
                                {title}
                            </span>
                            {
                                shouldDisplayWarning && (
                                    <span className="sectionWarning">
                                        <Icon type="warning-triangle-yellow" />
                                    </span>
                                )
                            }
                        </Col>
                        <Col span={1}>
                            {additionalTitleComponent}
                        </Col>
                    </Row>
                }
            </div>
        </div>
    );
};

EntityFormSectionTitle.propTypes = {
    collapsed: PropTypes.bool.isRequired,
    toggleExpandCollapseSection: PropTypes.func.isRequired,
    shouldDisplayWarning: PropTypes.bool,
    additionalTitleComponent: PropTypes.func,
    title: PropTypes.string.isRequired
};