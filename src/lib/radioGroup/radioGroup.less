.jobsPageBaseFilterRow, 
.resourcesPageBaseFilterRow, 
.marketplacePageBaseFilterRow,
.booleanFilterRow,
.rejectReasonRow {
    display: flex !important;
    align-items: center;
    margin: 10px;
    width: 150px;
    max-width: 300px;

    .ant-radio-checked > span.ant-radio-inner {
        border-color: @primary-color !important;
        background-color: @white-color!important;
    }
}

.baseFilterRadioButton {
        display: block !important;
        margin: 0 !important;
    }

.ant-radio-checked .ant-radio-inner::after,
.ant-radio-inner::after
{
    transition: none;
    transform: scale(0.5) !important;
}

.ant-radio-checked::after {
    border-color: transparent
}

.ant-legacy-form-item-control .ant-radio-group > div:first-child {
    margin-top: 0;
}