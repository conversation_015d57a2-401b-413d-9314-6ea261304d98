import React from 'react';
import BaseFilterControl from './baseFilterControl';
import { Row, Col, Button, DatePicker, Popover, InputNumber } from 'antd';
import { Icon } from '../..';
import { isEqual } from 'date-fns';
import PropTypes from 'prop-types';
import { cloneDeep } from 'lodash';
import styles from './dateSensitiveFilter.less';
import {
    startOfDay,
    endOfDay,
    getDisplayShortDateFormat,
    parseToUtcDate,
    subYears,
    addYears,
    addDays,
    dateObjectPropType,
    isSame,
    getCurrentDate,
    formatParsedDate,
    formatToUtcISOString,
    formatLocalDate,
    parseLocalDate
} from '../../../utils/dateUtils';
import { FILTER_SUBTYPES } from '../../../constants/globalConsts';
import { DATE_FORMATS } from '../../../constants';
import { populateStringTemplates } from '../../../utils/translationUtils';

const FLOAT_FIXED_DIGITS_AFTER_DELIMITER = 2;
const INITIAL_FILTER_VALUE = 80;
const INITIAL_HOURS_FILTER_VALUE = 8;

const dateFormat = getDisplayShortDateFormat();
const defaultDateRangeInDays = 7;
const maxDatePickersCount = 5;
const maxDateRangeExceedInYears = 1;

const defaultStartDate = formatToUtcISOString(startOfDay(getCurrentDate()));
const defaultEndDate = formatToUtcISOString(endOfDay(addDays(defaultStartDate, defaultDateRangeInDays)));

const defaultRangePickerDates = {
    startDate: defaultStartDate,
    endDate: defaultEndDate
};

export class DateSensitiveFilter extends BaseFilterControl {
    constructor(props) {
        super(props);

        this.renderAsCustom = true;

        this.addButtonClickListener = this.addButtonClick.bind(this);
        this.removeButtonClickListener = this.removeButtonClick.bind(this);
        this.onApplyListener = this.onFilterApply.bind(this);
        this.inputChangeListener = this.inputChange.bind(this);
        this.pickerChangeRangeListener = this.onPickerChangeRange.bind(this);
    }

    componentDidMount() {
        this.ensureValue();
    }

    componentDidUpdate(prevProps) {
        console.log('componentDidUpdate:', this.props);
        const { visibleDates, filter } = this.props;
        const { startDate, endDate } = visibleDates;


        if (!filter.applied && ((startDate && !isEqual(startDate, prevProps.visibleDates.startDate)) || (endDate && !isEqual(endDate, prevProps.visibleDates.endDate)))) {
            this.valueChangeListener({
                ...this.getFilterValue(),
                parameters: [{ startDate: formatToUtcISOString(startOfDay(startDate)), endDate: formatToUtcISOString(endOfDay(endDate)) }]
            });
        }
    }

    ensureValue() {
        if (!this.props.filter.value)
            this.valueChangeListener(this.getDefaultFilterValue());
    }

    onFilterApply() {
        this.ensureValue();
        this.applyListener();
    }

    onPickerChangeRange(pickerIndex, startDate, endDate) {
        const filterValue = this.getFilterValue();
        const dateRangePickersParams = cloneDeep(filterValue.parameters);

        dateRangePickersParams[pickerIndex] = { startDate, endDate };

        this.valueChangeListener({
            ...filterValue,
            parameters: dateRangePickersParams
        });
    }

    inputChange(value) {
        let persistValue = null;

        if (value !== '' && !isNaN(Number(value)))
            persistValue = Number(value);

        const filterValue = this.getFilterValue();
        const newFilterValue = {
            ...filterValue,
            calcValue: persistValue !== null ? persistValue : filterValue.calcValue
        };

        this.valueChangeListener(newFilterValue);
    }

    addButtonClick() {
        const filterValue = this.getFilterValue();
        const dateRangePickersParams = [...filterValue.parameters];

        if (dateRangePickersParams.length < maxDatePickersCount) {
            dateRangePickersParams.push(defaultRangePickerDates);
        }

        this.valueChangeListener({
            ...filterValue,
            parameters: [...dateRangePickersParams]
        });
    }

    removeButtonClick(index) {
        const filterValue = this.getFilterValue();
        const dateRangePickersParams = [...filterValue.parameters];

        if (dateRangePickersParams.length > 1) {
            dateRangePickersParams.splice(index, 1);
        }

        this.valueChangeListener({
            ...filterValue,
            parameters: [...dateRangePickersParams]
        });
    }

    getFilterValue() {
        const { filter } = this.props;

        return filter.value ? filter.value : this.getDefaultFilterValue();
    }

    getDefaultFilterValue() {
        const { visibleDates = {}, filter } = this.props;
        const { startDate, endDate } = visibleDates;

        return {
            calcValue: (filter.subType == FILTER_SUBTYPES.INPUT_NUMBER_HOURS) ? INITIAL_HOURS_FILTER_VALUE : INITIAL_FILTER_VALUE,
            parameters: [{ startDate: formatToUtcISOString(startOfDay(startDate)), endDate: formatToUtcISOString(endOfDay(endDate)) }]
        };
    }

    formatDateRangesDates(dateRanges) {
        return dateRanges.map(dates => {
            return {
                startDate: dates.startDate,
                endDate: dates.endDate
            };
        });
    }

    getInputNumberControl() {
        const { filter, fieldInfo } = this.props;
        const { calcValue } = this.getFilterValue();
        const { decimalPlace } = fieldInfo;
        const precision = decimalPlace != null ? decimalPlace : FLOAT_FIXED_DIGITS_AFTER_DELIMITER;

        switch (filter.subType) {
            case FILTER_SUBTYPES.INPUT_NUMBER_HOURS: {
                return (
                    <Col span={15} offset={1}>
                        <InputNumber onChange={this.inputChangeListener} value={calcValue} min={0} formatter={value => `${value}`} parser={value => value} />
                        <span className={styles.inputLabel}>hours</span>
                    </Col>
                );
            }
            default: {
                return (<Col span={15} offset={1}>
                    <InputNumber onChange={this.inputChangeListener} value={calcValue} min={0} max={100} precision={precision} formatter={value => `${value}%`} parser={value => value.replace('%', '')} />
                </Col>);
            }
        }
    }

    customRender() {
        const { filter, messages, usePopupContainer = true } = this.props;
        const { maxDateRangeMessage, fromDateLabel, toDateLabel, applyButtonText, addLabel, removeLabel, removeFilterButtonLabel } = messages || {};
        const { parameters: datesParams } = this.getFilterValue();
        const dateRangePickersDates = this.formatDateRangesDates(datesParams);

        return (
            <div className={styles.filterStyles}>

                <Row className={styles.calcValueLabelRow}>
                    <Col>
                        <p className={styles.labelText}>{filter.fieldAlias}</p>
                    </Col>
                </Row>
                <Row>
                    <Col span={8}>
                        {this.renderOperator()}
                    </Col>
                    {this.getInputNumberControl()}
                </Row>
                <Row key="separatorRow_1" className={styles.halfSeparatorRow}></Row>
                <Row key="dateRangePickers">
                    <DateRangePickersArea
                        key="dateRangePickersArea"
                        addButtonClick={this.addButtonClickListener}
                        removeButtonClick={this.removeButtonClickListener}
                        onDateRangeChange={this.pickerChangeRangeListener}
                        datePickers={dateRangePickersDates}
                        usePopupContainer={usePopupContainer}
                        maxDateRangeMessage={maxDateRangeMessage}
                        startDateLabel={fromDateLabel}
                        endDateLabel={toDateLabel}
                        addLabel={addLabel}
                        removeLabel={removeLabel}
                        removeFilterButtonLabel={removeFilterButtonLabel}
                    />
                </Row>
                <Row key="separatorRow_2" className={styles.separatorRow}></Row>
                <Row>
                    <Col span={24}>
                        <Button role="button" aria-label={applyButtonText} type="primary" className={styles.applyButton} onClick={this.onApplyListener} disabled={!filter.canApply}>{applyButtonText}</Button>
                    </Col>
                </Row>
            </div>
        );
    }
}

DateSensitiveFilter.propTypes = {
    filter: PropTypes.object.isRequired
};

export const DateRangePickersArea = (props) => {
    const { datePickers, usePopupContainer, addButtonClick, removeButtonClick, onDateRangeChange, maxDateRangeMessage, startDateLabel, endDateLabel, addLabel, removeLabel, removeFilterButtonLabel } = props;

    const getPickerProps = (datePickerSettings, index) => {
        return {
            startDate: datePickerSettings.startDate,
            endDate: datePickerSettings.endDate,
            showRemoveButton: datePickers.length === 1 ? false : true,
            removeButtonClick,
            onDateRangeChange,
            index,
            startDateLabel,
            endDateLabel,
            removeLabel,
            removeFilterButtonLabel,
            usePopupContainer
        };
    };

    const dateRangePickers = datePickers.map((datePickerSettings, index) => {
        return (
            <DateRangePickerBlock key={"dateRangeRow" + index} {...getPickerProps(datePickerSettings, index)} />
        );
    });

    const addButtonDisabled = datePickers.length === maxDatePickersCount;

    return (
        <div id="dateRangePickersArea">
            {dateRangePickers.length === 0 ? null : dateRangePickers}
            <Row>
                <AddButton ariaLabel={addLabel} disabled={addButtonDisabled} onClick={addButtonClick} />
                <Popover
                    open={datePickers.length === maxDatePickersCount}
                    placement="rightTop"
                    content={maxDateRangeMessage}
                    getPopupContainer={(trigger) => trigger.parentNode}
                />
            </Row>
        </div>
    );
};

DateRangePickersArea.propTypes = {
    addButtonClick: PropTypes.func.isRequired,
    removeButtonClick: PropTypes.func.isRequired,
    onDateRangeChange: PropTypes.func.isRequired,
    datePickers: PropTypes.array.isRequired,
    disabled: PropTypes.bool,
    maxDateRangeMessage: PropTypes.string,
    startDateLabel: PropTypes.string,
    endDateLabel: PropTypes.string,
    addLabel: PropTypes.string,
    removeLabel: PropTypes.string,
    removeFilterButtonLabel: PropTypes.string
};

export const DateRangePickerBlock = (props) => {
    const { index, showRemoveButton, removeButtonClick, startDate, endDate, onDateRangeChange, startDateLabel, endDateLabel, removeFilterButtonLabel, usePopupContainer } = props;
    const placeholderValues = {
        startDate: formatLocalDate(startDate, DATE_FORMATS.DAY_NAME_MONTH_NAME_YEAR),
        endDate: formatLocalDate(endDate, DATE_FORMATS.DAY_NAME_MONTH_NAME_YEAR)
    };
    const { removeFilterButtonLabel: removeDateFilterLabel } = populateStringTemplates({ removeFilterButtonLabel }, placeholderValues || {});

    return [
        <Row key={'dr_separator' + index} className={styles.halfSeparatorRow}></Row>,
        <Row key={'dr_labels_row' + index}>
            <Col span={11}>
                <p className={styles.labelText}>{startDateLabel}</p>
            </Col>
            <Col offset={1} span={12}>
                <p className={styles.labelText}>{endDateLabel}</p>
            </Col>
        </Row>,
        <Row key={'date_range_picker_row_' + index}>
            <Col span={20}>
                <DateRangePicker
                    key={'date_range_picker_' + index}
                    index={index}
                    startDate={startDate}
                    endDate={endDate}
                    onDateRangeChange={onDateRangeChange}
                    usePopupContainer={usePopupContainer}
                />
            </Col>
            {showRemoveButton &&
                <Col span={4}>
                    <RemoveButton ariaLabel={removeDateFilterLabel} onClick={removeButtonClick} index={index} />
                </Col>
            }
        </Row>
    ];
};

DateRangePickerBlock.propTypes = {
    index: PropTypes.number.isRequired,
    removeButtonClick: PropTypes.func.isRequired,
    showRemoveButton: PropTypes.bool,
    startDate: dateObjectPropType.isRequired,
    endDate: dateObjectPropType.isRequired,
    removeLabel: PropTypes.string
};

export class DateRangePicker extends React.Component {
    constructor(props) {
        super(props);

        this.minSelectableDate = null;
        this.maxSelectableDate = null;
        this.firstSelectedDate = null;

        this.disabledDate = this.disabledDate.bind(this);
        this.calculateSelectableDateRange = this.calculateSelectableDateRange.bind(this);
        this.onChange = this.onChange.bind(this);
        this.onOpenChange = this.onOpenChange.bind(this);
        this.getFormattedDate = this.getFormattedDate.bind(this);
    }

    setMinMaxSelectableDates(minDate, maxDate) {
        this.minSelectableDate = minDate;
        this.maxSelectableDate = maxDate;
    }

    disabledDate(current) {
        if (this.minSelectableDate == null || this.maxSelectableDate == null) {
            return false;
        }

        return isSame(this.firstSelectedDate, current) || current > endOfDay(this.maxSelectableDate) || current < endOfDay(this.minSelectableDate);
    }

    calculateSelectableDateRange(dates) {
        let minSelectableDate = null;
        let maxSelectableDate = null;
        let firstSelected = null;

        if (dates) {
            firstSelected = parseToUtcDate(dates[0]);
            minSelectableDate = subYears(dates[0], maxDateRangeExceedInYears);
            maxSelectableDate = addYears(dates[0], maxDateRangeExceedInYears);
        }

        this.firstSelectedDate = firstSelected;
        this.setMinMaxSelectableDates(minSelectableDate, maxSelectableDate);
    }

    onChange(dates, dateStrings) {
        const { index, onDateRangeChange } = this.props;
        // cover case when clearing dates by click cross icon to set default dates
        let newStartDate = defaultRangePickerDates.startDate;
        let newEndDate = defaultRangePickerDates.endDate;

        if ((dateStrings || []).length > 0) {
            newStartDate = formatToUtcISOString(parseLocalDate(dateStrings[0], dateFormat));
            newEndDate = formatToUtcISOString(endOfDay(parseLocalDate(dateStrings[1], dateFormat)));
        }

        onDateRangeChange(index, newStartDate, newEndDate);
    }

    getCalendarContainer(trigger) {
        return trigger;
    }

    onOpenChange(open) {
        if (open) {
            this.firstSelectedDate = null;
            this.setMinMaxSelectableDates(null, null);
        }
    }

    getFormattedDate(dateFormat) {
        return (date) => formatParsedDate(date, dateFormat);
    }

    render() {
        const { startDate, endDate, index, usePopupContainer } = this.props;
        const rangePickerValue = [
            startDate && parseLocalDate(startDate, DATE_FORMATS.YEAR_MONTH_DAY_SLASH),
            endDate && parseLocalDate(endDate, DATE_FORMATS.YEAR_MONTH_DAY_SLASH)
        ];

        const dateIcon = (<Icon type="calendar" />);
        let rangePickerProps = {
            key: 'range_picker_' + index,
            value: rangePickerValue,
            format: this.getFormattedDate(dateFormat),
            disabledDate: this.disabledDate,
            onCalendarChange: this.calculateSelectableDateRange,
            onChange: this.onChange,
            suffixIcon: dateIcon,
            className: styles.datePicker,
            onOpenChange: this.onOpenChange
        };

        if (usePopupContainer) {
            rangePickerProps = {
                ...rangePickerProps,
                getPopupContainer: this.getCalendarContainer
            };
        }

        return (
            <DatePicker.RangePicker {...rangePickerProps} />
        );
    }
}

DateRangePicker.propTypes = {
    startDate: dateObjectPropType.isRequired,
    endDate: dateObjectPropType.isRequired,
    onDateRangeChange: PropTypes.func.isRequired,
    index: PropTypes.number.isRequired
};

export const AddButton = (props) => {
    return (
        <Button role="button" aria-label={props.ariaLabel} disabled={props.disabled} type="secondary" className={styles.addButton} onClick={props.onClick}>
            <Icon type="plus" />
        </Button>
    );
};

AddButton.propTypes = {
    onClick: PropTypes.func.isRequired,
    disabled: PropTypes.bool.isRequired,
    ariaLabel: PropTypes.string
};

export const RemoveButton = (props) => {
    return (
        <Button role="button" aria-label={props.ariaLabel} type="secondary" className={styles.removeButton} onClick={() => props.onClick(props.index)}>
            <Icon type="minus" />
        </Button>
    );
};

RemoveButton.propTypes = {
    index: PropTypes.number.isRequired,
    onClick: PropTypes.func.isRequired,
    ariaLabel: PropTypes.string
};