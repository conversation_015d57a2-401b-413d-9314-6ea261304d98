/* Reuseable grid styles: delocalise and migrate to base styles and ant overrides

- Row highlights
- Icons
- Layout
- Pagination (Jobs management page only)
- Empty states

*/

/* Row highlights */

.ant-table-tbody > tr.ant-table-row-selected td,
.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td:not(.ant-table-row-expand-icon-cell),
.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row) > td,
.ant-table-thead > tr:hover:not(.ant-table-expanded-row) > td:not(.ant-table-row-expand-icon-cell),
.ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
    background: rgba(234, 243, 239, 1) !important;
}

/* Icons */

:local(.dataGridSortIconHidden) {
    visibility: hidden;
}

:local(.dataGridSortIconVisible) {
    visibility: visible;
}

  
  .resuable-data-grid .operation-icons {
      float: left;
      min-width: 2em;
      cursor: pointer;
  }

.ant-table-placeholder .ant-empty-image {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
}

.ant-table-cell .drag-handle {
    color: @primary-color
}

.ant-table-placeholder { 
    border-bottom: 0cm !important;
}

/* Layout */

:local(.dataGridCell) {
    min-width: 11em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:local(.roleGroupDataGridCell) {
    min-width: 3em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:local(.ungroupedRolesRow) {
    min-width: 3em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-bottom: 3px solid #B6BABF !important;
}

.addEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 11em;
}

:local(.dataGridContainer) {
    overflow: hidden;
    height: 100%;

    .ant-table-wrapper th.ant-table-cell-fix-right {
        padding: 8px;

        .anticon-sort-asc,
        .anticon-sort-desc {
            display: none;
        }

        .ant-menu-submenu-vertical .ant-menu-submenu-title {
            display: flex;
            justify-content: center;
            background: @secondary-background;
        }
    }

    .ant-table-expanded-row {
        display: none;
    }
    .table-cell {
        .table-expanded-row-fixed {
            display: none;
        }
    }
    .ant-table-cell-fix-left-last::after {
        transform: translateX(80%);
    }
}

.resuable-data-grid {
    margin: 0 0 1.3rem 0;
}

.resuable-data-grid .ant-legacy-form-item-with-help {
    margin-bottom: -12px;
}

/* Pagination: Jobs management page only */

:local(.paginationRow) {
    position: sticky;
    left: 0;
    border-bottom: 0 !important;
    visibility: hidden;
}

:local(.paginationRow):hover {
    background: @white-color !important;
}

:local(.pagination) {
    display: flex;
    margin-right: -500px;
    visibility: visible;
    padding: 5px 0px;
}

:local(.verticalLoading) {
    height: 50px;
    position: relative;
}

.ant-table-fixed-left .verticalLoadingSpinner {
    visibility: hidden;
}

/* Empty states */

:local(.emptyStatesIcon) {
    font-size: 9em;
    color: @medium-grey;
}

:local(.emptyStatesContainer), :local(.adminNoItemsGrid) {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: @white-color !important;
}

:local(.adminNoItemsGrid) {
    :local(.emptyStatesIcon) svg {
        height: 110px;
        margin-bottom: 15px;
        margin-top: 51px;
    }
}

:local(.emptyStatesHeader) {
    margin: 0.55em;
    color: @dark-grey-color !important;
    font-weight: normal;
}

:local(.emptyStatesContent) {
    color: @dark-grey-color;
}

:local(.emptyStatesJobButton) {
    color: @primary-color;
    font-size: small;
}

:local(.emptyStatesAddButton) {
    width: 15%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, 7%);
    margin-top: 15px !important;
    overflow: hidden;
    text-overflow: clip;
    white-space: nowrap;
    position: sticky;
}

.service-accounts-main-content {
    :local(.emptyStatesAddButton) {
        width: auto;
    }
}

:local(.dataGridNoData) .ant-table-placeholder {
    display: none;
}

:local(.dataGridNoData) .ant-table-header {
    overflow-y: hidden !important;
}

/* Mark delete row */

.mark-delete-row, .mark-delete-row:hover, .deletedRow, .deletedRow:hover {
    background-color: @error-tint !important;
    height: 40px;
}

.mark-delete-row td, .deletedRow td {
    border-bottom: 0px !important;
}

.ant-table-tbody > tr.mark-delete-row:hover:not(.ant-table-expanded-row) > td,
.ant-table-tbody > tr.deletedRow:hover:not(.ant-table-expanded-row) > td {
    background-color: @error-tint !important;
}

.mark-delete-row td, .deletedRow td {
   border: 0px;
   padding-left: 1.3rem;
}

/* Validation */

.mandatory-text span,
.mandatory-text .asterik {
    color: @error-color;
}

.resuable-data-grid .row-has-error {
    border: 2px solid @error-color;
}

/* Misc */
.editable-cell-value-wrap {
    min-width: 1.5rem;
    max-width: 12.5rem;
    width: max-content;
}

.editable-cell-value-wrap.line-through {
    margin-left: 0.55em;
}

.disabled>.anticon {
    color:@dark-grey-color;
}

.cancel-deletion {
    text-decoration: underline;
    color: @primary-color;
    cursor: pointer;
}

.skills-content-area {
    .operations {
        vertical-align: inherit !important; 
      }
}

.cell {
    vertical-align: initial;
}

:local(.cellInEditMode)>.ant-legacy-form-item{
  margin: 0.3rem 0 0 0;
}

.resuable-data-grid {
    .ant-table.ant-table-default {
        .ant-table-body {
            overflow-x: auto;
        }
        .ant-table-fixed-right .mark-for-delete {
            display: none;
        }
    }
}

.reorder-row {
    position:fixed;
    z-index:9999;
    height:0;
    margin-top:-1px;
    border-bottom:solid 5px @primary-color !important;
    display:none;
}

.ant-table-thead {

    .ant-table-column-has-sorters {

        .ant-table-column-sorter-inner { 
            span {
                display: none !important;
            }

            &:after {
                display: block;
                content: ' ';
                background-image: url('./sort-off.svg');
                background-repeat: no-repeat;
                background-position: center;
                background-size: 14px 14px;
                height: 14px;
                width: 14px;
            }
        }
    }

    .ant-table-column-sort {

        .ant-table-column-sorter-inner {
            
            span {
                display: inline !important;

                svg {
                    display: none !important;
                }
            }
        
            span.anticon-caret-up.on, span.anticon-caret-up.active {
                
                &:after {
                    display: block;
                    content: ' ';
                    background-image: url('./sort-asc.svg');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 14px 14px;
                    height: 14px;
                    width: 14px;
                }
            }
        
            span.anticon-caret-down.on, span.anticon-caret-down.active {
                
                &:after {
                    display: block;
                    content: ' ';
                    background-image: url('./sort-desc.svg');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 14px 14px;
                    height: 14px;
                    width: 14px;
                }
            }

            &:after {
                display: none;
            }            
        }
    }
}

.reorderlist-menu .ant-dropdown-menu-item:hover {
    background-color: @primary-color !important;
    color:white !important;
}

.reorderlist-menu .ant-menu-item-selected {
    color:white;
}



.ant-number-input-override.currency-field, .ant-number-input-override.hour-field {
    .ant-input-number{
        width: 100% !important;
    }
}

.color-preview {
    width: 150px;
    height: 35px;
    padding: 2px 8px;
    padding-bottom: 5px;
    border-radius: 5px;
}


.color-preview-light {
    height: 32px;
    padding: 4px 8px;
}

.dataGridHeading { 
    font-weight: bold !important;
    .ant-avatar {
        i {
            height: 32px;
            &.anticon-role {
                line-height: 40px;
            }
            &.anticon-role-group-without-fill {
                height: 26px;
                display: flex;
                align-items: center;
            }
        }
    }
}

:Root .ant-table-tbody > tr > td.ant-table-row-expand-icon-cell{
    padding-left: 1px;
}

.jobsGrid .ant-table-fixed-left .ant-table-fixed, 
.resourcesGrid .ant-table-fixed-left .ant-table-fixed{
    width: 410px
}
.resourcesGrid .ant-table-fixed-left .ant-table-fixed{
    width: 410px
}

.ant-table-tbody > tr.ant-table-row-selected {
    &.ant-table-row-hover {
        .ant-table-selection-column {
            box-shadow: -1px 0 0 @box-shadow-color-2, inset 3px 0 0 0 @secondary-color !important;
            color: gold;
        }
    }
    .ant-table-selection-column {
        box-shadow: -1px 0 0 @box-shadow-color-2, inset 3px 0 0 0 @secondary-color;
    }
}

.jobsGrid, .resourcesGrid {
    .ant-table-cell-fix-left-last::after {
        transform: translateX(80%);
    }

    .ant-table-selection-column.addEllipsis {
    visibility: visible !important;
}

.ant-table-selection-column,
.ant-table-selection-column + td {
    visibility: visible;
}

.ant-table-fixed-columns-in-body.ant-table-selection-column {
    left: 0;
}

.ant-table-fixed-columns-in-body.ant-table-selection-column + td {
    left: 60px;
}

.ant-table-fixed-columns-in-body.ant-table-selection-column,
.ant-table-fixed-columns-in-body.ant-table-selection-column + td {
    visibility: visible !important;
    position: sticky !important;
    z-index: 1;
}

.ant-table-fixed-columns-in-body.ant-table-row-cell-break-word {
   position: sticky !important;
   z-index: 1;
    right: 0;
}

.row-color-odd {

    .ant-table-fixed-columns-in-body.ant-table-selection-column,
    .ant-table-fixed-columns-in-body.ant-table-selection-column + td {
        background: @light-grey-color;
    }
}

    .ant-table-cell-fix-left {
        background: inherit;
    }

    .ant-table-tbody>tr>td:first-child {
        padding-left: 1.286rem !important;
}

    .ant-table-empty{
        .ant-table-body {
            overflow: hidden !important
        }
    }
}

.ant-table-thead > tr > th {
    white-space: nowrap;
}

.roleInboxGrid {
    &.ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:first-child {
        text-align: right;
        padding-left: 0;

        .anticon {
            display: none;
        }
    }

    .ant-table-cell-fix-left-last::after {
        transform: translateX(80%);
    }

    .ant-table-cell-with-append {
        display: flex;
        justify-content: space-between;
        height: 65.994px;
        align-items: center;
        padding-left: 0px !important;
    }

    .ant-table-empty {
        .ant-table-body {
            overflow: hidden !important
        }
    }

    .ant-table-thead > tr > th:nth-child(2) {
        padding: 0px 0px 0px 20px !important
    }

    .ant-table-tbody > tr > td {
        padding: 0.1rem 1.286rem;

        .stickyGridCheckbox {
            margin-left: 15px
        }
    }

    .table-body {
        .scrollYMoved {
            .table-cell-fix-left-last {
                box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
            }
        }
    }

    .table-cell-scrollbar{
        background:@white-color;
        right: -1px;
    }

    .table-cell-fix-right-first {
        z-index: 1;
        background:@white-color;
        box-shadow: inset 3px 0 8px -8px gray;
        min-width: 60px;
    }

    .row-color-odd {
        .table-cell {
            background-color: @light-grey-color;
        }
        .table-cell-fix-left {
            .stickyGridCheckbox{
                float:right;
            }
            min-width: 100px;
            background-color: @light-grey-color;
            z-index: 1
        }
    
        .table-cell-fix-right-first {
            background-color: @light-grey-color;
            right: -1px !important;
        }
    }
    
    .row-color-even {
        .table-cell-fix-left {
            .stickyGridCheckbox{
                float:right;
            }
            min-width: 100px;
            z-index: 1;
            background: inherit;
        }
    
        .table-cell-fix-right-first {
            background: @white-color;
            right: -1px !important;
        }
    }
    
    .table-cell {
            padding: 0.3rem @gutter-width;
            border-bottom: 1px solid #e8e8e8;
    }
    
    .has-no-nested-row {
        .customExpandableRowIcon {
            display: none;
        }
    }

    .table-cell-row-hover {
        background-color: #dce6ed !important;
    }

    .selectedRow > td {
            background-color: #dce6ed !important;
    }

    .selectedRow > .table-cell-fix-left {
        box-shadow: -1px 0 0 @box-shadow-color-2, inset 3px 0 0 0 @secondary-color;
    }

    .selectedRow > .table-cell-fix-left-last {
        box-shadow: none;
    }

    .customExpandableRowIcon {
        margin-left: 10px;

    }

    .nested-row {
        .ant-checkbox-wrapper {
            display: none;
        }
    }

    .table-thead {
        
        .table-cell {
            background: @grey-2;
            color: @heading-color;
            font-weight: @semibold-weight;
            padding: 1rem @gutter-width;
        }
    
        .table-cell-fix-left {
            min-width: 100px;
            z-index: 1;
            background: @grey-2;
            .stickyGridCheckbox{
                float:right;
            }
        }
    }
}

.deletedRow {

}

:local(.prefixIcon) {
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    padding-right: 10px;
}