import React from 'react';
import { <PERSON>u, Dropdown, But<PERSON>, Typography } from 'antd';
import { CommandBarDropDownProps } from '../propTypes';
import styles from './styles.less';
import { PLANNER_ACTIONS } from '../../constants/plannerConsts';
import { COMMAND_BAR_MENUS_COMPONENT_TYPES } from '../../constants/commandBarConsts';
import Icon from '../icon';
import { uniqueId } from 'lodash';
import { getEntityDependantMessage, getSelectionDependantMessage, getActionLabel } from '../../utils/entityStructureUtils';
import { commandBarStringTemplates } from '../../constants/stringTemplateConsts';
import { MenuItemRow } from '../menuItemRow';
import TooltipWrapper from '../tooltipWrapper/tooltipWrapper';
import { getTooltipTitleMessageControl } from '../tooltipTitleMessageControl';
import { getTooltipTitleMessage } from '../../utils/tooltipTitleMessageUtils';
import { ACTIONS_MENU_COMPONENTS } from '../../constants/actionsMenuConsts';

const defaultDropdownButtonClassName = 'defaultDropdownButton';

const { MENU_ITEM, DIVIDER, SUB_MENU, SUB_MENU_WITH_COMPONENT, STATIC_MESSAGE } = ACTIONS_MENU_COMPONENTS;
const { Text } = Typography;

class DropDown extends React.Component {
    constructor(props) {
        super(props);

        this.state = {
            openKeys: []
        };

        this.onAction = this.onAction.bind(this);
        this.onVisibleChange = this.onVisibleChange.bind(this);
        this.onOpenKeysChange = this.onOpenKeysChange.bind(this);
        this.renderMenu = this.renderMenu.bind(this);
    }

    onOpenKeysChange(keys = []) {
        this.setState({ openKeys: keys }, () => this.props.onOpenKeysChange && this.props.onOpenKeysChange(keys));
    }

    onVisibleChange(visible) {
        this.props.setVisibility(visible);
        !visible && this.setState({ openKeys: [] });
    }

    onAction(context, event) {
        const visible = false;
        this.props.setVisibility(visible);
        this.props.onAction(context, event);
        this.onOpenKeysChange();
    }

    getMenuItemKey(itemConfig, itemIndex) {
        const { label, labelPropName, type, componentType } = itemConfig || {};

        const result = `${label || labelPropName}_${componentType || type}`;

        return itemIndex
            ? `${result}_${itemIndex}`
            : result;
    }

    renderMenuItems(config) {
        const { shouldShowItem = () => true } = this.props;
        let menuItems = [];

        (config.items || []).filter(shouldShowItem).map((item, index) => {
            const { label, items, type, scroll, icon } = item;

            if (items && type === SUB_MENU) {
                const className = scroll ? styles.subMenuScroll : '';
                const titleIcon = icon ? <Icon type={icon} /> : null;
                const title = (
                    <>
                        <span className={styles.subMenuItem}>{titleIcon}{label}</span>{' '}
                        <span className="custom-submenu-arrow ant-dropdown-menu-submenu-arrow">
                            <Icon type="right" />
                        </span>
                    </>
                );

                menuItems.push((
                    <Menu.SubMenu className={className} key={this.getMenuItemKey(item, index)} title={title}>
                        {this.renderMenuItems(item)}
                    </Menu.SubMenu>));
            } else if (item.type === SUB_MENU_WITH_COMPONENT) {
                menuItems.push(this.renderSubMenuWithComponent(item, index));
            } else {
                menuItems.push(this.renderMenuItem(item, index));
            }

        });

        return menuItems;
    }

    getActiveStatus(actionMapKey) {
        //currently Filter Toggle option is the only action which supports an active status
        let status = false;
        switch (actionMapKey) {
            case PLANNER_ACTIONS.TOGGLE_FILTER_PANE:
                status = !this.props.actionProps.filterHidden;
                break;

            default:
                break;
        }

        return status;
    }

    renderMenuItem(item, index) {
        const { getEntityInfo, staticMessages } = this.props;
        const { type, label, labelPropName = '', onClickActionType, truncateLabelOptions, offset, icon, className, style, hotKeyDescription, showEllipsis = false, options = {}, functionalAccessName, tooltipProps, actionKey, tooltipMessage, isSelected } = item;
        const { tableName, pluralFormEllipsisSuffix, usePropName = false } = options;

        switch (type) {
            case MENU_ITEM: {
                const { actionProps, getItemDisabled } = this.props;
                const { entitiesIds = [], menuItemsAdditionalProps = {}, multipleTableSelectionActive, selectedEntitiesByTable = {} } = actionProps;

                let selectedEntitiesIds = entitiesIds;

                if (multipleTableSelectionActive && tableName) {
                    selectedEntitiesIds = (selectedEntitiesByTable[tableName] || {}).ids || [];
                }

                const context = { ...item, ...actionProps, actionType: onClickActionType };
                let updatedLabel = usePropName ? staticMessages[labelPropName] : label;
                const rowIcon = options.isPropertyDependant && actionProps[options.valuePropName] ? null : icon;

                let updatedShowEllipsis = showEllipsis;

                if (options.isEntityDependant) {
                    updatedLabel = getEntityDependantMessage(
                        getEntityInfo,
                        commandBarStringTemplates,
                        options,
                        label,
                        {
                            selectedEntitiesCount: selectedEntitiesIds.length
                        }
                    );

                    updatedShowEllipsis = pluralFormEllipsisSuffix ? selectedEntitiesIds.length > 1 : showEllipsis;
                }

                if (options.isSelectionDependant && selectedEntitiesIds.length > 1) {
                    const { multipleSelectionLabelPropName } = options;
                    updatedLabel = getSelectionDependantMessage(staticMessages, commandBarStringTemplates, { multipleSelectionLabelPropName }, label);
                    updatedShowEllipsis = true;
                }

                let itemAdditionalProps = {
                    entitiesIds: selectedEntitiesIds,
                    tableName,
                    functionalAccessName,
                    actionKey
                };

                if (options.useLabelWithCounters && menuItemsAdditionalProps[actionKey]) {
                    const { totalSelectedIdsCount, itemIdsCount } = menuItemsAdditionalProps[actionKey] || {};
                    const displayTotalCount = itemIdsCount !== totalSelectedIdsCount;
                    updatedLabel = getActionLabel(staticMessages, { key: actionKey }, itemIdsCount, totalSelectedIdsCount, displayTotalCount);
                    updatedShowEllipsis = pluralFormEllipsisSuffix ? itemIdsCount > 1 : showEllipsis;
                }

                if (options.canIncreaseRange) {
                    itemAdditionalProps = {
                        ...itemAdditionalProps,
                        canIncreaseRange: options.canIncreaseRange
                    };
                }

                const itemDisabled = (getItemDisabled && getItemDisabled(onClickActionType, itemAdditionalProps)) || context.disabled || false;
                let tooltipText;
                let tooltipWrapperClassName;

                const { tooltipPlacement, showIcon, iconType, tooltipTitleComponentType } = tooltipProps || {};
                if (tooltipProps) {
                    const tooltipMessageText = staticMessages ? getTooltipTitleMessage(actionKey, staticMessages) : tooltipMessage;
                    tooltipText = getTooltipTitleMessageControl(tooltipTitleComponentType, showIcon, iconType, tooltipMessageText);
                    tooltipWrapperClassName = tooltipProps.tooltipClassName;
                }

                if (truncateLabelOptions) {
                    const { charsLimit, tooltipClassName } = truncateLabelOptions;

                    if (label.length >= charsLimit) {
                        tooltipText = label;
                        updatedLabel = label.slice(0, charsLimit);
                        updatedShowEllipsis = true;
                        tooltipWrapperClassName = tooltipClassName;
                    }
                }

                return (
                    <Menu.Item
                        key={this.getMenuItemKey(item, index)}
                        disabled={itemDisabled}
                        offset={offset}
                        onClick={(e) => { this.onAction(context, e); }}
                        className={this.getActiveStatus(item.actionMapKey) ? `${styles.quickActionActive} ` : ''}
                        style={style}
                        aria-current={isSelected}
                    >
                        <TooltipWrapper tooltipText={tooltipText} className={styles[tooltipWrapperClassName]} placement={tooltipPlacement}>
                            <MenuItemRow
                                itemClassName={className}
                                label={updatedLabel}
                                icon={rowIcon}
                                hotKeyDescription={hotKeyDescription}
                                showEllipsis={updatedShowEllipsis}
                                itemDisabled={itemDisabled}
                            />
                        </TooltipWrapper>
                    </Menu.Item>
                );
            }
            case DIVIDER:
                return <Menu.Divider key={type + index} />;
            case STATIC_MESSAGE:
                return <Text>{label}</Text>;
            default:
                return null;
        }
    }

    renderSubMenuWithComponent(config, index) {
        const { actionProps, visible, staticMessages = {} } = this.props;
        const { openKeys = [] } = this.state;
        const { componentType, dropdownBuildComponentProps, iconStyle = {}, useSubMenuIcon = false, labelPropName = '', className = 'subMenu-with-component' } = config;
        const SpecificComponent = actionProps.components[componentType];
        const updatedLabel = staticMessages[labelPropName];

        if (!SpecificComponent) {
            console.error(`Invalid component type: ${componentType}`);

            return null;
        }

        const icon = config.icon ? <Icon style={iconStyle} type={config.icon} /> : null;
        const key = this.getMenuItemKey(config, index);

        let componentProps = {
            ...config,
            isOpened: openKeys.includes(key),
            onAction: this.onAction,
            staticMessages,
            onClickActionType: config.onClickActionType,
            onVisibleChange: this.onVisibleChange,
            actionProps,
            hideWeekends: actionProps.hideWeekends,
            id: 'command-bar',
            key: componentType === COMMAND_BAR_MENUS_COMPONENT_TYPES.DATE_RANGE_PICKER ? uniqueId(config.label) : key + 'SubComponent'
        };

        if (dropdownBuildComponentProps) {
            componentProps = dropdownBuildComponentProps(componentProps);
        }

        const componentTitle = (
            <>
                <span>{config.label || updatedLabel}</span>{' '}
                <span className="custom-submenu-arrow ant-dropdown-menu-submenu-arrow">
                    <Icon type="right" />
                </span>
            </>
        );

        // useSubMenuIcon and expandIcon should be used if we want to display a custom icon for the sub menu and SpecificComponent does not render children
        const subMenuExpandIcon = useSubMenuIcon ? icon : null;
        const specificComponentIcon = !useSubMenuIcon ? icon : null;

        return (
            <Menu.SubMenu key={key} title={componentTitle} popupClassName={className}>
                <SpecificComponent {...componentProps} dropdownOpened={visible}>
                    {config.label || updatedLabel} {specificComponentIcon}
                </SpecificComponent>
            </Menu.SubMenu>
        );
    }

    renderMenu() {
        const { config = {} } = this.props;
        const { label, triggerSubMenuAction, items = [], menuClassName = '', key } = config;
        const { openKeys } = this.state;

        return (
            <Menu
                key={key}
                id={`cb_${label}_menu`}
                triggerSubMenuAction={triggerSubMenuAction}
                openKeys={openKeys}
                onOpenChange={this.onOpenKeysChange}
                style={{ display: items.length ? '' : 'none' }}
                className={menuClassName}
            >
                {this.renderMenuItems(config)}
            </Menu>
        );
    }

    getDefaultDropdownButton() {
        const { config, visible } = this.props;
        const {
            icon,
            label,
            ariaLabel,
            openIcon,
            closedIcon,
            openArrowStyle,
            closedArrowStyle,
            addArrowToDropdownComponent = false,
            openedDropdownComponentStyle = {},
            closedDropdownComponentStyle = {},
            useButtonDropdownComponent = false,
            dropdownLabel,
            dropdownButtonClassName
        } = config;

        const { dropDownComponent } = config;

        const arrowStyle = (visible ? openArrowStyle : closedArrowStyle) || {};

        const arrowIcon = (
            (config.items || []).length
                ? <Icon style={arrowStyle} type={visible ? openIcon : closedIcon} />
                : null
        );

        let result = (
            <button role="link" aria-expanded={visible} aria-label={ariaLabel || label} className="ant-dropdown-link" tabIndex="0">
                {icon && <Icon style={arrowStyle} type={icon} />}
                <span className={styles.itemDropdownLabel}>{label}</span>
                {arrowIcon}
            </button>
        );

        if (dropDownComponent) {
            result = addArrowToDropdownComponent
                ? (
                    <span style={visible ? openedDropdownComponentStyle : closedDropdownComponentStyle}>
                        {dropDownComponent}
                        {arrowIcon}
                    </span>
                ) : dropDownComponent;
        } else if (useButtonDropdownComponent) {
            const iconType = visible ? (openIcon || 'up') : (closedIcon || 'down');
            const buttonClassName = dropdownButtonClassName ? `${defaultDropdownButtonClassName} ${dropdownButtonClassName}` : defaultDropdownButtonClassName;

            result = (
                <Button className={buttonClassName}>
                    {dropdownLabel}
                    <Icon type={iconType} />
                </Button>
            );
        }

        return result;
    }

    getQuickActionDropdownButton() {
        const className = `ant-dropdown-link ${styles.actionElement}`;

        return (
            <button role="link" aria-expanded={this.props.visible} aria-label={this.props.config.ariaLabel || this.props.config.label} className={className}>
                <Icon type={this.props.config.openIcon} />
                {this.props.config.label}
            </button>
        );
    }

    renderDropDown() {
        const { visible, config, disabled, getPopupContainer, destroyPopupOnHide = true, inactiveStatuses = false } = this.props;
        const { dropdownClosedClass, dropdownOpenClass, quickActionDropdown, placement, key, overlayClassName } = config;
        const additionalClassName = visible ? dropdownOpenClass : dropdownClosedClass;

        return (
            <Dropdown
                arrow
                destroyPopupOnHide={destroyPopupOnHide}
                key={key}
                trigger={['click']}
                onOpenChange={this.onVisibleChange}
                dropdownRender={this.renderMenu}
                open={this.props.visible}
                className={`${quickActionDropdown ? styles.cbActionDropdownOpen : styles.cbDropdownOpen} ${additionalClassName || ''}`}
                placement={placement || 'bottomLeft'}
                disabled={disabled || false}
                getPopupContainer={getPopupContainer}
                overlayClassName={overlayClassName}
            >
                {quickActionDropdown && !inactiveStatuses ? this.getQuickActionDropdownButton() : this.getDefaultDropdownButton()}
            </Dropdown>
        );
    }

    render() {
        const dropDown = this.renderDropDown();
        const { config, hidden, rowKey } = this.props;
        const additionalClassName = config.className ? config.className : '';
        const className = `${additionalClassName} ${styles.CommandBarDropDown} ${styles.CommandBarElement} ${this.props.visible ? '__drop-down-open' : ''}`;
        const dropdownId = config.label ? `cb_${config.label}` : rowKey;

        return (
            <div className={className} hidden={hidden} id={dropdownId}>
                {dropDown}
            </div>
        );
    }
}

DropDown.propTypes = CommandBarDropDownProps;

export { DropDown };
