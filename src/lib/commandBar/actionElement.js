import React from 'react';
import { QuickAction } from './quickAction';
import { SwitchViewElement } from './switchViewElement';
import { ActionWithComponent } from './actionWithComponent';
import { CommandBarActionElementProps } from '../propTypes';
import styles from './styles.less';
import { Button } from 'antd';
import { getEntityAlias } from '../../utils/entityStructureUtils';
import { COMMAND_BAR_ACTION_ELEMENT_TYPES } from '../../constants/globalConsts';
import Icon from '../icon';
import { populateStringTemplates } from '../../utils/translationUtils';
import AccessibleTooltip from '../../components/common-components/accessibleTooltip/accessibleTooltip';

const ActionElement = (props) => {

    const renderAction = () => {
        const { config, staticMessages = {} } = props;

        switch (config.type) {
            case COMMAND_BAR_ACTION_ELEMENT_TYPES.QUICK_ACTION:
                return <QuickAction key={config.label} {...props} />;
            case COMMAND_BAR_ACTION_ELEMENT_TYPES.SWITCH_VIEW: {
                const { getEntityInfo, config } = props;

                const options = config.options.map(option => {
                    const { options: aliasOptions = {} } = option;

                    let label = option.label;
                    if (aliasOptions.isEntityDependant) {
                        const entityInfo = getEntityInfo(aliasOptions.tableName);
                        label = getEntityAlias(entityInfo, aliasOptions);
                    }

                    return {
                        ...option,
                        label
                    };
                });

                const radioGroupProps = {
                    ...props,
                    config: {
                        ...config,
                        options
                    }
                };

                return <SwitchViewElement key={config.label} {...radioGroupProps} className={styles.actionElement} ></SwitchViewElement>;
            }
            case COMMAND_BAR_ACTION_ELEMENT_TYPES.BUTTON: {
                const { onAction, actionProps, config, shouldShowItem, getEntityInfo, getItemDisabled } = props;
                const { onClickActionType } = config;
                const disabled = getItemDisabled ? getItemDisabled(onClickActionType, actionProps) : (config.disabled || false);
                const hasAccess = shouldShowItem(config);
                const icon = config.icon ? <Icon type={config.icon} /> : null;
                const { options = {} } = config;

                let staticConfig = {};

                if (options.isEntityDependant) {
                    const entityInfo = getEntityInfo(options.tableName);
                    const resourceEntityAlias = getEntityAlias(entityInfo, options);
                    staticConfig = populateStringTemplates(config, { resourceEntityAlias });
                } else {
                    staticConfig = config;
                }

                return (hasAccess)
                    ?
                    <AccessibleTooltip placement="bottom" title={staticMessages[config.toolTipTextKey] || ''}>
                        <Button {...props} className={styles[config.btnClassName || '']} onClick={() => onAction({ actionType: onClickActionType, ...actionProps })} type={config.buttonType} size={config.size} disabled={disabled}>{icon}{staticConfig.label}</Button>
                    </AccessibleTooltip>
                    : null;
            }
            case COMMAND_BAR_ACTION_ELEMENT_TYPES.ACTION_WITH_COMPONENT:
                return <ActionWithComponent key={config.label} {...props} />;
            default: return null;
        }
    };

    const actionElem = renderAction();

    return (
        <div className={`${styles.CommandBarElement} ${props.config.btnClassName || ''}`}>
            {actionElem}
        </div>
    );

};

ActionElement.propTypes = CommandBarActionElementProps;

export { ActionElement };