:local(.commandBar) {
    display: flex;
    height: @layout-header-height;

    :local(.itemDropdownLabel):hover {
        text-decoration: underline;
    }
    
    :local(.viewJobFilter) {
        :local(.CommandBarElement) {
            &.ant-dropdown-open {
                background: @primary-color!important;
                padding: 10px!important;
                color: #fff;
                border-radius: 8px;
                span.anticon.anticon-up {
                    color: #fff;
                    margin-left: 8px!important;
                }
                .anticon-eyeFilterIcon {
                    svg {
                        path {
                            fill: #fff;
                        }
                    }
                }
            }
        }
        span.anticon.anticon-eyeFilterIcon.common-icon {
            margin-right: 8px!important;
        }
        span.anticon.anticon-down {
            margin-left: 8px!important;
        }
        .jobsPageBaseFilterRow {
            padding: 8px 12px;
            width: 155px;
            margin: 0;
            .ant-radio {
                .ant-radio-inner {
                    border: none;
                    outline: none!important;
                    &::after {
                        content: url('../../lib/icons/check.svg');
                        background-color: transparent!important;
                        transform: none!important;
                    }
                }
                &:hover {
                    border: none;
                }
            }
            span.ant-radio+* {
                padding-inline-end: 0;
            }
        } 
        
    }
}

:local(.roleGroupDetailsPageTitle) {
    display: flex;
    align-items: center;
}

:local(.roleGroupDetailsCommandBarActions) {
    position: absolute;
    align-items: center;
    right: 2.5em;
    display: flex;
    height: 60px;
}

.plannerPage {
    :local(.commandBar) {
        width: calc(100% - 245px);
        :local(.actionsSection) {
            width: 100%;
            position: relative;
            align-items: center;
            .restoreWorkSpaceBtn {
                position: absolute;
                right: 0;
            }
        }
    }
}

:local(.pageTitleSection) {
    align-self: flex-start;
    margin: 0 0 0 1em;
}

:local(.menusSection) {
    display: flex;
    padding: 0 0px;
}

:local(.actionsSection) {
    display: flex;
}

:local(.CommandBarDropDown) {
    min-width: 50px;

    & button:not(.ant-btn).ant-dropdown-open {
        background: @primary-light !important;
    }
}

.ant-table-body {
    :local(.CommandBarDropDown) {
        .ant-dropdown-trigger {
            height: auto;
            &.ant-dropdown-open {
                background: transparent !important;
            }
        }
    }
}

ul[id*=rolefromTemplateLabel] {
    overflow: hidden !important;
}

#dataGrid {
    :local(.CommandBarElement) {
        &.__drop-down-open {
            background-color: transparent;
            .ant-dropdown-link {
                color: @black-color;
            }
        }
    }
}

#cb_Add, .addButtonCB {
    background: @primary-color;
    color: @white-color !important;
    margin: 10px;
    border-radius: 8px;
    min-width: 69.6562px;

    button {
        background: transparent !important;
    }

    button, a, .anticon.anticon-down, .anticon.anticon-up{
        color: @white-color!important;
    }
}

.editListResourceButtonCB {
    background: @secondary-btn-bg;
    color: @dark-color !important;
    border-radius: 8px;
    border: none;
    box-shadow: none;
    font-weight: 500;
    transition: background 0.2s;

    &:hover:not([disabled]) {
        background: @secondary-btn-bg;
        color: @dark-color !important;
    }

    &[disabled],
    &.ant-btn[disabled],
    button[disabled] {
        background: @btn-bg-disabled;
        color: @btn-color-disabled;
        border-color: @btn-border-disabled;
        opacity: 0.7;
        cursor: not-allowed;
    }
}

:local(.CommandBarElement) {
    position: relative;
    display: flex;
    align-items: center;
    & a {
        text-decoration: none;
        display: flex;
        align-items: center;
        height: 100%;
        padding: 0 10px 0px;
        margin: 0px 2px;  /* Counter the positioning AntD does on dropdowns (box shadow) */
        color: @text-color;

        &:hover,
        &:hover span{
            text-decoration: underline;
        }
    }

    & button:not(.ant-btn):not(.ant-switch) {
        background-color: transparent;
        border: none;
        text-decoration: none;
        display: flex;
        align-items: center;
        height: 100%;
        padding: 0 10px 0px;
        margin: 0px 2px;
        cursor: pointer;

        &:focus,
        &.ant-dropdown-open {
            background-color: transparent;
        }

        &.ant-dropdown-link.ant-dropdown-open {
            background: @primary-light;
        }
    }

    & :local(a.quickActionActive) {
        height: 100%;
        background: @light-grey-color;
        color: @primary-color;
        border-color: @grey-color;
        border-right: 1px solid @grey-color;
        border-left: 1px solid @grey-color;
        span, div {
            display: inline-block;
            padding: 0;
            margin: 0;
        }
    }

    // so it appears part of filter panel below
    & :local(a.filterAction) {
        height: calc(100% + 1px);
        border-bottom: 1px solid @light-grey-color;
        color: @primary-10;
        .anticon-filter {
            margin-right: 8px;
        }
    }

    & :local(a.todayAction) {
        display: inline-block;
        line-height: 35px;
        height: 35px;
        border: 1px solid @primary-color;
        border-radius: 3px;
        text-align: center;
        padding: 0;
        margin: 0 24px;
        min-width: 90px;
        color: @primary-color;

        span, div {
            display: inline;
            padding: 0;
            margin: 0;
        }

        // not sure why this is being generated
        .anticon {
            display:none;
        }

        &:hover {
            color: white;
            background-color: @primary-color;
        }
    }

    &.__drop-down-open {
        & .ant-dropdown-link {
            color: @primary-10;
        }
    }

    span.anticon-display-density {
        margin-right: 8px !important;
        font-size: 1.1em;
        transform: translate(0px, 1px);
    }

    span.anticon {
        margin: 0 0.2rem 0 0 !important;

        // Give dropdown arrows a margin on both sides so there is a gap between the text 
        &.anticon-down,
        &.anticon-up { 
            margin: 0 0 0 0.35rem !important;
        }
    }

    .ant-radio-group {
        margin: 0 1rem;
        display: flex;
    }

    &.ant-radio-button-wrapper {
        border: 1px solid @light-grey-color-2;
        box-shadow: none;
    }
    .ant-radio-button-wrapper-checked {
        outline: none !important;
    }

    button[role="switch"] {
        margin-left: 8px;
        margin-right: 8px;
    }

    /* Jobs / Resources toggle */

        // selected option
        .ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled),
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {
            background: @item-hover-bg;
            box-shadow: none;
            color: @text-color;
            border-color: @secondary-color;
            &:hover{
                background: @item-hover-bg;
            }
        }
        .ant-radio-button-wrapper:not(.ant-radio-button-wrapper-disabled):hover {
            background: white;
            text-decoration: underline;
            box-shadow: none;
        }
}

:local(.actionElement) {
    cursor: pointer;
}

.plans-dropdown {
    height: 60px;
}

.plans-dropdown-popup {
    .ant-row {
        display: block;
      }
}

.plans-dropdown,
.reports-dropdown{
    position: absolute; 
    right: 40px;
}

.reports-dropdown {
    height: 60px;

    .ant-dropdown-link {
        &:hover {
            color: @primary-color;
            .anticon {
                color: @primary-color;
            }
        }
        span {
            max-width: 262px;
            white-space: normal;
            .truncate-text();
        }
    }
    .anticon-reports {
        color: @main-text-color;
        margin-right: 10px !important;
    }
    .ant-dropdown-open {
        .anticon-reports {
            color: @primary-color;
        }
    }
}

.report-dropdown {
    .ant-dropdown-menu {
        min-width: 250px;

        .ant-dropdown-menu-submenu {
            ul {
                padding: 4px 0;
            }
        }

        .menuItemIconCol {
            width: unset;
        }
    }

    :local(.reportTooltipWrapper) {
        padding: 0;
        width: 100%;
        display: block;
    }

    .ant-dropdown-menu-submenu-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span:first-child {
            margin-right: 10px;
        }

        .ant-dropdown-menu-submenu-arrow {
            .anticon {
                margin: 0;
                font-size: 15px !important;
            }
        }

        .ant-dropdown-menu-submenu-arrow:not(.custom-submenu-arrow) {
            display: none;
        }
    }
}

:local(.subMenuScroll) {  
    .ant-dropdown-menu-submenu {
        ul  {
            max-height: 500px;
            max-width: 300px;
            min-width: 130px;
            overflow-y: auto;
            overflow-x: hidden;
            li {
                padding-left: 15px;
                height: auto;
                max-height: initial;
                .ant-row-flex {
                    display: inline-block;
                    .menuItemLabelCol {
                        white-space: pre-wrap;
                        overflow-wrap: anywhere;
                        line-height: 22px;
                        display: inline-block;
                    }
                }
            }
        }
    }
    .ant-dropdown-menu-submenu-arrow:not(.custom-submenu-arrow) {
        display: none;
    }
}

.ant-dropdown-menu-submenu {
    .ant-dropdown-menu-sub {
        min-width: 126px;
        max-height: 580px;
        max-width: 300px;
        min-width: 130px;
        overflow-y: auto;
        overflow-x: hidden;
    }
}

.ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow:not(.custom-submenu-arrow){
    display: none;
}

.ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow{ 
    right:0;    /* Lines up the submenu disclosure arrows in View menu with shortcuts */
    .anticon  {
        font-size: 12px;
        color: @grey-1;
        float: right;
        padding-top: 5px;
    }
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title, .ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title {
    padding-inline-end: 5px;
}

.ant-layout-header {
    .ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title {
        padding-inline-end: 12px;
        > span {
            margin-left: 5px;
        }
    }
}

/* menu item container */
.barOptionsDensityRow{
    width: 100%;
    flex-flow: nowrap;
    span+span{
        margin-left:10px;
    }
}

/* Three spans in menu item row
   First and last spans use fit-content
   Middle span uses fill-available to push third span to the right edge */

.barOptionsDensityRadioCol{
    width: fit-content;
    display: none;
}

.barOptionsDensityLabelCol{
    width: -webkit-fill-available;
    width: -moz-available;
    padding-left: 100px;
}

.barOptionsDensityKeyboardShortcutCol {
    text-align: right;
    width: fit-content;
}

.iconLeft {
    width: auto !important;
    font-size: 12px;
    padding-right: 10px;
}

.selectedPlan {
    font-weight: @bold-weight;
    display: list-item;
    list-style-type: disc;
    list-style-position: inside;
}

.selectedReport {
    display: list-item;
    list-style-type: disc;
    list-style-position: inside;
    color: @primary-color;
}

.info {
    color: @light-grey-color-2;
    max-width: 250px;
    white-space: pre-line;
    line-height: initial;
}

.hideWeekendsToggle, .hidePotentialConflicts {
    .menuItemIconCol {
        color: @dark-color;
    }
}

.hideRecordsRow, .hideRecordsSubRow, .hideRecordsRowWithExplanation {
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-width: 350px;
     > .actionExplanation {display: inline-block;}

    .descriptionCol {
        display: flex;
        flex-direction: column;
        width: 55%;
    }

    .toggleCol {
        width: 45%;
        display: flex;
        align-items: center;
        justify-content: end;
        color: @text-dark-color;

        .ant-switch:hover {
            background: @primary-color;
            border-color: @primary-color;
        }

        .ant-switch-checked:hover {
            background: @main-text-color !important;
            border-color: @main-text-color !important;
        }
    }

    .actionExplanation {
        line-height: 16px;
        height: 18px;
        margin-bottom: 14px;
        font-size: 12px;
        color: @light-grey-color-2;
        min-width: 300px;
    }

    .switch {
         margin-left: 5px;
         border-color: @primary-color;
         background-color: @primary-color;
         &:after {
           background-color: @white-color;
         }
         &.ant-switch-checked {
            background-color: @main-text-color;
            border-color: @main-text-color;
            &:after {
              background-color: @white-color;
            }
         }
     }
    
    button {
        background-color: @white-color;
        border-color: @dark-color;
        margin-right: 5px;

        &::after {
            background-color: @dark-color;
        }
    }
}

.hideRecordsRowWithExplanation {
    padding: 5px 0px;
}

.hideRecordsSubRow {
    padding: 8px 0px 10px 15px;
}

.hideRecordsRow {
    padding: 8px 0px;
}

.filtersBadgeCount {
    sup {
        background: @dark-color !important;
        margin-left: 10px;
    }
}

.defaultDropdownButton {
    float: right;
}

:local(.selectionBarTooltipWrapper),
:local(.commandBarTooltipWrapper) {
    margin-right: -10px;
}

:local(.peopleFinderBtnStyle){
    margin-left: 20px;
    display: flex;
    align-items: center;
    span.anticon {
        font-size: 18px;
    }
}

.menuItemRow {
    display: flex;
    align-items: center;
}

.menuItemHotKeyDescriptionCol {
    display: flex;
    align-items: center;
    span {
        margin: 0 !important;
        display: inline-block;
        font-size: 14px;
        color: @heading-color;
        display: flex;
        align-items: center;
        justify-content: center;
        &:nth-child(1),
        &:nth-child(3) {
            background: @grey-2;
            border: 1px solid #E0E0E0;
            max-height: 20px;
            min-width: 20px;
            height: 20px;
            line-height: 2px;
            padding: 0 5px;
        }
        &:nth-child(2) {
            margin: 0 3px !important;
        }
    }
}

.report-dropdown {
    max-width: 330px;
    &.ant-dropdown:after {
        left: auto !important;
        right: 20px;
    }
    .ant-dropdown-menu-item {
        .menuItemRow {
            &.selectedReport {
                .menuItemIconCol {
                    .anticon {
                        color: @primary-color;
                    }
                }
            }
            .menuItemIconCol {
                .anticon {
                    color: @main-text-color;
                }
            }
            .menuItemLabelCol {
                margin-right: 20px;
            }
        }
    }
    .ant-dropdown-menu-submenu {
        &:hover {
            background: transparent !important;

            .anticon,
            .custom-submenu-arrow .anticon {
                color: @primary-color !important;
            }
        }
        ul li.ant-dropdown-menu-item {
            min-height: 32px;
            .ant-row-flex {
                display: inline-flex;
                .menuItemLabelCol {
                    white-space: nowrap;
                }
            }
        }
    }
    .ant-dropdown-menu .ant-dropdown-menu-submenu-open {
        background-color: transparent !important;
        .ant-dropdown-menu-submenu-title {
            color: @white-color !important;
            
            .anticon,
            .custom-submenu-arrow .anticon {
                color: @white-color !important;
            }
        }
    }
}

.truncate-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hide-element {
    display: none;
    visibility: hidden;
    opacity: 0;
}

// Smaller screens less than 1679px)
@media only screen and (max-width: 1679px) {
    .plannerPage {
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 76px;
            }
        }
        :local(.CommandBarElement) {
            &.ant-radio-button-wrapper {
                span.anticon {
                    margin: 0;
                    font-size: 17px;
                }
                :local(.viewElementLabel) {
                    .hide-element();
                }
            }
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                span.anticon {
                    &.anticon-layout,
                    &.anticon-display-density,
                    &.anticon-eye {
                        .hide-element();
                    }
                }
                :local(.itemDropdownLabel) {
                    max-width: 50px;
                    .truncate-text();
                }

                :local(.labelMenuBadge) {
                    .hide-element();
                }
            }
            &.plans-dropdown {
                & a {
                    span {
                        max-width: 200px;
                        .truncate-text();
                    }
                }
            }
        }
        :local(.peopleFinderBtnStyle) {
            span {
                .truncate-text();
                max-width: 56px;
                margin-left: 2px;
                width: 5ch;
            }
        }
        .restoreWorkSpaceBtn {
            span {
                margin-left: 2px;
            }
        }
        .ant-layout-sider-collapsed + .ant-layout {
            .ant-layout-header h3 {
                :local(.pageTitle){
                    max-width: 100px;
                }
            }
            :local(.CommandBarElement) {
                & a {
                    :local(.itemDropdownLabel) {
                        max-width: 56px;
                    }
                }
                &.plans-dropdown {
                    & a {
                        span {
                            max-width: 220px;
                        }
                    }
                }
            }
            :local(.peopleFinderBtnStyle) {
                span {
                    max-width: 60px;
                }
            }
        }
        &.es-lang,
        &.fr-lang {
            :local(.peopleFinderBtnStyle) {
                span {
                    width: 7ch;
                }
            }
        }
    }
    .roleInboxPage,
    .jobsPage {
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 100px;
            }
        }
        :local(.CommandBarElement) {
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                span.anticon.anticon-eye + :local(.itemDropdownLabel) {
                    max-width: 120px;
                }
                :local(.itemDropdownLabel) {
                    max-width: 56px;
                    .truncate-text();
                }
                :local(.labelMenuBadge) {
                    max-width: 56px;
                    .truncate-text();
                }
            }
            & :local(a.quickActionActive) {
                .anticon-filter + span {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}

// Medium screens (1680px  - 1919px)
@media only screen and (min-width: 1680px) and (max-width: 1919px) {
    .plannerPage {
        :local(.commandBar) {
            width: calc(100% - 260px);
        }
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 76px;
            }
        }
        :local(.CommandBarElement) {
            .ant-radio-group {
                max-width: 190px;
            }
            &.ant-radio-button-wrapper {
                span.anticon {
                    .hide-element();
                }
            }
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                :local(.itemDropdownLabel) {
                    max-width: 50px;
                    .truncate-text();
                }
            }
            &.plans-dropdown {
                & a {
                    span {
                        max-width: 200px;
                        .truncate-text();
                    }
                }
            }
        }
        .restoreWorkSpaceBtn,
        :local(.peopleFinderBtnStyle) {
            span {
                .truncate-text();
                max-width: 112px;
                margin-left: 2px;
            }
        }
        .ant-layout-sider-collapsed + .ant-layout {
            .ant-layout-header h3 {
                :local(.pageTitle){
                    max-width: 100px;
                }
            }
            :local(.CommandBarElement) {
                & a {
                    :local(.itemDropdownLabel) {
                        max-width: 56px;
                    }
                }
                &.plans-dropdown {
                    & a {
                        span {
                            max-width: 220px;
                        }
                    }
                }
            }
            :local(.peopleFinderBtnStyle) {
                span {
                    max-width: 120px;
                }
            }
        }
    }
    .roleInboxPage,
    .jobsPage {
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 100px;
            }
        }
        :local(.CommandBarElement) {
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                span.anticon.anticon-eye + :local(.itemDropdownLabel) {
                    max-width: 130px;
                }
                :local(.itemDropdownLabel) {
                    max-width: 56px;
                    .truncate-text();
                }
                :local(.labelMenuBadge) {
                    max-width: 56px;
                    .truncate-text();
                }
            }
            & :local(a.quickActionActive) {
                .anticon-filter + span {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}

// Large screens (1920px and above)
@media only screen and (min-width: 1920px) {
    .plannerPage {
        :local(.commandBar) {
            width: calc(100% - 260px);
        }
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 100px;
            }
        }
        :local(.CommandBarElement) {
            .ant-radio-group {
                max-width: 220px;
            }
            &.ant-radio-button-wrapper {
                span.anticon {
                    .hide-element();
                }
            }
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                :local(.itemDropdownLabel) {
                    max-width: 56px;
                    .truncate-text();
                }
            }
            &.plans-dropdown {
                & a {
                    span {
                        max-width: 220px;
                        .truncate-text();
                    }
                }
            }
        }
        :local(.peopleFinderBtnStyle) {
            span {
                .truncate-text();
                max-width: 120px;
                margin-left: 2px;
            }
        }
        .ant-layout-sider-collapsed + .ant-layout {
            .ant-layout-header h3 {
                :local(.pageTitle){
                    max-width: 120px;
                }
            }
            :local(.CommandBarElement) {
                .ant-radio-group {
                    max-width: 240px;
                }
                & a {
                    :local(.itemDropdownLabel) {
                        max-width: 64px;
                    }
                }
                &.plans-dropdown {
                    & a {
                        span {
                            max-width: 240px;
                        }
                    }
                }
            }
            :local(.peopleFinderBtnStyle) {
                span {
                    max-width: 140px;
                }
            }
        }
    }
    .roleInboxPage,
    .jobsPage {
        .ant-layout-header h3 {
            display: flex;
            align-items: center;
            :local(.pageTitle){
                .truncate-text();
                max-width: 120px;
            }
        }
        :local(.CommandBarElement) {
            & a {
                .filtersBadgeCount sup {
                    margin-left: 8px;
                }
                span.anticon.anticon-eye + :local(.itemDropdownLabel) {
                    max-width: 140px;
                }
                :local(.itemDropdownLabel) {
                    max-width: 64px;
                    .truncate-text();
                }
                :local(.labelMenuBadge) {
                    max-width: 64px;
                    .truncate-text();
                }
            }
            & :local(a.quickActionActive) {
                .anticon-filter + span {
                    display: flex;
                    align-items: center;
                }
            }
        }
    }
}

.addMenuListPage {
    .ant-dropdown {
        min-width: 200px !important;

        .ant-typography {
            display: flex;
            line-height: 1.5;
            padding: 5px 16px;
            color: @text-color-secondary;
        }

    }
}

.listPage {
    .jobs-workspaces-dropdown {
        height: 35px;
        margin-top: 13px;
        border-radius: 8px;

        button:not(.ant-btn):not(.ant-switch).ant-dropdown-link  {
            border-radius: 8px;

            &.ant-dropdown-open {
                background: @primary-color !important;
                color: @white-color;

                .anticon  {
                    color: @white-color;
                }
            }
        }
    }
}