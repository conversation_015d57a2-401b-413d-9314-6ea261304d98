import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from 'antd';
import './reviewSection.less';
import { useSelector } from 'react-redux';
import { getEntityWindowReviewSectionsSelector } from '../../selectors/entityWindowSelectors';

/**
 * Content for the Reviews section.
 *
 * @param {Object} props
 * @param {number} props.reviewCount
 * @param {Function} props.onSubmitReview
 */
const ReviewSection = ({
    reviewCount,
    onSubmitReview
}) => {
    // Use selector to get translated labels from redux state
    const reviewSection = useSelector(getEntityWindowReviewSectionsSelector);

    return (
        <div className="review-section">
            <div className="review-section__row">
                <Typography.Text className="ant-col ant-col-8 review-section__label">
                    {reviewSection.skillReviewLabel}
                </Typography.Text>

                <div className="ant-col ant-col-12">
                    <Typography.Text className="review-section__count">
                        {reviewSection.resourceReviewedLabel.replace('{0}', reviewCount)}
                    </Typography.Text>
                    <br />
                    <div>
                        <Typography.Link
                            className="review-section__submit"
                            onClick={onSubmitReview}
                        >
                            {reviewSection.submitReviewButtonLabel}
                        </Typography.Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

ReviewSection.propTypes = {
    reviewCount: PropTypes.number.isRequired,
    onSubmitReview: PropTypes.func.isRequired
};

export default ReviewSection;
