.review-section {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 4px 12px;
}

.review-section__row {
  display: flex;
}

.review-section__label {
  color: #8c8fa3;
  font-size: 14px;
}

.review-section__count {
  color: #222e3a;
  font-weight: 500;
  font-size: 15px;
}

a.review-section__submit {
  font-weight: 500;
  font-size: 15px;
  padding: 0;
  margin-top: 3px;
  color: #018048;
  text-decoration: underline;
}

.delete-reviews {
  color: red;
}

.review-settings-radio-button {
  display: flex;
  gap: 8px;
  flex-direction: column;
  margin-top: 6px;
}

.clear-skills-review-button {
  margin-left: 330px;
}

.reviewer-dropdown {
  padding-left: 100px;
}

.reviews-radio-button {
  margin-left: 109px;
}
