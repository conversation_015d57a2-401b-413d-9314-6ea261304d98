import { PLANNER_PAGE_ALIAS } from '../constants/plannerConsts';
import { JOBS_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE, ROLE_GROUP_DETAILS_PAGE } from '../constants/jobsPageConsts';
import { PROFILE_PAGE_ALIAS } from '../constants/talentProfileConsts';
import { ROLE_INBOX_PAGE_ALIAS } from '../constants/roleInboxPageConsts';
import { TIMESHEETS_PAGE_ALIAS } from '../constants/timeSheetsPageConsts';
import * as actionTypes from './actionTypes';
import { NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../constants/tableViewPageConsts';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';

const { PAGE_ACTIONS: PA } = actionTypes;

export function navBarPageChange(page, subPage) {
  return { type: actionTypes.NAV_BAR_PAGE_CHANGE, page, subPage }
}

export function navBarCollapse() {
  return { type: actionTypes.NAV_BAR_COLLAPSE };
}

export const populateApplicationPages = (pages) => {
  return {
    type: actionTypes.POPULATE_APPLICATION_PAGES,
    pages
  }
};

export const populateApplicationPagesError = () => {
  return {
    type: actionTypes.POPULATE_APPLICATION_PAGES,
    pages: []
  }
}

export const loadApplicationPages = (followUpAction) => {
  return {
    type: actionTypes.LOAD_APPLICATION_PAGES,
    payload: { followUpAction }
  };
};

export const loadApplicationPagesSuccessful = (alias, payload, pages) => {
  return {
    type: actionTypes.LOAD_APPLICATION_PAGES_SUCCESSFUL,
    payload: {
      ...payload,
      pages
    }
  };
};

export const pushUrl = (newParams, page = '', subPageParentPage) => {
    return {
        type: actionTypes.PUSH_APPLICATION_URL,
        payload: {
            page,
            newParams,
            subPageParentPage
        }
    };
};

export const pushLocation = (location) => {
    return {
        type: actionTypes.PUSH_APPLICATION_LOCATION,
        payload: {
          location
        }
    };
};

export const replaceUrl = (newParams) => {
    return {
        type: actionTypes.REPLACE_APPLICATION_URL,
        payload: {
            newParams
        }
    };
};

export const navigateToParentPage = () => {
  return {
    type: actionTypes.NAVIGATE_TO_PARENT_PAGE,
    payload: {
      
    }
  }
}

export const PAGE_ACTIONS = {
  OPEN: {
    [PROFILE_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[PROFILE_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [NOTIFICATIONS_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[NOTIFICATIONS_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [PLANNER_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[PLANNER_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [ROLE_INBOX_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[ROLE_INBOX_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [TIMESHEETS_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[TIMESHEETS_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [JOBS_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[JOBS_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [ROLE_GROUP_LIST_PAGE]: (location) => {
      return {
        type: PA.OPEN[ROLE_GROUP_LIST_PAGE],
        payload: {
          location
        }
      }
    },
    [ROLE_GROUP_DETAILS_PAGE]: (location, page, addNewEntity = false, additionalProps = {}) => {
      return {
        type: PA.OPEN[ROLE_GROUP_DETAILS_PAGE],
        payload: {
          location,
          addNewEntity,
          additionalProps
        }
      }
    },
    [MARKETPLACE_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[MARKETPLACE_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [PREVIEW_ENTITY_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[PREVIEW_ENTITY_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [TABLE_VIEW_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[TABLE_VIEW_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
    [RESOURCES_PAGE_ALIAS]: (location) => {
      return {
        type: PA.OPEN[RESOURCES_PAGE_ALIAS],
        payload: {
          location
        }
      }
    },
  },
  LOAD: {
    [PROFILE_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[PROFILE_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [NOTIFICATIONS_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[NOTIFICATIONS_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [PLANNER_PAGE_ALIAS]: (loadStrategy, previousPageState = {}) => {
      return {
        type: PA.LOAD[PLANNER_PAGE_ALIAS],
        payload: {
          loadStrategy,
          previousPageState
        }
      };
    },
    [ROLE_INBOX_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[ROLE_INBOX_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [TIMESHEETS_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[TIMESHEETS_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [JOBS_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[JOBS_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      };
    },
    [ROLE_GROUP_LIST_PAGE]: (loadStrategy) => {
      return {
        type: PA.LOAD[ROLE_GROUP_LIST_PAGE],
        payload: {
          loadStrategy
        }
      };
    },
    [ROLE_GROUP_DETAILS_PAGE]: (loadStrategy, addNewEntity = false, additionalProps = {}) => {
      return {
        type: PA.LOAD[ROLE_GROUP_DETAILS_PAGE],
        payload: {
          loadStrategy,
          addNewEntity,
          additionalProps
        }
      };
    },
    [MARKETPLACE_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[MARKETPLACE_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [PREVIEW_ENTITY_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[PREVIEW_ENTITY_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [TABLE_VIEW_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[TABLE_VIEW_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      }
    },
    [RESOURCES_PAGE_ALIAS]: (loadStrategy) => {
      return {
        type: PA.LOAD[RESOURCES_PAGE_ALIAS],
        payload: {
          loadStrategy
        }
      };
    },
  },
  CLOSE: {
    [ROLE_GROUP_LIST_PAGE]: () => {
      return {
        type: PA.CLOSE[ROLE_GROUP_LIST_PAGE]
      };
    },
    [ROLE_GROUP_DETAILS_PAGE]: () => {
      return {
        type: PA.CLOSE[ROLE_GROUP_DETAILS_PAGE]
      };
    }
  }
};