import { COMMAND_BAR_WORKSPACES_SECTION, JOBS_COMMAND_BAR, LIST_PAGE_ACTIONS } from "./actionTypes";

export function updateListView(payload) {
    return {
        type: LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW,
        payload
    };
}

// List page command bar actions
export function commandBarSetSectionVisibility(sectionKey, visible) {
    return {
        type: JOBS_COMMAND_BAR.SET_SECTION_VISIBILITY,
        payload: {
            sectionKey,
            visible
        }
    };
}

export function commandBarPopulateWorkspacesSection(workspaces) {
    return {
        type: COMMAND_BAR_WORKSPACES_SECTION.POPULATE,
        payload: {
            workspaces,
            selectedWorkspaceGuid: workspaces?.selected
        }
    }
}