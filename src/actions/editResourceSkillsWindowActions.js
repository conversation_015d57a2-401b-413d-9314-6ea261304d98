import { EDIT_RESOURCE_SKILLS_WINDOW } from './actionTypes';

export const editResourceSkillsWindowOpen = (entityId) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.OPEN,
        payload: {
            entityId
        }
    };
};

export const editResourceSkillsWindowClose = () => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.CLOSE
    };
};

export const editResourceSkillsChangeTab = (tabIndex) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.CHANGE_TAB,
        payload:{
            tabIndex
        }
    };
};

export const editResourceSkillsWindowBuildSections = (sections) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.BUILD_SECTIONS,
        payload: {
            sections
        }
    };
};

export const editResourceSkillsWindowAddSkills = (entityId, sectionId, skillIds, skillInfos) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS,
        payload: {
            entityId,
            sectionId,
            skillIds,
            skillInfos
        }
    };
};

export const editResourceSkillsWindowUpdateSkills = (entityId) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.UPDATE.SKILLS,
        payload: {
            entityId
        }
    };
};

export const editResourceSkillsWindowUpdateSkillsSuccess = () => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.UPDATE.SKILLS_SUCCESS
    };
};

//Action creator to resend an approval request to the manager.
export const resendApprovalRequestToManager = (resourceId) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.RESEND_APPROVAL_REQUEST,
        payload: {
            resourceId
        }
    }
}

export const editResourceSkillsWindowDiscardChanges = () => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.UI.DISCARD_CHANGES
    };
};

export const editResourceSkillsWindowLoadAutoCompleteSkills = (searchTerm, maxResults, filterByResourcePermission) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.AUTOCOMPLETE.SEARCH_SKILLS,
        payload: {
            searchTerm,
            maxResults,
            filterByResourcePermission
        }
    };
};

export const editResourceSkillsWindowLoadAutoCompleteSkillsSuccess = (alias, { searchTerm }, skills) => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.AUTOCOMPLETE.SEARCH_SKILLS_SUCCESS,
        payload: {
            searchTerm,
            skills
        }
    };
};

export const editResourceSkillsWindowLoadAutoCompleteSkillsError = () => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.AUTOCOMPLETE.SEARCH_SKILLS_ERROR
    };
};

export const editResourceSkillsWindowClearAutoCompleteSkills = () => {
    return {
        type: EDIT_RESOURCE_SKILLS_WINDOW.AUTOCOMPLETE.REMOVE_SKILLS
    };
};