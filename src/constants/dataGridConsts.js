import { ROLEREQUEST_FIELDS } from "./fieldConsts";
import { ROLEREQUEST_DISPLAY_FIELD } from "./rolesConsts";

export const DATA_GRID_PAGED_DATA_SUFFIX = 'paged_data';
export const DATA_GRID_TABLE_DATAS_SUFFIX = 'table_datas';
export const DATA_GRID_FILTERS_SUFFIX = 'filters';
export const DATA_GRID_NO_ITEMS_STATE = 'noItemsState';
export const DATA_GRID_NO_MATCHING_ITEMS_STATE = 'noMatchingItemsState';
export const DATA_GRID_EMPTY_GRID_STATE = 'emptyGridState';
export const DATA_GRID_EMPTY_ROLE_LIST_PAGE_STATE = 'emptyGridRoleListPage';
export const DATA_GRID_INITIAL_PAGE_NUMBER = 1;
export const DATA_GRID_INITIAL_PAGE_SIZE = 20;
export const DATA_GRID_PAGE_SIZES_OPTIONS = [10, 20, 30];

export const DATA_GRID_DENSITY_KEYS = {
    COMPACT: 'compact',
    DEFAULT: 'default',
    EXPANDED: 'expanded'
};

export const POTENTIAL_CONFLICTS_INDICATOR_SIZES = {
    SMALL: 20,
    BIG: 40
};

export const DATA_GRID_DENSITY_OPTIONS = {
    SMALL: 35,
    MEDIUM: 57,
    BIG: 65
};

export const STYLE_SETTINGS_KEYS = {
    ROW_HEIGHT: 'row',
    BARS_ROWS_MARGIN: 'barsRowsMargin',
    BAR_HEIGHT: 'bookingBar'// refactor?
};

// TODO: Refactor for proper entity aliases in messages, this will include refactoring for jobs page grid and rolegrouplist grid
export const DATA_GRID_MODULE_CONSTS = {
    jobsPage: {
        DATA_GRID_NO_ITEMS_ICON: 'job',
        DATA_GRID_NO_ITEMS_MESSAGE: `##key##noItemsMessage###You don't have any jobs`,
        DATA_GRID_NO_ITEMS_BUTTON_LABEL: '##key##newJob###New job',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noMatchingItemsMessage###No jobs found matching the filters',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##noMatchingItemsContent###Try changing your filters.'
    },
    resourcesPage: {
        DATA_GRID_NO_ITEMS_ICON: 'user-filled',
        DATA_GRID_NO_ITEMS_MESSAGE: `##key##noResourcesItemsMessage###You don't have any resources`,
        DATA_GRID_NO_ITEMS_BUTTON_LABEL: '##key##newResource###New resource',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noMatchingResourceItemsMessage###No resources found matching your filters',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##noMatchingResourceItemsContentMessage###Try changing your filters or view settings'
    },
    roleInboxPage: {
        DATA_GRID_NO_ITEMS_ICON: 'role',
        DATA_GRID_NO_ITEMS_MESSAGE: `##key##noRolesItemsMessage###You don't have any roles`,
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noMatchingRolesItemsMessage###No roles found matching the filters',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##noMatchingItemsContent###Try changing your filters.'
    },
    rolegroupListPage: {
        DATA_GRID_NO_ITEMS_MESSAGE: '##key##noRoleGroupItemsMessage###No scenarios',
        DATA_GRID_NO_ITEMS_CONTENT: '##key##noRoleGroupItemsContent###Create and compare scenarios for this job',
        DATA_GRID_NO_ITEMS_BUTTON_LABEL: '##key##newRoleGroup###New role group'
    },
    peopleFinderDialog: {
        DATA_GRID_NO_ITEMS_ICON: 'user-outlined',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noResourceFoundMessage###No resources found matching the criteria',
        DATA_GRID_ADDITIONAL_STYLE: { height: 'fit-content' }
    },
    marketplacePage: {
        DATA_GRID_NO_ITEMS_ICON: 'megaphone',
        DATA_GRID_NO_ITEMS_MESSAGE: '##key##noMarketplaceRolesPublished###There aren\'t any roles published',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noMatchingRolesItemsMessage###No roles found matching the filters.',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##noMatchingItemsContent###Try changing your filters.'
    },
    jobFilterDialog: {
        DATA_GRID_NO_ITEMS_ICON: 'job',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noResultsMessage###No results found',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##tryAdjustingFiltersMessage###Try adjusting your search or filter',
        DATA_GRID_ADDITIONAL_STYLE: { transform: 'translateY(50%)' }
    },
    massDuplicateJobs: {
        DATA_GRID_NO_ITEMS_ICON: 'job',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##noResultsMessage###No results found',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##tryAdjustingFiltersMessage###Try adjusting your search or filter',
        DATA_GRID_ADDITIONAL_STYLE: { transform: 'translateY(50%)' }
    },
    operationsLogDialog: {
        DATA_GRID_NO_ITEMS_ICON: 'empty-accounts-grid',
        DATA_GRID_NO_MATCHING_ITEMS_MESSAGE: '##key##operationLogEmptyMessage###Operation log is empty',
        DATA_GRID_NO_MATCHING_ITEMS_CONTENT: '##key##operationLogEmptyContent###This log is currently empty with no recorded operations.',
        DATA_GRID_ADDITIONAL_STYLE: { transform: 'translateY(50%)' }
    }
};

export const DATA_GRID_FIELDS_BLANK_VALUES = {
    jobsPage: {
        job_budget: '0.00'
    },
    resourcesPage: {},
    rolegroupListPage: {
        rolerequestgroup_totalcost: '0.00',
        rolerequestgroup_totalrevenue: '0.00',
        rolerequestgroup_totalprofit: '0.00',
        rolerequestgroup_totalroles: '0',
        rolerequestgroup_totalcount: '0'
    },
    roleInboxPage: {
        [ROLEREQUEST_FIELDS.RESOURCE_GUID]: 'Unassigned',
        [ROLEREQUEST_FIELDS.DESCRIPTION]: '${rolerequestSingularCapitalAlias} by ${resourceSingularLowerAlias} name'
    },
    marketplacePage: {
        [ROLEREQUEST_FIELDS.DESCRIPTION]: 'No ${rolerequestSingularLowerAlias} description set',
        [ROLEREQUEST_FIELDS.REQUIREMENTS_TAGS]: 'No ${rolerequestSingularLowerAlias} requirements set'
    }
};

export const DATA_GRID_ROW_SELECTED_CLASS_NAME = 'ant-table-row-selected';
export const DATA_GRID_ROW_HOVER_CLASS_NAME = 'ant-table-row-hover';
export const FIXED_COLUMN_MIN_WIDTH = 420;

export const CUSTOM_DATAGRID_FIELDS = [ROLEREQUEST_DISPLAY_FIELD];

export const SCROLL_TO_BOTTOM_THRESHOLD = 40;

export const PAGINATION_TYPE = {
    VERTICAL_SCROLL: 'VERTICAL_SCROLL',
    MANUAL_PICK: 'MANUAL_PICK'
};

export const JOBS_FILTER_DATA_GRID_COLUMN_WIDTH = 160;
export const JOBS_FILTER_DATA_GRID_FIXED_COLUMN_WIDTH = 280;
export const JOBS_FILTER_SCROLL_Y_HEIGHT = 400;

export const DEFAULT_MAX_RECORDS = 0;

export const DATA_GRID_IDS = {
    WORK_HISTORY_GRID_ID: 'workHistoryGrid',
    REJECT_ROLE_GRID_ID: 'rejectRoleGrid',
    PROGRESS_ROLE_GRID_ID: 'progressRolesGrid',
    PEOPLE_FINDER_GRID_ID: 'peopleFinderGrid',
    OPERATIONS_LOG_GRID_ID: 'operationsLogGrid',
    JOB_FILTER_GRID_ID: 'jobFilterGrid',
    CONNECTED_DATA_GRID_ID: 'connectedDataGrid',
    ROLE_INBOX_GRID_ID: 'roleInboxGrid',
    ROLE_GROUP_LIST_GRID_ID: 'roleGroupListGrid',
    SKILLS_GRID_ID: 'skillsGrid',
    FUNCTIONAL_ACCESS_GRID_ID: 'functionalAccessGrid',
    ENTITY_ACCESS_GRID_ID: 'entityAccessGrid',
    SERVICE_ACCOUNTS_GRID_ID: 'serviceAccountsGrid'
};