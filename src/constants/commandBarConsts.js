import { COMMAND_BAR_ACTION_ELEMENT_TYPES } from "./globalConsts";

const COMMAND_BAR_MENUS_COMPONENT_TYPES = {
    CUSTOM_DATE_RANGE_PICKER: 'CustomDateRangePicker',
    BAR_OPTIONS: 'BarOptions',
    DATE_RANGE_PICKER: 'DateRangePicker',
    GO_TO_DATE: 'GoToDate',
    SWITCH: 'Switch',
    HIDE_RECORDS_OPTIONS: 'HideRecordsOptions',
    RADIO_GROUP_ELEMENT: 'RadioGroupElement',
    DIVIDER: 'Divider',
    MENU_ITEM: 'MenuItem',
    MENU: 'Menu',
    ROLE_FROM_TEMPLATE: 'RoleFromTemplate',
    STATIC_MESSAGE: 'StaticMessage',
};

const COMMAND_BAR_MENUS_SECTION_KEYS = {
    PLANS: 'plansSection',
    ADD: 'addSection',
    EDIT: 'editSection',
    VIEW: 'viewSection'
};

const COMMAND_BAR_QUICK_ACTION_KEYS = {
    FILTERS: 'filters',
    TODAY: 'today',
    DISPLAY_DENSITY: 'displayDensity',
    BASE_FILTER: 'baseFilter'
};

const COMMAND_BAR_PROP_KEY_TOGGLE_VALUE = {
    HIDE_DRAFT_ROLES: 'hideDraftRolesRecords',
    HIDE_REQUESTED_ROLES: 'hideRequestedRolesRecords',
    HIDE_UNASSIGNED_ROLES: 'hideUnassignedRolesRecords',
    HIDE_ROLES_BY_NAME: 'hideRolesByNameRecords',
    HIDE_ROLES_BY_CRITERIA: 'hideRolesByCriteriaRecords',
    HIDE_LIVE_BARS: 'hideLiveBars'
};

const commonBaseViewRolesFiltersItemProps = {
    type: COMMAND_BAR_ACTION_ELEMENT_TYPES.MENU_WITH_COMPONENT,
    componentType: COMMAND_BAR_MENUS_COMPONENT_TYPES.SWITCH,
    size: 'small',
    className: 'hideRecordsRow',
    visible: true
};

const COMMAND_BAR_CUSTOM_CONDITION_TYPES = {
    HIDE_EDIT_BOOKINGS_ACTION: 'hideEditBookingsActions',
    HIDE_EDIT_ROLE_BY_TYPE_ACTION: 'hideEditRoleByTypeAction',
    NO_ROLES_SELECTED: 'noRolesSelected',
    MULTIPLE_ROLES_SELECTED: 'multipleRolesSelected',
    HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROW_GRID_SELECTION: 'hideBarCreateOptionsOnCriteriaRowGridSelection',
    HIDE_EDIT_DIVIDERS: 'hideEditDividers',
    HIDE_BAR_CREATE_OPTIONS_ON_RESOURCE_ROW_GRID_SELECTION: 'hideBarCreateOptionsOnResourceRowGridSelection',
    HIDE_BAR_CREATE_OPTIONS_ON_CRITERIA_ROLE_BAR_SELECTION: 'hideBarCreateOptionsOnCriteriaRoleBarSelection',
    HIDE_CRITERIA_ROLE_CREATE_OPTIONS_ON_BAR_SELECTION: 'hideCriteriaRoleCreateOptionsOnBarSelection'
};

export {
    COMMAND_BAR_PROP_KEY_TOGGLE_VALUE,
    COMMAND_BAR_MENUS_SECTION_KEYS,
    COMMAND_BAR_QUICK_ACTION_KEYS,
    COMMAND_BAR_MENUS_COMPONENT_TYPES,
    commonBaseViewRolesFiltersItemProps,
    COMMAND_BAR_CUSTOM_CONDITION_TYPES
};