import { ActionsObservable } from 'redux-observable';
import { amendedState } from './testData';
import * as actionTypes from '../../../src/actions/actionTypes';
import * as _ from 'lodash';

import 'rxjs/add/observable/of';
import 'rxjs/add/operator/toArray';
import apisMock from '../apisMock';

import {
  createLoadPlannerDataEpic,
  createLoadPagePlannerDataEpic,
  createRefreshPlannerDataEpic,
  createUpdatePlannerDataEpic,
  updatePlannerDateSensitiveFieldsEpic,
  createPlannerPageFieldsChangedInterceptor,
  createClearPlannerSelectEditsEpic,
  createViewSettingsChangeEpic,
  commonDataBatchInterceptorEpic,
  barInsertGetDerivedActionDataFn,
  batchUpdateRoleRequestsStatusEpic$,
  entityWindowJobInsertSuccessInterceptorEpic,
} from '../../../src/epics/plannerDataEpics';

import { getSelectedWorkspaceSettings } from '../../../src/selectors/workspaceSelectors';
import * as plannerQuerySelectionUtils from '../../../src/utils/plannerQuerySelectionUtils';
import { outputActionsContainsType } from '../../testUtils';
import { SUCCESS_STATUS, ERROR_STATUS } from '../../../src/constants';
import { plannerDataScrolled } from '../../../src/actions/plannerDataActions';

const workspaceSettings = getSelectedWorkspaceSettings(
  amendedState.value.plannerPage.workspaces
);
const {
  plannerDataGuid,
  pagedMasterRecPlannerDataGuid,
  subRecPlannerDataGuid,
  bookingGroupsGuid,
  workspace_guid,
  masterRecTableName,
  startDate,
  endDate,
} = workspaceSettings;

const createLoadPagedResultsDataAction$ = ActionsObservable.of({
  type: 'LOAD_PLANNER_DATA',
  payload: { pageSize: 20, workspaceGuid: workspace_guid },
});

const createSortPagedResultDataAction$ = ActionsObservable.of({
  type: 'SORT_PLANNER_DATA',
  payload: {
    workspaceGuid: workspace_guid,
  },
});

const createScrollPagedResultDataAction$ = ActionsObservable.of({
  type: 'SCROLL_PLANNER_DATA',
  payload: {
    workspaceGuid: workspace_guid,
    gridRowsCount: 21,
    pagedDataCount: 20,
    plannerDataGuid,
    pagedMasterRecPlannerDataGuid,
    masterRecTableName,
  },
});

const createRefreshPagedResultDataAction$ = ActionsObservable.of({
  type: 'DATE_RANGE_CHANGED',
  payload: {
    workspaceGuid: workspace_guid,
    startDate,
    endDate,
  },
});

const createUpdatePagedResultDataAction$ = ActionsObservable.of({
  type: 'UPDATE_PLANNER_DATA',
  payload: {
    response: true,
    tableDataGuid: pagedMasterRecPlannerDataGuid,
    tableName: 'booking',
    tableDataEntryGuid: '0e247f5f-2d4a-475f-8ddb-33789e923897',
    tableData: {
      booking_resource_guid: null,
      booking_job_guid: 'c5ceb77b-cace-47e4-a8a2-8bccaf24a728',
      booking_start: '2020-04-28T23:00:00.000Z',
      booking_end: '2020-05-05T22:59:59.000Z',
    },
    workspaceSettingsGuid: workspace_guid,
    subRecTableName: 'job',
    plannerDataGuid,
    pagedMasterRecPlannerDataGuid,
    subRecPlannerDataGuid,
    bookingGroupsGuid,
    updatedMasterIds: [null],
    updatedSubIds: ['c5ceb77b-cace-47e4-a8a2-8bccaf24a728'],
  },
});

const batchPatchTableDataSuccessfulAction = {
  type: 'BATCH_PATCH_GROUPED_TABLE_DATA_SUCCESSFUL',
  payload: {
    response: true,
    tableDataGuid: pagedMasterRecPlannerDataGuid,
    tableName: 'booking',
    tableDataEntryGuids: ['tableDataEntryGuid1', 'tableDataEntryGuid2'],
    tableData: [
      {
        booking_resource_guid: 'testResourceGuid1',
        booking_job_guid: 'testJobGuid1',
        booking_start: '2020-04-28T23:00:00.000Z',
        booking_end: '2020-05-05T22:59:59.000Z',
      },
      {
        booking_resource_guid: 'testResourceGuid2',
        booking_job_guid: 'testJobGuid2',
        booking_start: '2020-04-28T23:00:00.000Z',
        booking_end: '2020-05-05T22:59:59.000Z',
      },
    ],
    workspaceSettingsGuid: workspace_guid,
    subRecTableName: 'job',
    plannerDataGuid,
    pagedMasterRecPlannerDataGuid,
    subRecPlannerDataGuid,
    bookingGroupsGuid,
  },
};
const bookingInsertGetDerivedActionDataFnAction = {
  ...batchPatchTableDataSuccessfulAction,
  workspaceSettings: {
    workspaceSettingsGuid: 'd11bb1a7-f353-4853-b31b-5fd74024ca4b',
    masterRecTableName: 'resource',
    subRecTableName: 'job',
    plannerDataGuid: 'planner_data_default',
    pagedMasterRecPlannerDataGuid: 'pagedMasterRecPlannerData_default',
    subRecPlannerDataGuid: 'subRecPlannerData_default',
    barGroupsGuids: {
      bookingGroupsGuid: `bookingGroups_default`,
      roleGroupsGuid: `roleGroups_default`,
    },
    barTableNamesGroupKeys: {
      booking: 'bookingGroupsGuid',
      rolerequest: 'roleGroupsGuid',
    },
  },
};

const updateRolerequestStatusesAction$ = ActionsObservable.of({
  type: 'BATCH_UPDATE_PLANNER_ROLEREQUEST_STATUS',
  payload: {
    guids: ['testGuid'],
    tableName: 'rolerequest',
    status: 'Requested',
  },
});

const alias = 'alias';

describe('createLoadPlannerDataEpic Epic', () => {
  const ajax = { apis: apisMock };
  const expectedOutputActions = [
    'LOAD_PAGED_ACCESS_DATA_SUCCESSFUL_alias',
    'LOAD_TABLE_DATA_SUCCESSFUL_plannerSubRec',
    'PAGED_DATA_REGISTER_KEEP_ALIVE_alias',
    'HANDLE_LOADED_USER_ENTITY_ACCESS',
    'PLANNER_DATA_LOADED',
    'DIGEST_PLANNER_DATA_LOADED',
  ];

  it('dispatches the correct actions when loading is successful', (done) => {
    const epic = createLoadPlannerDataEpic(alias);
    epic(createLoadPagedResultsDataAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('dispatches the correct actions when sorting is successful', (done) => {
    const epic = createLoadPlannerDataEpic(alias);
    epic(createSortPagedResultDataAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

describe('createScrollPlannerDataEpic', () => {
  it('dispatches the correct actions when successful', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'LOAD_PAGED_RESULTS_DATE_SUCCESSFUL_alias',
      'LOAD_MORE_TABLE_DATA_SUCCESSFUL_plannerSubRec',
      'PLANNER_DATA_SCROLLED',
    ];

    const epic = createLoadPagePlannerDataEpic(
      alias,
      actionTypes.SCROLL_PLANNER_DATA,
      plannerDataScrolled
    );
    epic(createScrollPagedResultDataAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

describe('createRefreshPlannerDataEpic', () => {
  it('dispatches the correct actions when successful', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'PATCH_MULTIPLE_PAGED_DATA_SUCCESSFUL_plannerMasterRecPaged',
      'LOAD_TABLE_DATA_SUCCESSFUL_plannerSubRec',
      'PLANNER_DATA_LOADED',
      'REFRESH_SENSITIVE_INFO',
      'HANDLE_LOADED_USER_ENTITY_ACCESS',
      'DIGEST_PLANNER_DATA_LOADED',
      'LOAD_TABLE_DATA_SUCCESSFUL_plannerSubRec',
      'LOAD_MORE_TABLE_DATA_SUCCESSFUL_plannerPage_plannerTableDatas',
      'BATCH_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL_plannerRolerequestResources',
      'BATCH_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL_plannerRolerequests',
      'BATCH_DELETE_GROUPED_TABLE_DATA_SUCCESSFUL_plannerBookingsGroups',
      'PATCH_MULTIPLE_PAGED_DATA_SUCCESSFUL_plannerMasterRecPaged',
      'PATCH_MULTIPLE_GROUPPED_TABLE_DATA_SUCCESSFUL_plannerBookingsGroups',
      'PATCH_MULTIPLE_GROUPPED_TABLE_DATA_SUCCESSFUL_plannerRolerequests',
      'PATCH_MULTIPLE_GROUPPED_TABLE_DATA_SUCCESSFUL_plannerRolerequestResources',
    ];

    const epic = createRefreshPlannerDataEpic();
    epic(createRefreshPagedResultDataAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

describe('createUpdatePlannerDataEpic', () => {
  it('dispatches the correct actions when successful', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'PATCH_MULTIPLE_PAGED_DATA_SUCCESSFUL_plannerMasterRecPaged',
      'PATCH_MULTIPLE_TABLE_DATA_SUCCESSFUL_plannerSubRec',
      'PLANNER_DATA_PATCH',
      'LOAD_MORE_TABLE_DATA_SUCCESSFUL_plannerPage_plannerTableDatas',
    ];

    const epic = createUpdatePlannerDataEpic(alias);
    epic(createUpdatePagedResultDataAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('does not dispatch actions when data is not loaded', (done) => {
    const ajax = { apis: apisMock };
    const loadingState = {
      ...amendedState,
      value: {
        ...amendedState.value,
        plannerPage: {
          ...amendedState.value.plannerPage,
          plannerData: {
            ...amendedState.value.plannerPage.plannerData,
            [plannerDataGuid]: {
              ...amendedState.value.plannerPage.plannerData[plannerDataGuid],
              loading: true,
            },
          },
        },
      },
    };
    const expectedOutputActions = [];

    const epic = createUpdatePlannerDataEpic(alias);
    epic(createUpdatePagedResultDataAction$, loadingState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

const createUpdatePlannerDateSensitiveFieldsAction$ = ActionsObservable.of({
  type: 'REFRESH_SENSITIVE_INFO',
  payload: { workspaceGuid: workspace_guid },
});

describe('updatePlannerDateSensitiveFieldsEpic', () => {
  it('calls the correct action creator', (done) => {
    const spy = jest
      .spyOn(
        plannerQuerySelectionUtils,
        'getLoadPlannerDataDateSensitiveActions'
      )
      .mockImplementation(() => {
        return [];
      });

    const epic = updatePlannerDateSensitiveFieldsEpic;
    epic(createUpdatePlannerDateSensitiveFieldsAction$, amendedState)
      .toArray()
      .subscribe((actualOutputActions) => {
        expect(spy).toHaveBeenCalled();
        spy.mockRestore();
        done();
      });
  });
});

const createPlannerPageFieldsChangedInterceptorAction$ = ActionsObservable.of({
  type: 'PLANNER_PAGE_FIELDS_CHANGED_SUCCESS',
});

describe('createPlannerPageFieldsChangedInterceptor', () => {
  it('calls BUILD_BAR_ADDITIONAL_DATA', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = ['BUILD_BAR_ADDITIONAL_DATA'];

    const epic = createPlannerPageFieldsChangedInterceptor();
    epic(createPlannerPageFieldsChangedInterceptorAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

const createDetailsPaneCloseReloadPlannerAction$ = ActionsObservable.of({
  type: 'RELOAD_PLANNER_DATA',
});

const createDetailsPaneCloseApplyFilterAction$ = ActionsObservable.of({
  type: 'APPLY_FILTER_plannerFilters',
  payload: {},
});

const createDetailsPaneCloseClearFilterAction$ = ActionsObservable.of({
  type: 'FILTER_CLEAR_plannerFilters',
  payload: {},
});

const createViewSettingsChangedAction$ = ActionsObservable.of({
  type: 'VIEW_SETTINGS_CHANGED',
  payload: {},
});

describe('createClearPlannerSelectEditsEpic', () => {
  it('resets planner detailsPane state on filter apply', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'ENTITY_WINDOW_CLOSE_detailsPane',
      'SELECT_EDITS_plannerBookingsGroups',
      'SELECT_EDITS_plannerMasterRecPaged',
      'SELECT_EDITS_plannerSubRec',
      'SET_DETAILS_PANE_COLLAPSED_',
      'SET_DETAILS_PANE_VISIBILITY_',
      'CLEAR_PLANNER_SELECTION',
    ];

    const epic = createClearPlannerSelectEditsEpic();
    epic(createDetailsPaneCloseApplyFilterAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('resets planner detailsPane state on filter clear', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'ENTITY_WINDOW_CLOSE_detailsPane',
      'SELECT_EDITS_plannerBookingsGroups',
      'SELECT_EDITS_plannerMasterRecPaged',
      'SELECT_EDITS_plannerSubRec',
      'SET_DETAILS_PANE_COLLAPSED_',
      'SET_DETAILS_PANE_VISIBILITY_',
      'CLEAR_PLANNER_SELECTION',
    ];

    const epic = createClearPlannerSelectEditsEpic();
    epic(createDetailsPaneCloseClearFilterAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('persist planner selected entities on view settings changed', (done) => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [
      'ENTITY_WINDOW_CLOSE_detailsPane',
      'PERSIST_SELECTED_EDITS_plannerBookingsGroups',
      'PERSIST_SELECTED_EDITS_plannerMasterRecPaged',
      'PERSIST_SELECTED_EDITS_plannerSubRec',
      'CLEAR_PLANNER_SELECTION',
    ];
    const epic = createViewSettingsChangeEpic();

    epic(createViewSettingsChangedAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

const createCommonGroupedBatchInterceptorAction = (type, response) =>
  ActionsObservable.of({
    type,
    payload: {
      response,
      tableDataEntryGuids: ['id1', 'id2'],
      tableData: [
        { prop1: 'smth', prop2: 3 },
        { prop1: 'smth123', prop2: 1 },
      ],
    },
  });

describe('commonDataBatchInterceptorEpic', () => {
  it('intercepts and handles successful responses correctly', (done) => {
    const actionsOfInterest = ['ACTION1', 'ACTION2'];
    const successHandler = () => {
      return [{ type: 'SUCCESS' }];
    };
    const props = {
      actionsOfInterest,
      successHandler,
      requestOperation: 'OPERATION',
    };

    const action = createCommonGroupedBatchInterceptorAction('ACTION1', [
      {
        type: SUCCESS_STATUS,
        result: 'smth',
      },
      {
        type: SUCCESS_STATUS,
        result: 'else',
      },
    ]);

    const ajax = { apis: apisMock };
    const epic = commonDataBatchInterceptorEpic(alias, props);
    const expectedOutputActions = ['SUCCESS'];
    epic(action, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('intercepts and handles both successful and error responses correctly', (done) => {
    const actionsOfInterest = ['ACTION1', 'ACTION2'];
    const successHandler = () => {
      return [{ type: 'SUCCESS' }];
    };
    const props = {
      actionsOfInterest,
      successHandler,
      requestOperation: 'OPERATION',
    };
    const action = createCommonGroupedBatchInterceptorAction('ACTION2', [
      {
        type: SUCCESS_STATUS,
        result: 'smth',
      },
      {
        type: ERROR_STATUS,
        result: 'else',
      },
    ]);

    const ajax = { apis: apisMock };
    const epic = commonDataBatchInterceptorEpic(alias, props);
    const expectedOutputActions = ['SUCCESS', 'DISPATCH_OR_PROMPT'];
    epic(action, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });

  it('intercepts and handles error responses correctly', (done) => {
    const actionsOfInterest = ['ACTION1', 'ACTION2'];
    const successHandler = () => {
      return [{ type: 'SUCCESS' }];
    };
    const props = {
      actionsOfInterest,
      successHandler,
      requestOperation: 'OPERATION',
    };
    const action = createCommonGroupedBatchInterceptorAction('ACTION1', [
      {
        type: ERROR_STATUS,
        result: 'smth',
      },
      {
        type: ERROR_STATUS,
        result: 'else',
      },
    ]);

    const ajax = { apis: apisMock };
    const epic = commonDataBatchInterceptorEpic(alias, props);
    const expectedOutputActions = ['DISPATCH_OR_PROMPT'];
    epic(action, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
        done();
      });
  });
});

describe('barInsertGetDerivedActionDataFn', () => {
  it('should return updated master/sub records ids', () => {
    const { updatedMasterIds, updatedSubIds } = barInsertGetDerivedActionDataFn(
      amendedState.value,
      bookingInsertGetDerivedActionDataFnAction
    );

    expect(updatedMasterIds).toContain('testResourceGuid1');
    expect(updatedMasterIds).toContain('testResourceGuid2');
    expect(updatedSubIds).toContain('testJobGuid1');
    expect(updatedSubIds).toContain('testJobGuid2');
  });
});

describe('batchUpdateRoleRequestsStatusEpic tests', () => {
  it('should call batchUpdateRoleRequestsStatusEpic with proper args', () => {
    const ajax = { apis: apisMock };
    const expectedOutputActions = [actionTypes.PATCH_GROUPPED_TABLE_DATA];

    const epic = batchUpdateRoleRequestsStatusEpic$;

    epic(updateRolerequestStatusesAction$, amendedState, ajax)
      .toArray()
      .subscribe((actualOutputActions) => {
        expectedOutputActions.forEach((expectedAction) => {
          expect(
            outputActionsContainsType(actualOutputActions, expectedAction)
          ).toBe(true);
        });
      });
  });
});

describe('entityWindowJobInsertSuccessInterceptorEpic', () => {
  const {
    ENTITY_WINDOW_MODULES,
    TABLE_NAMES,
    FEATURE_FLAGS,
  } = require('../../../src/constants/globalConsts');
  const { LIST_PAGE_ALIAS } = require('../../../src/constants/listPageConsts');
  const { JOBS_PAGE_ALIAS } = require('../../../src/constants/jobsPageConsts');

  it('should dispatch updateListView when feature flag is enabled and not on Job view', () => {
    const action = {
      type: `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_job`,
      payload: {
        moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
        stopEditing: true,
      },
    };

    const mockState = {
      navigation: { page: LIST_PAGE_ALIAS },
      listPage: { activeListView: TABLE_NAMES.RESOURCE },
      featureManagement: {
        featureFlags: {
          [FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE]: true,
        },
      },
    };

    const action$ = ActionsObservable.of(action);
    const state$ = { value: mockState };

    const epic = entityWindowJobInsertSuccessInterceptorEpic();

    epic(action$, state$)
      .toArray()
      .subscribe((outputActions) => {
        expect(outputActions.length).toBeGreaterThan(0);

        // Check that updateListView action is dispatched
        const updateListViewAction = outputActions.find(
          (action) =>
            action.type === 'UPDATE_LIST_VIEW' &&
            action.payload === TABLE_NAMES.JOB
        );
        expect(updateListViewAction).toBeDefined();

        // Check that base actions are also dispatched
        const closeAction = outputActions.find(
          (action) =>
            action.type && action.type.indexOf('ENTITY_WINDOW_CLOSE') > -1
        );
        expect(closeAction).toBeDefined();
      });
  });

  it('should not dispatch updateListView when feature flag is disabled', () => {
    const action = {
      type: `${actionTypes.ENTITY_WINDOW.SUBMIT_INSERT_TABLE_DATA_SUCCESS}_job`,
      payload: {
        moduleName: ENTITY_WINDOW_MODULES.JOBS_PAGE_MODAL,
        stopEditing: true,
      },
    };

    const mockState = {
      navigation: { page: LIST_PAGE_ALIAS },
      listPage: { activeListView: TABLE_NAMES.RESOURCE },
      featureManagement: {
        featureFlags: {
          [FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE]: false,
        },
      },
    };

    const action$ = ActionsObservable.of(action);
    const state$ = { value: mockState };

    const epic = entityWindowJobInsertSuccessInterceptorEpic();

    epic(action$, state$)
      .toArray()
      .subscribe((outputActions) => {
        // Check that updateListView action is NOT dispatched
        const updateListViewAction = outputActions.find(
          (action) => action.type === 'UPDATE_LIST_VIEW'
        );
        expect(updateListViewAction).toBeUndefined();
      });
  });
});
